package com.r2.bi.aop;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ ElementType.PARAMETER, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
public @interface OperateLogAnnotation {
    /**
     * 节点描述
     *
     * @return
     */
    String description() default "";

    /**
     * 节点名称
     *
     * @return
     */
    String model() default "";

    /**
     * 操作类型：
     *
     * @return
     */
    String operateType() default "";
}
