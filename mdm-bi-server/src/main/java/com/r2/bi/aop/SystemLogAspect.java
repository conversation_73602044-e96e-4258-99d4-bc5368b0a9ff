package com.r2.bi.aop;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.FilterProvider;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.r2.bi.vo.bi.centermember.BiCenterMemberReqVO;
import com.r2.framework.util.ResultCode;
import com.r2.bi.entity.bi.BiUser;
import com.r2.bi.entity.bi.SysLog;
import com.r2.bi.service.SysLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Aspect
@Component
@Slf4j
public class SystemLogAspect {

    private static final String DEFAULT_IP = "127.0.0.1";

    @Autowired(required=false)
    private HttpServletRequest request;

    @Pointcut("@annotation(com.r2.bi.aop.OperateLogAnnotation)")
    public void controllerAspect(){}

    @Resource
    private SysLogService sysLogService;

    /**
     * 后置操作 用户拦截Controller层用户操作
     *
     * @param joinPoint
     */
    @AfterReturning(value = "controllerAspect()", returning = "returnValue")
    public void doAfter(JoinPoint joinPoint, Object returnValue) {
        // 获取当前执行的方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 检查是否为需要特殊处理的方法
        boolean isSpecialMethod = "queryByPageCommon".equals(method.getName()) &&
                "com.r2.bi.controller.InfoCenterController".equals(method.getDeclaringClass().getName());

        // 请求参数
        Object[] args = joinPoint.getArgs();

        if (isSpecialMethod) {
            // 对于特殊处理的方法，检查password字段是否不为空
            boolean shouldLog = checkPasswordField(args);
            if (!shouldLog) {
                return; // 如果不应该记录日志，则直接返回
            }
        }
        //请求ip
        String remoteAddr = getIpAddr(request);
        //请求uri
        String requestUri = request.getRequestURI();
        //请求方法
        String methodType = request.getMethod();
        //请求参数
        Map<String, String[]> params = request.getParameterMap();
        //切点描述
        Map<String, String> methodDescription = getMethodDescription(joinPoint);

        SysLog operateLog = new SysLog();
        operateLog.setModuleName(methodDescription.get("name"));
        operateLog.setOperateType(methodDescription.get("operateType"));
        operateLog.setMethod(methodType);
        operateLog.setRequestIp(remoteAddr);
        operateLog.setRequestUri(requestUri);
        operateLog.setParams(buildRequestParams(params, joinPoint.getArgs()));
        operateLog.setCreateTime(LocalDateTime.now());
        //操作人
        BiUser sysUser = (BiUser) SecurityUtils.getSubject().getPrincipal();
        if (ObjectUtil.isNotEmpty(sysUser)) {
            operateLog.setUsername(sysUser.getUsername());
        }
        //返回结果
        if (ObjectUtil.isNotEmpty(returnValue)) {
            JSON valueJson = JSONUtil.parse(returnValue);
            operateLog.setExceptionDetail(valueJson.toString());
            Integer code = (Integer) valueJson.getByPath("code");
            if (!ResultCode.Codes.SUCCESS.getCode().equals(code)) {
                operateLog.setOperateStatus(2);
            } else {
                operateLog.setOperateStatus(1);
            }
        }
        //保存
        sysLogService.save(operateLog);

    }
    /**
     * 检查参数中是否包含password字段且其值不为空
     */
    private boolean checkPasswordField(Object[] args) {
        for (Object arg : args) {
            if (arg instanceof BiCenterMemberReqVO) { // 假设BiCenterMemberReqVO是你的请求对象类型
                BiCenterMemberReqVO reqVO = (BiCenterMemberReqVO) arg;
                if (reqVO.getPassword() != null && !reqVO.getPassword().trim().isEmpty()) {
                    return true;
                }
            }
        }
        return false;
    }
    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param joinPoint 切点
     * @return 方法描述
     */
    public static Map<String, String> getMethodDescription(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        OperateLogAnnotation controllerLog = method
                .getAnnotation(OperateLogAnnotation.class);
        String description = controllerLog.description();
        String modelName = controllerLog.model();
        String operateType = controllerLog.operateType();

        HashMap<String, String> descMap = new HashMap<>();
        descMap.put("desc", description);
        descMap.put("name", modelName);
        descMap.put("operateType", operateType);
        return descMap;
    }

    /**
     * 请求参数转化
     * @param paramMap
     * @param args
     * @return
     */
    private String buildRequestParams(Map<String, String[]> paramMap, Object[] args) {
        StringBuilder params = new StringBuilder();

        if (CollectionUtils.isEmpty(paramMap)) {
            for (Object obj : args) {
                // 将对象转换为JSON对象
                JSONObject jsonObject = JSONUtil.parseObj(obj);

                // 移除password字段
                if(jsonObject.containsKey("password")) {
                    jsonObject.remove("password");
                }

                // 追加到params中
                params.append(jsonObject.toString());
            }
        }
        return params.toString();
    }

    /**
     * 列表数组转换为json字符串,忽略空值
     *
     * @param obj
     * @return
     */
    private String obj2jsonIgnoreNull(Object obj) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("对象转json错误:" + e.getMessage(), e);
        }
    }

    /**
     * 获取ip地址
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (Strings.isBlank(ip) || ip.equals(DEFAULT_IP)) {
            ip = request.getHeader("X-Real-IP");
        }else {
            ip = ip.split(",")[0];
        }
        if(ip ==null || ip.length() ==0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if(ip ==null || ip.length() ==0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if(ip ==null || ip.length() ==0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip == null ? DEFAULT_IP : ip;
    }
}
