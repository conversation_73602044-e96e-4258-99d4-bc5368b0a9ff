package com.r2.bi.dao.bi;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.r2.bi.bo.SysUserListBO;
import com.r2.bi.entity.bi.BiUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;



/**
 * 人员(SysUser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-10-21 10:04:10
 */
@Mapper
public interface BiSysUserDao extends BaseMapper<BiUser> {

    Page<SysUserListBO> findPage(@Param("page") Page<BiUser> page, @Param(Constants.WRAPPER) QueryWrapper<BiUser> qw);


}

