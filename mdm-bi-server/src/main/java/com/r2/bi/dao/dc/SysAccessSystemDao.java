package com.r2.bi.dao.dc;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.r2.bi.entity.dc.SysAccessSystem;
import com.r2.bi.vo.sysaccesssystem.AccessListReqVO;
import com.r2.bi.vo.sysaccesssystem.AccessListRespVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限访问控制管理表(SysAccessSystem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-27 10:40:14
 */
@DS("mdm")
public interface SysAccessSystemDao extends BaseMapper<SysAccessSystem> {

    /**
     * 数量
     * @param query
     * @return
     */
    Integer totalCount(@Param("query") AccessListReqVO query);

    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<AccessListRespVO> listByCond(@Param("query") AccessListReqVO query);

}

