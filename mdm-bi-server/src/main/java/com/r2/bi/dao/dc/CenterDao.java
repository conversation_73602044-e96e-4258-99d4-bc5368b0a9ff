package com.r2.bi.dao.dc;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.r2.bi.entity.dc.Center;
import com.r2.bi.vo.CenterListRespVO;
import com.r2.bi.vo.TableConfigListRespVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 中心基本信息(Center)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-09 10:17:13
 */
@DS("mdm")
public interface CenterDao extends BaseMapper<Center> {
    @Select({
            "<script>" +
            "SELECT COLUMN_NAME as columnName,COLUMN_COMMENT as columnComment,CHARACTER_MAXIMUM_LENGTH as maxLength " +
            "FROM INFORMATION_SCHEMA.COLUMNS " +
            "WHERE TABLE_SCHEMA = '${db}' and TABLE_NAME = 'center' " +
            "</script>"
    })
    List<TableConfigListRespVO> getTableConfig(@Param("db") String db);

    @Select({
            "<script>" +
            "INSERT INTO ${db}.center(${columnStr}) " +
            "VALUES(${valueStr}) " +
            "</script>"
    })
    Long insertBySql(@Param("db") String db, @Param("columnStr") String columnStr, @Param("valueStr") String valueStr);

    @Select({
            "<script>" +
            "UPDATE ${db}.center SET ${updateStr} " +
            "WHERE id = ${id} " +
            "</script>"
    })
    Long updateBySql(@Param("db") String db, @Param("id") Long id, @Param("updateStr") String updateStr);

    @Select({
            "<script>" +
            "SELECT id,center_name AS name FROM ${db}.center " +
            "WHERE is_delete = 0 " +
            "<if test='\"${querySql}\" != null and \"${querySql}\" != \"\"'> ${querySql} </if>" +
            "ORDER BY dateCreated ASC " +
            "<if test='\"${limitSql}\" != null and \"${limitSql}\" != \"\"'> ${limitSql} </if>" +
            "</script>"
    })
    List<CenterListRespVO> simpleList(@Param("db") String db, @Param("querySql") String querySql, @Param("limitSql") String limitSql);
    //List<Map<String, Object>> simpleList(@Param("db") String db, @Param("querySql") String querySql, @Param("limitSql") String limitSql);

    @Select({
            "<script>" +
            "SELECT count(*) FROM ${db}.center " +
            "WHERE is_delete = 0 " +
            "<if test='\"${querySql}\" != null and \"${querySql}\" != \"\"'> ${querySql} </if>" +
            "</script>"
    })
    Integer totalCount(@Param("db") String db, @Param("querySql") String querySql);

    @Select({
            "<script>" +
            "SELECT * FROM ${db}.center " +
            "WHERE is_delete = 0 AND id = ${id}" +
            "</script>"
    })
    Map<String, Object> detail(@Param("db") String db, @Param("id") Long id);

    @Select({
            "<script>" +
            "SELECT * FROM ${db}.center " +
            "WHERE is_delete = 0 " +
            "<if test='\"${querySql}\" != null and \"${querySql}\" != \"\"'> ${querySql} </if>" +
            "<if test='\"${orderSql}\" != null and \"${orderSql}\" != \"\"'> ${orderSql} </if>" +
            "<if test='\"${limitSql}\" != null and \"${limitSql}\" != \"\"'> ${limitSql} </if>" +
            "</script>"
    })
    List<Map<String, Object>> getList(@Param("db") String db, @Param("querySql") String querySql, @Param("limitSql") String limitSql, @Param("orderSql") String orderSql);

    @Select({
            "<script>" +
            "SELECT MAX(standard_code) FROM ${db}.center " +
            "WHERE is_delete = 0 AND standard_code like \"%${code}%\" " +
            "</script>"
    })
    String getMaxCode(@Param("db") String db, @Param("code") String code);



}

