package com.r2.bi.dao.cs;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.r2.bi.entity.cs.CenterMeeting;
import com.r2.bi.entity.cs.CenterMeetingCycle;
import com.r2.bi.vo.centermeeting.CenterMeetingListRespVO;
import com.r2.bi.vo.centermeeting.CenterMeetingQueryStrRespVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 日历(CenterMeeting)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-10 17:58:24
 */
@DS("cs")
public interface CenterMeetingDao extends BaseMapper<CenterMeeting> {


    /**
     * 根据中心id查询立项会会议列表
     * @param centerId
     * @return
     */
    List<CenterMeetingListRespVO> queryByInitialCenterId(Long centerId);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<CenterMeeting> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<CenterMeeting> entities);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<CenterMeetingCycle> 实例对象列表
     * @return 影响行数
     */
    int insertCycleBatch(@Param("entities") List<CenterMeetingCycle> entities);

    /**
     * 根据中心id查询伦理会会议列表
     * @param centerId
     * @return
     */
    List<CenterMeetingListRespVO> queryByEthicCenterId(Long centerId);

    /**
     * 根据中心id查询合同会会议列表
     * @param centerId
     * @return
     */
    List<CenterMeetingListRespVO> queryByContractCenterId(Long centerId);

    /**
     * 根据中心和类型查询立项会议表
     * @param centerId
     * @return
     */
    List<CenterMeetingQueryStrRespVO> queryInitialStrByCenterId(Long centerId);

    /**
     * 根据中心和类型查询伦理会议表
     * @param centerId
     * @return
     */
    List<CenterMeetingQueryStrRespVO> queryEthicsStrByCenterId(Long centerId);

    /**
     * 根据中心和类型查询合同会议表
     * @param centerId
     * @return
     */
    List<CenterMeetingQueryStrRespVO> queryContractStrByCenterId(Long centerId);

    /**
     * 根据截止日期查询日历数据
     *
     * @param startDate
     * @return
     */
    List<CenterMeetingQueryStrRespVO> queryByDateScope(String startDate);



    /**
     * 根据中心id修改周期删除状态
     * @param centerMeeting
     * @return
     */
    Integer disableCycles(CenterMeeting centerMeeting);


    @Select({
            "<script>",
            "SELECT m.* FROM center_meeting m",
            "WHERE m.meeting_date BETWEEN #{startDate} AND #{endDate}",
            "AND m.center_id IN",
            "<foreach item='id' index='index' collection='centerIds' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"
    })
    List<CenterMeetingQueryStrRespVO> queryByDateScopeAndCenters(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("centerIds") List<Integer> centerIds);

}
