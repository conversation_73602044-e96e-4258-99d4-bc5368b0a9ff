package com.r2.bi.dao.bi;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.r2.bi.entity.bi.SysLog;
import com.r2.bi.vo.syslog.SysLogListRespVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 系统日志(SysLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-10-21 10:04:10
 */
@Mapper
public interface SysLogDao extends BaseMapper<SysLog> {

    @Select({
            "<script>" +
            "SELECT " +
            "count(*) " +
            "FROM " +
            "sys_log sl " +
            "<if test='\"${querySql}\" != null and \"${querySql}\" != \"\"'> ${querySql} </if>" +
            "</script>"
    })
    Integer totalCount(@Param("querySql") String querySql);

    @Select({
            "<script>" +
            "SELECT " +
            "sl.id,sl.module_name,sl.operate_type,sl.operate_status,sl.method,sl.username,sl.request_ip,sl.operate_status,sl.create_time " +
            "FROM " +
            "sys_log sl " +
            "<if test='\"${querySql}\" != null and \"${querySql}\" != \"\"'> ${querySql} </if>" +
            "ORDER BY sl.create_time DESC " +
            "<if test='\"${limitSql}\" != null and \"${limitSql}\" != \"\"'> ${limitSql} </if>" +
            "</script>"
    })
    List<SysLogListRespVO> listByCond(@Param("querySql") String querySql, @Param("limitSql") String limitSql);
}

