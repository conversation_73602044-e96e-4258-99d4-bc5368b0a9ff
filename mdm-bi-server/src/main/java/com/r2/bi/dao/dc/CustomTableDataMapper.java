package com.r2.bi.dao.dc;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.r2.bi.entity.dc.CustomTableData;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【custom_table_data(自定义表格数据)】的数据库操作Mapper
* @createDate 2024-09-18 18:30:05
* @Entity com.r2.ssu.entity.CustomTableData
*/
@DS("mdm")
public interface CustomTableDataMapper extends BaseMapper<CustomTableData> {

    /**
     * 根据tableId查询数据
     * @param tableId
     * @return
     */
    @Select("select * from custom_table_data where table_id = #{tableId}")
    List<CustomTableData> selectByTableId(String tableId);

    @Insert("<script>" +
            "INSERT INTO custom_table_data (table_id, content, status, remark, create_by, create_time) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.tableId}, #{item.content}, #{item.status}, #{item.remark}, #{item.createBy}, #{item.createTime}) " +
            "</foreach>" +
            "</script>")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insertBatch(List<CustomTableData> tableDataList);

    @Delete("delete from custom_table_data where table_id = #{tableId}")
    void deleteByTableId(String tableId);
}




