package com.r2.bi.dao.dc;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.r2.bi.entity.bi.SysMenu;
import com.r2.bi.vo.sysmenu.SysMenuListRespVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统菜单(SysMenu)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-10-21 10:04:10
 */
@DS("mdm")
public interface SysMenuDao extends BaseMapper<SysMenu> {

    @Select("SELECT " +
            "m.permission " +
            "FROM " +
            "sys_menu m " +
            "LEFT JOIN sys_roles_menus rm ON rm.menu_id = m.id " +
            "LEFT JOIN sys_user_roles ur ON ur.role_id = rm.role_id " +
            "where ur.user_id=${userId} and m.`status`=1")
    List<String> getPermissionsByUserId(@Param("userId") Long userId);

    @Select({
            "<script>" +
            "SELECT " +
            "sm.id,sm.pid,sm.sub_count,sm.type,sm.title,sm.name,sm.menu_sort,sm.route,sm.status,sm.create_time " +
            "FROM " +
            "sys_menu sm " +
            "WHERE sm.is_delete = 0 " +
            "<if test='\"${querySql}\" != null and \"${querySql}\" != \"\"'> ${querySql} </if>" +
            "ORDER BY sm.menu_sort ASC " +
            "<if test='\"${limitSql}\" != null and \"${limitSql}\" != \"\"'> ${limitSql} </if>" +
            "</script>"
    })
    List<SysMenuListRespVO> getMenuList(@Param("querySql") String querySql, @Param("limitSql") String limitSql);

    @Select({
            "<script>" +
            "SELECT " +
            "count(*) " +
            "FROM " +
            "sys_menu sm " +
            "WHERE sm.is_delete = 0 " +
            "<if test='\"${querySql}\" != null and \"${querySql}\" != \"\"'> ${querySql} </if>" +
            "</script>"
    })
    Integer totalCount(@Param("querySql") String querySql);

    @Select({
            "<script>" +
            "UPDATE " +
            "sys_menu sm " +
            "SET sm.update_by = '${updateBy}', sm.status = ${status}, sm.update_time = '${updateTime}' " +
            "WHERE (sm.id = ${id} OR sm.pid = ${id}) " +
            "</script>"
    })
    Integer disable(@Param("id") Integer id, @Param("status") Integer status, @Param("updateBy") String updateBy, @Param("updateTime") LocalDateTime updateTime);

    @Select({
            "<script>" +
            "SELECT " +
            "rm.menu_id AS id " +
            "FROM sys_user_roles ur " +
            "JOIN sys_roles_menus rm ON ur.role_id = rm.role_id " +
            "JOIN sys_menu sm ON sm.id = rm.menu_id " +
            "WHERE ur.user_id = ${userId} AND sm.status = 1 AND sm.is_delete = 0 " +
            "</script>"
    })
    List<SysMenuListRespVO> getUserMenuList(@Param("userId") Long userId);

    @Select({
            "<script>" +
            "select * from t_sys_menu" +
            "</script>"
    })
    List<SysMenuListRespVO> selectByGroupHaving();
}

