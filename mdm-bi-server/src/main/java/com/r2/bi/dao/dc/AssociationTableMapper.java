package com.r2.bi.dao.dc;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.r2.bi.entity.dc.AssociationTable;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【association_table(数据映射关联表)】的数据库操作Mapper
* @createDate 2024-10-28 15:45:37
* @Entity generator.domain.AssociationTable
*/
@DS("mdm")
public interface AssociationTableMapper extends BaseMapper<AssociationTable> {


    @Insert("<script>" +
            "INSERT INTO association_table (system_source, type, third_party_id, third_party_center_name,third_party_address,province_and_city,delete_flag, mdm_id, mdm_center_code, mdm_center_name, mdm_center_alias, mdm_address," +
            "third_party_indication_id,third_party_indication,mdm_indication_id,mdm_indication_zh,mdm_indication_en,mdm_indication_abbr, is_delete, create_time, update_time) " +
            "VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.systemSource}, #{item.type}, #{item.thirdPartyId}, #{item.thirdPartyCenterName},#{item.thirdPartyAddress}, #{item.provinceAndCity},#{item.deleteFlag}," +
            "#{item.mdmId}, #{item.mdmCenterCode}, #{item.mdmCenterName}, #{item.mdmCenterAlias}, #{item.mdmAddress}," +
            " #{item.thirdPartyIndicationId},#{item.thirdPartyIndication},#{item.mdmIndicationId},#{item.mdmIndicationZh},#{item.mdmIndicationEn},#{item.mdmIndicationAbbr}, #{item.isDelete}, #{item.createTime}, #{item.updateTime})" +
            "</foreach>" +
            "</script>")
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insertBatch(@Param("list") List<AssociationTable> list);

    @Delete("delete from association_table where system_source = #{systemSource}")
    void deleteBySystemSource(@Param("systemSource") String systemSource);
}




