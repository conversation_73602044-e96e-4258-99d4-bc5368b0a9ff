package com.r2.bi.dao.cs;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.r2.bi.entity.cs.CenterMember;
import com.r2.bi.vo.bi.centermember.BiCenterMemberReqVO;
import com.r2.bi.vo.bi.centermember.BiCenterMemberRespVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 中心人员(CenterMember)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-10 20:31:41
 */
@DS("cs")
public interface BiCenterMemberDao extends BaseMapper<CenterMember> {

    /**
     * 查询指定行数据
     *
     * @return 对象列表
     */
    List<BiCenterMemberRespVO> listByCond(@Param("query") BiCenterMemberReqVO query, @Param("start") int start, @Param("size") int size);

    Integer totalCount(@Param("query") BiCenterMemberReqVO query);
}

