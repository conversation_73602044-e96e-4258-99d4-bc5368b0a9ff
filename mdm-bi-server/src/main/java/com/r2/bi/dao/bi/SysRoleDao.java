package com.r2.bi.dao.bi;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.r2.bi.entity.bi.SysRole;
import com.r2.bi.vo.sysrole.SysRoleListRespVO;
import com.r2.bi.vo.sysrole.SysRoleSimpleListRespVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * 角色表(SysRole)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-10-21 09:55:46
 */
@Mapper
public interface SysRoleDao extends BaseMapper<SysRole> {

    @Select({
            "<script>" +
                    "SELECT " +
                    "count(*) " +
                    "FROM " +
                    "t_role sr " +
                    "WHERE sr.is_delete = 0 " +
                    "<if test='\"${querySql}\" != null and \"${querySql}\" != \"\"'> ${querySql} </if>" +
                    "</script>"
    })
    Integer totalCount(@Param("querySql") String querySql);
    @Select({
            "<script>" +
                    "SELECT " +
                    "sr.id,sr.name,sr.code " +
                    "FROM " +
                    "t_role sr " +
                    "WHERE sr.is_delete = 0 " +
                    "<if test='\"${querySql}\" != null and \"${querySql}\" != \"\"'> ${querySql} </if>" +
                    "ORDER BY sr.id ASC " +
                    "<if test='\"${limitSql}\" != null and \"${limitSql}\" != \"\"'> ${limitSql} </if>" +
                    "</script>"
    })
    List<SysRoleSimpleListRespVO> simpleListByCond(@Param("querySql") String querySql, @Param("limitSql") String limitSql);


    @Select({
            "<script>" +
                    "SELECT " +
                    "sr.id,sr.name,sr.code,sr.data_scope,sr.description,sr.status,sr.create_time " +
                    "FROM " +
                    "t_role sr " +
                    "WHERE sr.is_delete = 0 " +
                    "<if test='\"${querySql}\" != null and \"${querySql}\" != \"\"'> ${querySql} </if>" +
                    "ORDER BY sr.create_time DESC " +
                    "<if test='\"${limitSql}\" != null and \"${limitSql}\" != \"\"'> ${limitSql} </if>" +
                    "</script>"
    })
    List<SysRoleListRespVO> listByCond(@Param("querySql") String querySql, @Param("limitSql") String limitSql);

}

