package com.r2.bi.dao.bi;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.r2.bi.entity.bi.SysOrganize;
import com.r2.bi.vo.sysorg.SysOrgListReqVO;
import com.r2.bi.vo.sysorg.SysOrgTreeNodeRespVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 组织部门(SysOrganize)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-02 10:41:19
 */
@Mapper
public interface SysOrganizeDao extends BaseMapper<SysOrganize> {



    /**
     * 查询组织管理数据,返回树形结构需要的实体类
     *
     * @param org 组织信息
     * @return 组织信息集合
     */
    List<SysOrgTreeNodeRespVO> selectTreeOrgList(SysOrgListReqVO org);

    /**
     * 查询组织管理数据
     *
     * @param org 组织信息
     * @return 组织信息集合
     */
    List<SysOrganize> selectOrgList(SysOrgListReqVO org);

}

