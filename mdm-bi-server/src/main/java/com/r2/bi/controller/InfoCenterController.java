package com.r2.bi.controller;

import com.r2.bi.aop.OperateLogAnnotation;
import com.r2.bi.service.BiCenterService;

import com.r2.bi.vo.BiCenterRepVO;
import com.r2.bi.vo.bi.BiCalendarReqVO;
import com.r2.bi.vo.bi.BiCenterDetailsRepVO;
import com.r2.bi.vo.bi.BiRelateReportReqVO;
import com.r2.bi.vo.bi.center.*;
import com.r2.bi.vo.bi.centermember.BiCenterMemberReqVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@RequestMapping("/info/")
@RestController
public class InfoCenterController {


    @Resource
    private BiCenterService biCenterService;


    /**
     * 查询全国数据
     */
    @PostMapping("centerCount")
    @OperateLogAnnotation(model="中心管理", operateType = "查询全国数据")
    public Object centerCount(@RequestBody AreaListReqVO areaListVO) {
        return biCenterService.centerCountList(areaListVO);
    }

    /**
     * 获取所有的区域、省市
     */
    @PostMapping("structure")
    public Object structure() {
        return biCenterService.getAggregatedDataAsArray();
    }


    /**
     * 中心列表
     * @return
     */
    @PostMapping("ssuCenterList")
    @OperateLogAnnotation(model="中心画像", operateType = "查询中心列表")
    public Object ssuCenterList(@RequestBody BiCenterRepVO repVO) {
        return biCenterService.ssuCenterList(repVO);
    }

    /**
     * 中心列表详情
     * @return
     */
    @PostMapping("centerDetails")
    @OperateLogAnnotation(model="中心画像", operateType = "查询中心详情")
    public Object centerDetails(@RequestBody BiCenterDetailsRepVO repVO) {
        return biCenterService.centerDetails(repVO);
    }


    /**
     * 根据中心和时间段返回日历列表
     *
     * @param listReqVO 查询实体
     * @return 删除是否成功
     */
    @PostMapping("infoCalendar")
    public Object infoCalendarByScope(@RequestBody @Valid BiCalendarReqVO listReqVO) {
        return biCenterService.infoCalendarByScope(listReqVO);
    }

    /**
     * 单个中心信息完整度
     * @return
     */
    @PostMapping("centerIntegrity")
    public Object centerIntegrity(@RequestBody BiRelateReportReqVO req){
        return biCenterService.centerIntegrity(req);
    }

    /**
     * 单个中心信息完整度导出
     * @return
     */
    @PostMapping("exportToExcel")
    public Object exportToExcel(@RequestBody BiRelateReportReqVO req, HttpServletResponse response){
        return biCenterService.exportToExcel(req,response);
    }

    /**
     * 启动速度
     * @return
     */
    @PostMapping("startSpeed")
    @OperateLogAnnotation(model="中心画像-中心列表详情", operateType = "启动速度")
    public Object startSpeed(@RequestBody BiStartSpeedVO speedVO){
        return biCenterService.startSpeed(speedVO);
    }

    /**
     * 启动速度详情
     * @return
     */
    @PostMapping("startSpeedDetail")
    public Object startSpeedDetail(@RequestBody InfoStartSpeedDetailVO detailVO){
        return biCenterService.startSpeedDetail(detailVO);
    }

    /**
     * 中心通讯录
     * @param
     * @return 查询结果
     */
    @PostMapping("commonList")
    @OperateLogAnnotation(model="中心画像-中心通讯录", operateType = "查询中心通讯录号码")
    public Object queryByPageCommon(@RequestBody @Valid BiCenterMemberReqVO reqVO) {
        return biCenterService.queryByPage(reqVO);
    }

    /**
     * 权限数据状态修改
     * @param vo
     * @return
     */
    @PostMapping("updateStatus")
    public Object updateStatus(@RequestBody @Valid SysUserAuthorityReqVO vo) {
        return biCenterService.updateStatus(vo);
    }



}
