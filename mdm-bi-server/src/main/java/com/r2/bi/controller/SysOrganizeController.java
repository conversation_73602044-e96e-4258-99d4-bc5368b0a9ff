package com.r2.bi.controller;

import com.r2.bi.service.SysOrganizeService;
import com.r2.bi.vo.sysorg.SysOrgListReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 组织部门(SysOrganize)表控制层
 *
 * <AUTHOR>
 * @since 2022-11-02 10:41:18
 */
@RestController
@RequestMapping("/sysOrganize/")
@Slf4j
public class SysOrganizeController {
    /**
     * 服务对象
     */
    @Resource
    private SysOrganizeService sysOrganizeService;



    /**
     * 查询组织列表
     *
     * @param reqVO 筛选条件
     * @return 查询结果
     */
    @PostMapping("list")
    public Object selectOrgList(@RequestBody @Valid SysOrgListReqVO reqVO) {
        return sysOrganizeService.selectOrgList(reqVO);
    }

    /**
     * 获取组织下拉树列表
     */
    @GetMapping("getOrgCodeList")
    public Object getOrgCodeList(@RequestParam(value = "code", required = false) String code) {
        return sysOrganizeService.getOrgCodeList(code);
    }


}

