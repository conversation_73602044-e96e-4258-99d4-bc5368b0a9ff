package com.r2.bi.controller;

import com.r2.bi.service.SysMenuService;
import com.r2.bi.vo.sysmenu.SysMenuListReqVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RequestMapping("menu")
@RestController
public class MenuController {

    @Resource
    private SysMenuService sysMenuService;



    /**
     * 当前用户菜单权限列表
     *
     * @param
     * @return 删除结果
     */
    @GetMapping("currentUserMenuList")
    public Object userMenuList() {
        return sysMenuService.currentUserMenuList();
    }


    /**
     * 菜单列表
     *
     * @param reqVO
     * @return 查询结果
     */
    @PostMapping("list")
    public Object queryByPage(@RequestBody @Valid SysMenuListReqVO reqVO) {
        return sysMenuService.queryByPage(reqVO);
    }

}
