package com.r2.bi.controller;

import com.r2.bi.aop.OperateLogAnnotation;
import com.r2.bi.service.SysUserService;
import com.r2.bi.vo.BaseListReqVO;
import com.r2.bi.vo.sysUser.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/user/")
public class SysUserController {

    @Resource
    private SysUserService sysUserService;

    /**
     * 获取用户信息
     * @return
     */
    @RequestMapping("info")
    public Object getUserInfo() {
        return sysUserService.getUserInfo();
    }

    /**
     * 获取用户信息详情
     * @return
     */
    @GetMapping("infoDetail")
    public Object getUserInfoDetail() {
        return sysUserService.getUserInfoDetail();
    }

    /**
     * 用户列表
     * @param vo
     * @return
     */
    @PostMapping("list")
    //@RequiresPermissions("user:list")
    public Object list(@RequestBody SysUserListReqVO vo) {
        return sysUserService.list(vo);
    }

    /**
     * 下拉职位列表
     * @param vo
     * @return
     */
    @PostMapping("dropDutyNameList")
    public Object dropDutyNameList(@RequestBody BaseListReqVO vo) {
        return sysUserService.dropDutyNameList(vo);
    }

    /**
     * 下拉列表-直属领导
     * @return
     */
    @PostMapping("dropLeaders")
    public Object dropLeaders(@RequestBody  BaseListReqVO vo) {
        return sysUserService.dropLeaders(vo);
    }

    /**
     * 获取详情
     * @param id
     * @return
     */
    @GetMapping("detail")
    //@RequiresPermissions("user:detail")
    public Object detail(@RequestParam("id") Long id) {
        return sysUserService.detail(id);
    }


    /**
     * 编辑
     * @param vo
     * @return
     */
    @PostMapping("edit")
    //@RequiresPermissions("user:edit")
    @OperateLogAnnotation(model="系统用户", operateType = "新增/编辑")
    public Object edit(@RequestBody List<SysUserEditReqVO> vo) {
        return sysUserService.edit(vo);
    }

//    /**
//     * 系统用户状态修改
//     * @param vo
//     * @return
//     */
//    @PostMapping("updateStatus")
//    //@RequiresPermissions("user:updateStatus")
//    @OperateLogAnnotation(model="系统用户状态", operateType = "修改")
//    public Object updateStatus(@RequestBody @Valid SysUserStatusReqVO vo) {
//        return sysUserService.updateStatus(vo);
//    }
}
