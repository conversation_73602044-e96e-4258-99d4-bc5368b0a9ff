package com.r2.bi.controller;

import com.r2.framework.exception.CodeException;
import com.r2.framework.util.ResultCode;
import com.r2.bi.aop.OperateLogAnnotation;
import com.r2.bi.service.SysRoleService;

import com.r2.bi.vo.sysrole.SysRoleCreateReqVO;
import com.r2.bi.vo.sysrole.SysRoleDeleteReqVO;
import com.r2.bi.vo.sysrole.SysRoleListReqVO;
import com.r2.bi.vo.sysrole.SysRoleUpdateReqVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("sysRole")
public class SysRoleController {

    /**
     * 服务对象
     */
    @Resource
    private SysRoleService sysRoleService;

    /**
     * 角色下拉列表[简单]
     *
     * @param
     * @return 查询结果
     */
    @PostMapping("dropList")
    public Object simpleQueryByPage(@RequestBody @Valid SysRoleListReqVO reqVO) {
        return sysRoleService.simpleQueryByPage(reqVO);
    }

    /**
     * 列表
     *
     * @param reqVO
     * @return 查询结果
     */
    @PostMapping("list")
    @OperateLogAnnotation(model="角色管理", operateType = "查询")
    public Object queryByPage(@RequestBody @Valid SysRoleListReqVO reqVO) {
        return sysRoleService.queryByPage(reqVO);
    }


    /**
     * 新增数据
     *
     * @param reqVO
     * @return 新增结果
     */
    @PostMapping("add")
    @OperateLogAnnotation(model="角色管理", operateType = "新增")
    public Object add(@RequestBody @Valid SysRoleCreateReqVO reqVO) {
        return sysRoleService.insert(reqVO);
    }

    /**
     * 编辑数据
     *
     * @param reqVO
     * @return 编辑结果
     */
    @PostMapping("update")
    @OperateLogAnnotation(model="角色管理", operateType = "修改")
    public Object edit(@RequestBody @Valid SysRoleUpdateReqVO reqVO) {
        return sysRoleService.update(reqVO);
    }

    /**
     * 停用/启用
     *
     * @param reqVO
     * @return
     */
    @PostMapping("disable")
    @OperateLogAnnotation(model="角色管理", operateType = "修改")
    public Object disable(@RequestBody @Valid SysRoleDeleteReqVO reqVO) {
        if (null == reqVO.getStatus()) {
            throw new CodeException(ResultCode.Codes.ERROR, "参数异常");
        }
        return sysRoleService.disable(reqVO.getId(), reqVO.getStatus());
    }

    /**
     * 删除
     *
     * @param reqVO
     * @return
     */
    @PostMapping("delete")
    @OperateLogAnnotation(model="角色管理", operateType = "删除")
    public Object delete(@RequestBody @Valid SysRoleDeleteReqVO reqVO) {
        return sysRoleService.delete(reqVO.getId());
    }
}
