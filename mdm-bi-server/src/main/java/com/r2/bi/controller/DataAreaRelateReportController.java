package com.r2.bi.controller;

import com.r2.bi.aop.OperateLogAnnotation;
import com.r2.bi.service.DataAreaRelateService;
import com.r2.bi.vo.dataarearelate.DataAreaRelateReqVO;
import com.r2.bi.vo.dataarearelate.UpdateDataAreaRelateReqVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@RequestMapping("infoDataAreaRelate")
@RestController
public class DataAreaRelateReportController {

    @Resource
    private DataAreaRelateService dataAreaRelateService;

    /**
     * 信息完整度统计
     * @return
     */
    @PostMapping("count")
    @OperateLogAnnotation(model="中心管理", operateType = "信息完整度统计")
    public Object count(){
        return dataAreaRelateService.count();
    }

    /**
     * 信息完整度列表
     * @return
     */
    @PostMapping("list")
    public Object list(@RequestBody DataAreaRelateReqVO req){
        return dataAreaRelateService.list(req);
    }

    /**
     * 信息完整度导出
     */
    @PostMapping("export")
    public void export(@RequestBody UpdateDataAreaRelateReqVO reqVO, HttpServletResponse response){
        dataAreaRelateService.export(reqVO,response);
    }
}
