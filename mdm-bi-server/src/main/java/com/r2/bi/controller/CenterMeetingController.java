package com.r2.bi.controller;


import com.r2.bi.aop.OperateLogAnnotation;
import com.r2.bi.service.CenterMeetingService;
import com.r2.bi.vo.centermeeting.CalendarListReqVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 日历(CenterMeeting)表控制层
 *
 * <AUTHOR>
 * @since 2022-11-10 17:58:23
 */
@RestController
@RequestMapping("/centerMeeting/")
public class CenterMeetingController {

    /**
     * 服务对象
     */
    @Resource
    private CenterMeetingService centerMeetingService;


    /**
     * 根据时间段返回日历列表
     *
     * @param listReqVO 查询实体
     * @return 删除是否成功
     */
    @PostMapping("listCalendar")
    @OperateLogAnnotation(model="中心管理", operateType = "查询上会日历列表")
    public Object listCalendarByScope(@RequestBody @Valid CalendarListReqVO listReqVO) {
        return centerMeetingService.listCalendarByScope(listReqVO);
    }


    @PostMapping("export")
    public void export(@RequestBody @Valid CalendarListReqVO listReqVO,HttpServletResponse response) {
        centerMeetingService.export(listReqVO,response);
    }


}



