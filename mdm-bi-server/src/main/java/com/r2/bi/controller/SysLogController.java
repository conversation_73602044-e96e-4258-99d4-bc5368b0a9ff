package com.r2.bi.controller;

import com.r2.bi.service.SysLogService;
import com.r2.bi.vo.syslog.SysLogListReqVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 系统日志(SysLog)表控制层
 *
 * <AUTHOR>
 * @since 2022-10-26 14:50:43
 */
@RestController
@RequestMapping("sysLog")
public class SysLogController {
    /**
     * 服务对象
     */
    @Resource
    private SysLogService sysLogService;

    /**
     * 分页查询
     *
     * @param reqVO 筛选条件
     * @return 查询结果
     */
    @PostMapping("list")
    public Object queryByPage(@RequestBody @Valid SysLogListReqVO reqVO) {
        return sysLogService.queryByPage(reqVO);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param reqVO
     * @return 单条数据
     */
    @PostMapping("detail")
    public Object queryById(@RequestBody @Valid SysLogListReqVO reqVO) {
        return sysLogService.queryById(reqVO.getId());
    }
}

