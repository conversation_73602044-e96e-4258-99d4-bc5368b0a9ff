package com.r2.bi.controller;

import com.r2.bi.aop.OperateLogAnnotation;
import com.r2.bi.service.auth.AuthService;
import com.r2.bi.task.SyncOrganizationTaskService;
import com.r2.bi.task.SyncUserTaskService;
import com.r2.bi.vo.login.AuthLoginReqVO;
import com.r2.bi.vo.login.AuthUpdatePasswordReqVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RequestMapping("/auth/")
@RestController
public class AuthController {

    @Resource
    private AuthService authService;

    @Resource
    private SyncUserTaskService syncUserTaskService;

    @Resource
    private SyncOrganizationTaskService syncOrganizationTaskService;


    /**
     * sso登录
     * @param
     * @return
     */
    @PostMapping("thirdLoginBi")
    @OperateLogAnnotation(model="sso三方系统登入", operateType = "sso登录")
    public Object thirdLoginBi(@RequestParam(value = "key") String key) {
        return authService.thirdLoginBi(key);
    }

    /**
     * 登入
     * @param vo
     * @return
     */
    @PostMapping("login")
    @OperateLogAnnotation(model="登入", operateType = "登入")
    public Object login(@RequestBody @Valid AuthLoginReqVO vo) {
        return authService.login(vo);
    }



    /**
     * 登出
     * @return
     */
    @PostMapping("logout")
    public Object logout() {
        return authService.logout();
    }


    /**
     * 更新密码
     * @param authUpdatePasswordReqVO
     * @return
     */
    @PostMapping("updatePassword")
    public Object updatePassword(@RequestBody @Valid AuthUpdatePasswordReqVO authUpdatePasswordReqVO){
        return authService.updatePassword(authUpdatePasswordReqVO);
    }
    /**
     * [后门] 同步中台人员
     * @param
     * @return
     */
    @GetMapping("syncUserBackdoor")
    @OperateLogAnnotation(model="同步用户", operateType = "同步用户")
    public Object syncUserBackdoor() {
        try {
            syncUserTaskService.handelSyncOrganizationTask();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * [后门] 同步中台组织
     * @param
     * @return
     */
    @GetMapping("syncOrganizationBackdoor")
    public Object syncOrganizationBackdoor() {
        try {
            syncOrganizationTaskService.handelSyncOrganizationTask();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
