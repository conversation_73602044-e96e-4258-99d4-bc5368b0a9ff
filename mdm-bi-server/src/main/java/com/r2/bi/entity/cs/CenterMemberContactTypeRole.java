package com.r2.bi.entity.cs;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 中心人员任职信息与角色关系表(CenterMemberContactTypeRole)实体类
 *
 * <AUTHOR>
 * @since 2023-06-28 17:17:24
 */
@Data
@TableName("t_center_member_contact_type_role")
public class CenterMemberContactTypeRole implements Serializable {
    private static final long serialVersionUID = 761364958712617752L;
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 中心id
     */
    private Long centerId;
    /**
     * 人员id
     */
    private Long memberId;
    /**
     * 职位
     */
    private String contactType;
    /**
     * 角色
     */
    private String role;
}

