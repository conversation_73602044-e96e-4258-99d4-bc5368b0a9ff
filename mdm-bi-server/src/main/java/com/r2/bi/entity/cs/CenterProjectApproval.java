package com.r2.bi.entity.cs;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 机构立项信息(CenterProjectApproval)表实体类
 *
 * <AUTHOR>
 * @since 2022-11-10 11:25:41
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_center_project_approval")
public class CenterProjectApproval implements Serializable {
    private static final long serialVersionUID = 1L;
        
    @TableId(value = "id", type=IdType.AUTO)
    private Long id;
    //是否删除 0未删除 1删除
    private Integer isDelete;
    //删除标识 
    private String deleteFlag;
    //修改用户
    private String modifiedbyname;
    //修改用户id
    private String modifiedby;
    //创建用户
    private String createdbyname;
    //创建用户id
    private String createdby;
    //修改日期
    private Date datemodified;
    //创建日期
    private Date datecreated;
    //中心id
    private Long centerId;
    //联系人id（是否保留看业务设计）
    private String contact;
    //新项目调研流程/顺序
    private String newResearchOrder;
    //新项目调研方式
    private String newResearchWay;
    //新调研人员要求
    private String newResearcherRqmt;
    //新调研人员要求补充说明
    private String newResearcherRqmtInfo;
    //同适应症项目同时开展
    private String simultaneously;
    //一期项目承接资质
    private String fisrtUndertakeQual;
    //PI承接在研项目数量限制
    private String piProjNumLimit;
    //PI承接在研项目数量限制补充说明
    private String piProjNumLimitInfo;
    //对承接项目要求
    private String projRqmt;
    //对承接项目要求补充说明
    private String projRqmtInfo;
    //对SMO要求
    private String smoRqmt;
    //上传优选SMO名单位置
    private String smoRqmtAppendix;
    //优选SMO评选要求
    private String smoChooseRqmt;
    //对启动前CRA要求
    private String craRqmt;
    //对启动前CRA要求补充说明
    @TableField(fill = FieldFill.UPDATE)
    private String craRqmtInfo;
    //前期要求补充说明
    private String preRequisitesInfo;
    //立项周期（_天（自然日））
    private String projectApprovalCycle;
    //立项步骤及要求
    private String workflow;
    //立项审核顺序
    private String ethiauditOrder;
    //是否立项需要组长单位批件
    private String isGroupLeaderFile;
    //遗传办批件是否影响立项审核
    private String auditRequirements;
    //立项SOP下载地址
    private String downFileUrl;
    //资料预审方式（电子、纸质、电子/纸质）
    private String preTrialWay;
    //电子审核方式（系统、邮箱）
    private String verifyOnlineWay;
    //系统链接
    private String systemLink;
    //邮箱地址
    private String emailAddr;
    //审核对应人员联系方式
    private String verifyContactWay;
    //邮箱回复频率
    private String emailReplyFreq;
    //资料发送要求 【电子资料发送要求】
    private String fileSendRqmt;
    //纸质资料递交要求
    private String paperSubmitRqmt;
    //立项审核方式
    private String auditWay;
    //审核频率
    private String verifyFreq;
    //文件审核要求 【文件审核其他要求】
    @TableField(fill = FieldFill.UPDATE)
    private String fileVerifyRqmt;
    //人员递交要求（是否接受CRC递交）
    private String personSubmitRqmt;
    //补充说明【不接受CRC递交-补充说明】
    private String noCrsubmitInfo;
    //立项评审会时间（日历id）（是否保留看业务设计）【立项评审会频率及时间】
    @TableField(fill = FieldFill.UPDATE)
    private String projectApprovalTime;
    //立项评审会频率补充说明
    private String approvalFreqInfo;
    //立项是否需要收费【是否需要收取立项费】
    private String projApprCharge;
    //立项审评费用（含税）
    @TableField(fill = FieldFill.UPDATE)
    private String chargeStandard;
    //打款是否影响立项
    private String paymentImpact;
    //打款时限（会审_天（自然日）之前）
    private String paymentMilestone;
    //其他打款时限要求
    private String paymentMilestoneInfo;
    //打款备注
    private String paymentNotes;
    //立项相关信息补充
    private String supplimentary;
    //相关附件
    private String appendix;
    //启动前质控
    private String qualityControl;
    //启动前质控附件
    private String qualityControlAppendix;
    //smo合作中心
    private String smo;
    //立项资料每轮反馈时限补充说明
    private String perFeedbackLimitInfo;
    //立项审核方式
    private String ethiauditWay;
    //立项资料每轮反馈时限
    private String perFeedbackLimit;

    //二期新增字段

    //国际多中心
    private Integer internationalCenter;
    //国际多中心数量
    private Integer internationalCenterNum;
    //国内多中心
    private Integer domesticCenter;
    //国内多中心数量
    private Integer domesticCenterNum;
    //研究者发起IIT
    private Integer researcherPropose;
    //研究者发起IIT数量
    private Integer researcherProposeNum;
    //对启动前CRC要求
    private String crcRqmt;
    //对启动前CRC要求补充说明
    @TableField(fill = FieldFill.UPDATE)
    private String crcRqmtInfo;
    //遗传办批件是否影响立项审核补充说明
    private String auditRequirementsRqmt;
    //是否需要上传申办方与CDE沟通函
    private String communicationLetter;
    //立项步骤及要求附件
    private String workflowAttachment;
    //会审后多久出结果
    @TableField(fill = FieldFill.UPDATE)
    private String reviewResult;
    //是否需要PPT
    @TableField(fill = FieldFill.UPDATE)
    private String needPpt;
    //是否需要收取立项费
    private String projectApprovalFee;
    //对PPT的要求
    @TableField(fill = FieldFill.UPDATE)
    private String pptRqmt;
    //对PPT的要求附件
    private String pptRqmtAttachment;
    //对立项费到账时间要求
    @TableField(fill = FieldFill.UPDATE)
    private String projectApprovalFeeRqmt;
    //立项费用影响立项进度
    @TableField(fill = FieldFill.UPDATE)
    private String projectApprovalProcess;
    //立项资料上传系统账户要求
    @TableField(fill = FieldFill.UPDATE)
    private String accountRqmt;
    //立项资料上传系统网络要求
    @TableField(fill = FieldFill.UPDATE)
    private String networkRqmt;
    //资料盖章要求
    @TableField(fill = FieldFill.UPDATE)
    private String stampRqmt;
    //份数要求
    @TableField(fill = FieldFill.UPDATE)
    private Integer numRqmt;
    //装订要求
    @TableField(fill = FieldFill.UPDATE)
    private String bindingRqmt;

}
