package com.r2.bi.entity.cs;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 日历周期(新增)(CenterMeetingCycle)表实体类
 *
 * <AUTHOR>
 * @since 2022-11-11 11:16:49
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_center_meeting_cycle")
public class CenterMeetingCycle implements Serializable {
    private static final long serialVersionUID = 1L;
        
    @TableId(value = "id", type=IdType.AUTO)
    private Long id;
    //是否删除 0未删除 1删除
    private Integer isDelete;
    //删除标识 
    private String deleteFlag;
    //创建日期
    private Date datecreated;
    //修改日期
    private Date datemodified;
    //创建用户id
    private String createdby;
    //创建用户
    private String createdbyname;
    //修改用户id
    private String modifiedby;
    //修改用户
    private String modifiedbyname;
    //会议id
    private Long meetingId;
    //会议时间段：上午/下午
    private String period;
    //重复信息（英文逗号隔开）
    private String repeatDetails;
    //重复周
    private String repeatWeek;

}
