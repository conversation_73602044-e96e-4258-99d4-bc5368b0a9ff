package com.r2.bi.entity.cs;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * 伦理信息
 * @TableName t_center_ethics
 */
@TableName(value ="t_center_ethics")
public class TCenterEthics implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long ethicsId;

    /**
     * 是否删除 0未删除 1删除
     */
    private Integer isDelete;

    /**
     * 删除标识 
     */
    private String deleteFlag;

    /**
     * 中心id
     */
    private Long centerId;

    /**
     * 伦理整体时长/伦理流程所需时间
     */
    private String processTimeline;

    /**
     * 伦理上会流程
     */
    private String workflow;

    /**
     * 伦理SOP下载地址（暂无）
     */
    private String downloadEthicsMaterials;

    /**
     * 伦理审核遗传办批件要求
     */
    private String ethicalApprovalRequirements;

    /**
     * 组长单位批件
     */
    private String leaderApproval;

    /**
     * 是否需要PPT
     */
    private String withPpt;

    /**
     * PPT提纲大概
     */
    private String pptOutlines;

    /**
     * 首次上会可快审
     */
    private String allowQuickVerify;

    /**
     * 上会需要排队
     */
    private String meetingWait;

    /**
     * 上会需要排队补充说明
     */
    private String meetingWaitInfo;

    /**
     * 平均排队时间（_周/月）
     */
    private String waitTime;

    /**
     * 是否插队上会
     */
    private String cutLineWill;

    /**
     * 是否可加开伦理会
     */
    private String allowAddlEc;

    /**
     * 汇报要求
     */
    private String prstRqmt;

    /**
     * 复审费用打款备注
     */
    private String reviewPaymentRemark;

    /**
     * 需要预约：1：是；2：否
     */
    private String subscribe;

    /**
     * 预约方式：1：当天预约；2：提前预约；3：伦理抢号预约；4：其他
     */
    private String subscribeType;

    /**
     * 预约方式补充说明
     */
    private String subscribeTypeInfo;

    /**
     * 接待人员要求：1：CRC提交；2：CRA递交；3：其他
     */
    private String staffAsk;

    /**
     * 预约要求
     */
    private String subscribeAsk;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 加开伦理会沟通人员姓名/联系方式：
     */
    private String plusStaffRemark;

    /**
     * 伦理会汇报人：1：PI汇报；2：Sub-I汇报；3：其他
     */
    private String report;

    /**
     * 补充说明
     */
    private String reportRemark;

    /**
     * 汇报要求
     */
    private String reportAsk;

    /**
     * 需要陪同：1: CRA；2：CRC；3：申办方医学；4：不需要
     */
    private String accompany;

    /**
     * 陪同方式：1：线上参会；2：线下参会；3：其他；
     */
    private String accompanyType;

    /**
     * 补充说明
     */
    private String accompanyRemark;

    /**
     * 电子审核方式：1：系统审核；2：邮箱审核
     */
    private String eAuditType;

    /**
     * 系统审核方式:CRA账号外网上传资料/PI账号内网上传资料/CRA账号内网上传资料/其他
     */
    private String systemAuditType;

    /**
     * 系统链接
     */
    private String systemAuditLink;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 对应人员联系方式
     */
    private String emailAuditAddress;

    /**
     * 邮箱回复频率：1-3天/3-5天/5天以上
     */
    private String emailReply;

    /**
     * 资料发送要求
     */
    private String dataSendRequire;

    /**
     * 补充说明
     */
    private String auditRemark;

    /**
     * 补充说明
     */
    private String ethicalApprovalRequirementsRemark;

    /**
     * 补充说明
     */
    private String leaderApprovalRemark;

    /**
     * 伦理会频率补充说明
     */
    private String rateRemark;

    /**
     * 平均会审项目数
     */
    private Integer projectNum;

    /**
     * 对接人员
     */
    private String abutmentStaff;

    /**
     * 对接人联系方式
     */
    private String abutmentStaffRemark;

    /**
     * 沟通注意点
     */
    private String point;

    /**
     * 可加开伦理会：1：是；2：否
     */
    private String plus;

    /**
     * 加开伦理会条件：
     */
    private String plusCondition;

    /**
     * 伦理前置
     */
    private String ethicalPre;

    /**
     * 前置条件
     */
    private String ethicalPrecondition;

    /**
     * 立项伦理资料可同时提交：1：是；2：否
     */
    private String submit;

    /**
     * 伦理整体时长
     */
    private String duration;

    /**
     * EC会议取批件时间
     */
    private String ecTime;

    /**
     * 伦理SOP下载地址
     */
    private String accessoryId;

    /**
     * 资料预审：1：电子审核；2：纸质版审核；3：电子/纸质版审核
     */
    private String dataPreAudit;

    /**
     * 纸质资料人员递交要求: 接受CRC提交/不接受CRC提交
     */
    private String paperDataStaffRequire;

    /**
     * 会前资料预审：1：是；2：否
     */
    private String preAudit;

    /**
     * 精装版/简版资料要求：1：电子版；2：纸质版；
     */
    private String dataType;

    /**
     * 纸质材料套数
     */
    private String dataNum;

    /**
     * 提交人员
     */
    private String submitStaff;

    /**
     * 送审时间
     */
    private String submitTime;

    /**
     * 装订要求
     */
    private String bindAsk;

    /**
     * 需要ppt：1：是；2：否
     */
    private String ppt;

    /**
     * ppt模板
     */
    private String pptUrls;

    /**
     * 送审地址
     */
    private String submitAddress;

    /**
     * 上会要求补充说明
     */
    private String askRemark;

    /**
     * 打款时限
     */
    private String reviewPaymentDeadline;

    /**
     * 发票领取时间及地址
     */
    private String reviewInvoice;

    /**
     * 相关附件
     */
    private String attachment;

    /**
     * 补充说明
     */
    private String remark;

    /**
     * 补充说明
     */
    private String allowQuickVerifyRemark;

    /**
     * 伦理会频率说明
     */
    private String ethicsFreqDes;

    /**
     * 首次上会伦理费用
     */
    private String charge;

    /**
     * 是否影响参会：1：是；2：否
     */
    private String firstInfluence;

    /**
     * 打款备注
     */
    private String firstPaymentRemark;

    /**
     * 打款时限
     */
    private String firstPaymentDeadline;

    /**
     * 发票领取时间及地址
     */
    private String firstInvoice;

    /**
     * 复审上会伦理费用
     */
    private String reviewFee;

    /**
     * 快审/会审：1：快审；2：会审；
     */
    private String auditType;

    /**
     * 是否影响参会：1：是；2：否
     */
    private String reviewInfluence;

    /**
     * 伦理上会须满足条件: 1.伦理费需会前到账, 2.指定伦理汇报人员, 3.伦理上会前需伦理资料预审通过, 4.伦理上会前需完成纸质版资料提交, 5.其他 多选,英文逗号隔开
     */
    private String meetingPerCond;

    /**
     * 伦理上会须满足条件补充说明
     */
    private String meetingPerCondRemark;

    /**
     * 伦理上会须满足条件
     */
    private String ethicalMeetingRequire;

    /**
     * EC会议至公布结果时长
     */
    private String ecResultTime;

    /**
     * 是否有明确接待日要求：是/否
     */
    private String openDay;

    /**
     * 接待日要求
     */
    private String openDayRequire;

    /**
     * 伦理资料上传系统账户要求
     */
    private String ethicalDataAccountRequire;

    /**
     * 伦理资料上传网络要求
     */
    private String ethicalDataNetworkRequire;

    /**
     * 线上审核流程
     */
    private String onlineAuditProcess;

    /**
     * 是否需要纸质资料
     */
    private String paperData;

    /**
     * 不接受CRC递交-补充说明
     */
    private String noCrcRemark;

    /**
     * 伦理资料每轮反馈时限
     */
    private String ethicsDataDeadline;

    /**
     * 资料发送要求补充说明
     */
    private String dataSendRemark;

    /**
     * 纸质材料盖章要求
     */
    private String stampAsk;

    /**
     * 伦理审查形式
     */
    private String ethicsAuditType;

    /**
     * 加急费用
     */
    private String urgentFee;

    /**
     * 加急费用是否需要提前到账
     */
    private String urgentFeeAhead;

    /**
     * 快审费用
     */
    private String quickFee;

    /**
     * 快审费用是否需要提前到账
     */
    private String quickFeeAhead;

    /**
     * 伦理付款账号
     */
    private String paymentCardNo;

    /**
     * 相关附件说明
     */
    private String attachmentRemark;

    /**
     * 上会PPT模板及递交伦理相关文件说明
     */
    private String pptUrlsRemark;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public Long getEthicsId() {
        return ethicsId;
    }

    /**
     * 
     */
    public void setEthicsId(Long ethicsId) {
        this.ethicsId = ethicsId;
    }

    /**
     * 是否删除 0未删除 1删除
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * 是否删除 0未删除 1删除
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 删除标识 
     */
    public String getDeleteFlag() {
        return deleteFlag;
    }

    /**
     * 删除标识 
     */
    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    /**
     * 中心id
     */
    public Long getCenterId() {
        return centerId;
    }

    /**
     * 中心id
     */
    public void setCenterId(Long centerId) {
        this.centerId = centerId;
    }

    /**
     * 伦理整体时长/伦理流程所需时间
     */
    public String getProcessTimeline() {
        return processTimeline;
    }

    /**
     * 伦理整体时长/伦理流程所需时间
     */
    public void setProcessTimeline(String processTimeline) {
        this.processTimeline = processTimeline;
    }

    /**
     * 伦理上会流程
     */
    public String getWorkflow() {
        return workflow;
    }

    /**
     * 伦理上会流程
     */
    public void setWorkflow(String workflow) {
        this.workflow = workflow;
    }

    /**
     * 伦理SOP下载地址（暂无）
     */
    public String getDownloadEthicsMaterials() {
        return downloadEthicsMaterials;
    }

    /**
     * 伦理SOP下载地址（暂无）
     */
    public void setDownloadEthicsMaterials(String downloadEthicsMaterials) {
        this.downloadEthicsMaterials = downloadEthicsMaterials;
    }

    /**
     * 伦理审核遗传办批件要求
     */
    public String getEthicalApprovalRequirements() {
        return ethicalApprovalRequirements;
    }

    /**
     * 伦理审核遗传办批件要求
     */
    public void setEthicalApprovalRequirements(String ethicalApprovalRequirements) {
        this.ethicalApprovalRequirements = ethicalApprovalRequirements;
    }

    /**
     * 组长单位批件
     */
    public String getLeaderApproval() {
        return leaderApproval;
    }

    /**
     * 组长单位批件
     */
    public void setLeaderApproval(String leaderApproval) {
        this.leaderApproval = leaderApproval;
    }

    /**
     * 是否需要PPT
     */
    public String getWithPpt() {
        return withPpt;
    }

    /**
     * 是否需要PPT
     */
    public void setWithPpt(String withPpt) {
        this.withPpt = withPpt;
    }

    /**
     * PPT提纲大概
     */
    public String getPptOutlines() {
        return pptOutlines;
    }

    /**
     * PPT提纲大概
     */
    public void setPptOutlines(String pptOutlines) {
        this.pptOutlines = pptOutlines;
    }

    /**
     * 首次上会可快审
     */
    public String getAllowQuickVerify() {
        return allowQuickVerify;
    }

    /**
     * 首次上会可快审
     */
    public void setAllowQuickVerify(String allowQuickVerify) {
        this.allowQuickVerify = allowQuickVerify;
    }

    /**
     * 上会需要排队
     */
    public String getMeetingWait() {
        return meetingWait;
    }

    /**
     * 上会需要排队
     */
    public void setMeetingWait(String meetingWait) {
        this.meetingWait = meetingWait;
    }

    /**
     * 上会需要排队补充说明
     */
    public String getMeetingWaitInfo() {
        return meetingWaitInfo;
    }

    /**
     * 上会需要排队补充说明
     */
    public void setMeetingWaitInfo(String meetingWaitInfo) {
        this.meetingWaitInfo = meetingWaitInfo;
    }

    /**
     * 平均排队时间（_周/月）
     */
    public String getWaitTime() {
        return waitTime;
    }

    /**
     * 平均排队时间（_周/月）
     */
    public void setWaitTime(String waitTime) {
        this.waitTime = waitTime;
    }

    /**
     * 是否插队上会
     */
    public String getCutLineWill() {
        return cutLineWill;
    }

    /**
     * 是否插队上会
     */
    public void setCutLineWill(String cutLineWill) {
        this.cutLineWill = cutLineWill;
    }

    /**
     * 是否可加开伦理会
     */
    public String getAllowAddlEc() {
        return allowAddlEc;
    }

    /**
     * 是否可加开伦理会
     */
    public void setAllowAddlEc(String allowAddlEc) {
        this.allowAddlEc = allowAddlEc;
    }

    /**
     * 汇报要求
     */
    public String getPrstRqmt() {
        return prstRqmt;
    }

    /**
     * 汇报要求
     */
    public void setPrstRqmt(String prstRqmt) {
        this.prstRqmt = prstRqmt;
    }

    /**
     * 复审费用打款备注
     */
    public String getReviewPaymentRemark() {
        return reviewPaymentRemark;
    }

    /**
     * 复审费用打款备注
     */
    public void setReviewPaymentRemark(String reviewPaymentRemark) {
        this.reviewPaymentRemark = reviewPaymentRemark;
    }

    /**
     * 需要预约：1：是；2：否
     */
    public String getSubscribe() {
        return subscribe;
    }

    /**
     * 需要预约：1：是；2：否
     */
    public void setSubscribe(String subscribe) {
        this.subscribe = subscribe;
    }

    /**
     * 预约方式：1：当天预约；2：提前预约；3：伦理抢号预约；4：其他
     */
    public String getSubscribeType() {
        return subscribeType;
    }

    /**
     * 预约方式：1：当天预约；2：提前预约；3：伦理抢号预约；4：其他
     */
    public void setSubscribeType(String subscribeType) {
        this.subscribeType = subscribeType;
    }

    /**
     * 预约方式补充说明
     */
    public String getSubscribeTypeInfo() {
        return subscribeTypeInfo;
    }

    /**
     * 预约方式补充说明
     */
    public void setSubscribeTypeInfo(String subscribeTypeInfo) {
        this.subscribeTypeInfo = subscribeTypeInfo;
    }

    /**
     * 接待人员要求：1：CRC提交；2：CRA递交；3：其他
     */
    public String getStaffAsk() {
        return staffAsk;
    }

    /**
     * 接待人员要求：1：CRC提交；2：CRA递交；3：其他
     */
    public void setStaffAsk(String staffAsk) {
        this.staffAsk = staffAsk;
    }

    /**
     * 预约要求
     */
    public String getSubscribeAsk() {
        return subscribeAsk;
    }

    /**
     * 预约要求
     */
    public void setSubscribeAsk(String subscribeAsk) {
        this.subscribeAsk = subscribeAsk;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建人
     */
    public String getCreateBy() {
        return createBy;
    }

    /**
     * 创建人
     */
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 更新人
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 更新人
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 加开伦理会沟通人员姓名/联系方式：
     */
    public String getPlusStaffRemark() {
        return plusStaffRemark;
    }

    /**
     * 加开伦理会沟通人员姓名/联系方式：
     */
    public void setPlusStaffRemark(String plusStaffRemark) {
        this.plusStaffRemark = plusStaffRemark;
    }

    /**
     * 伦理会汇报人：1：PI汇报；2：Sub-I汇报；3：其他
     */
    public String getReport() {
        return report;
    }

    /**
     * 伦理会汇报人：1：PI汇报；2：Sub-I汇报；3：其他
     */
    public void setReport(String report) {
        this.report = report;
    }

    /**
     * 补充说明
     */
    public String getReportRemark() {
        return reportRemark;
    }

    /**
     * 补充说明
     */
    public void setReportRemark(String reportRemark) {
        this.reportRemark = reportRemark;
    }

    /**
     * 汇报要求
     */
    public String getReportAsk() {
        return reportAsk;
    }

    /**
     * 汇报要求
     */
    public void setReportAsk(String reportAsk) {
        this.reportAsk = reportAsk;
    }

    /**
     * 需要陪同：1: CRA；2：CRC；3：申办方医学；4：不需要
     */
    public String getAccompany() {
        return accompany;
    }

    /**
     * 需要陪同：1: CRA；2：CRC；3：申办方医学；4：不需要
     */
    public void setAccompany(String accompany) {
        this.accompany = accompany;
    }

    /**
     * 陪同方式：1：线上参会；2：线下参会；3：其他；
     */
    public String getAccompanyType() {
        return accompanyType;
    }

    /**
     * 陪同方式：1：线上参会；2：线下参会；3：其他；
     */
    public void setAccompanyType(String accompanyType) {
        this.accompanyType = accompanyType;
    }

    /**
     * 补充说明
     */
    public String getAccompanyRemark() {
        return accompanyRemark;
    }

    /**
     * 补充说明
     */
    public void setAccompanyRemark(String accompanyRemark) {
        this.accompanyRemark = accompanyRemark;
    }

    /**
     * 电子审核方式：1：系统审核；2：邮箱审核
     */
    public String geteAuditType() {
        return eAuditType;
    }

    /**
     * 电子审核方式：1：系统审核；2：邮箱审核
     */
    public void seteAuditType(String eAuditType) {
        this.eAuditType = eAuditType;
    }

    /**
     * 系统审核方式:CRA账号外网上传资料/PI账号内网上传资料/CRA账号内网上传资料/其他
     */
    public String getSystemAuditType() {
        return systemAuditType;
    }

    /**
     * 系统审核方式:CRA账号外网上传资料/PI账号内网上传资料/CRA账号内网上传资料/其他
     */
    public void setSystemAuditType(String systemAuditType) {
        this.systemAuditType = systemAuditType;
    }

    /**
     * 系统链接
     */
    public String getSystemAuditLink() {
        return systemAuditLink;
    }

    /**
     * 系统链接
     */
    public void setSystemAuditLink(String systemAuditLink) {
        this.systemAuditLink = systemAuditLink;
    }

    /**
     * 邮箱地址
     */
    public String getEmail() {
        return email;
    }

    /**
     * 邮箱地址
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 对应人员联系方式
     */
    public String getEmailAuditAddress() {
        return emailAuditAddress;
    }

    /**
     * 对应人员联系方式
     */
    public void setEmailAuditAddress(String emailAuditAddress) {
        this.emailAuditAddress = emailAuditAddress;
    }

    /**
     * 邮箱回复频率：1-3天/3-5天/5天以上
     */
    public String getEmailReply() {
        return emailReply;
    }

    /**
     * 邮箱回复频率：1-3天/3-5天/5天以上
     */
    public void setEmailReply(String emailReply) {
        this.emailReply = emailReply;
    }

    /**
     * 资料发送要求
     */
    public String getDataSendRequire() {
        return dataSendRequire;
    }

    /**
     * 资料发送要求
     */
    public void setDataSendRequire(String dataSendRequire) {
        this.dataSendRequire = dataSendRequire;
    }

    /**
     * 补充说明
     */
    public String getAuditRemark() {
        return auditRemark;
    }

    /**
     * 补充说明
     */
    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    /**
     * 补充说明
     */
    public String getEthicalApprovalRequirementsRemark() {
        return ethicalApprovalRequirementsRemark;
    }

    /**
     * 补充说明
     */
    public void setEthicalApprovalRequirementsRemark(String ethicalApprovalRequirementsRemark) {
        this.ethicalApprovalRequirementsRemark = ethicalApprovalRequirementsRemark;
    }

    /**
     * 补充说明
     */
    public String getLeaderApprovalRemark() {
        return leaderApprovalRemark;
    }

    /**
     * 补充说明
     */
    public void setLeaderApprovalRemark(String leaderApprovalRemark) {
        this.leaderApprovalRemark = leaderApprovalRemark;
    }

    /**
     * 伦理会频率补充说明
     */
    public String getRateRemark() {
        return rateRemark;
    }

    /**
     * 伦理会频率补充说明
     */
    public void setRateRemark(String rateRemark) {
        this.rateRemark = rateRemark;
    }

    /**
     * 平均会审项目数
     */
    public Integer getProjectNum() {
        return projectNum;
    }

    /**
     * 平均会审项目数
     */
    public void setProjectNum(Integer projectNum) {
        this.projectNum = projectNum;
    }

    /**
     * 对接人员
     */
    public String getAbutmentStaff() {
        return abutmentStaff;
    }

    /**
     * 对接人员
     */
    public void setAbutmentStaff(String abutmentStaff) {
        this.abutmentStaff = abutmentStaff;
    }

    /**
     * 对接人联系方式
     */
    public String getAbutmentStaffRemark() {
        return abutmentStaffRemark;
    }

    /**
     * 对接人联系方式
     */
    public void setAbutmentStaffRemark(String abutmentStaffRemark) {
        this.abutmentStaffRemark = abutmentStaffRemark;
    }

    /**
     * 沟通注意点
     */
    public String getPoint() {
        return point;
    }

    /**
     * 沟通注意点
     */
    public void setPoint(String point) {
        this.point = point;
    }

    /**
     * 可加开伦理会：1：是；2：否
     */
    public String getPlus() {
        return plus;
    }

    /**
     * 可加开伦理会：1：是；2：否
     */
    public void setPlus(String plus) {
        this.plus = plus;
    }

    /**
     * 加开伦理会条件：
     */
    public String getPlusCondition() {
        return plusCondition;
    }

    /**
     * 加开伦理会条件：
     */
    public void setPlusCondition(String plusCondition) {
        this.plusCondition = plusCondition;
    }

    /**
     * 伦理前置
     */
    public String getEthicalPre() {
        return ethicalPre;
    }

    /**
     * 伦理前置
     */
    public void setEthicalPre(String ethicalPre) {
        this.ethicalPre = ethicalPre;
    }

    /**
     * 前置条件
     */
    public String getEthicalPrecondition() {
        return ethicalPrecondition;
    }

    /**
     * 前置条件
     */
    public void setEthicalPrecondition(String ethicalPrecondition) {
        this.ethicalPrecondition = ethicalPrecondition;
    }

    /**
     * 立项伦理资料可同时提交：1：是；2：否
     */
    public String getSubmit() {
        return submit;
    }

    /**
     * 立项伦理资料可同时提交：1：是；2：否
     */
    public void setSubmit(String submit) {
        this.submit = submit;
    }

    /**
     * 伦理整体时长
     */
    public String getDuration() {
        return duration;
    }

    /**
     * 伦理整体时长
     */
    public void setDuration(String duration) {
        this.duration = duration;
    }

    /**
     * EC会议取批件时间
     */
    public String getEcTime() {
        return ecTime;
    }

    /**
     * EC会议取批件时间
     */
    public void setEcTime(String ecTime) {
        this.ecTime = ecTime;
    }

    /**
     * 伦理SOP下载地址
     */
    public String getAccessoryId() {
        return accessoryId;
    }

    /**
     * 伦理SOP下载地址
     */
    public void setAccessoryId(String accessoryId) {
        this.accessoryId = accessoryId;
    }

    /**
     * 资料预审：1：电子审核；2：纸质版审核；3：电子/纸质版审核
     */
    public String getDataPreAudit() {
        return dataPreAudit;
    }

    /**
     * 资料预审：1：电子审核；2：纸质版审核；3：电子/纸质版审核
     */
    public void setDataPreAudit(String dataPreAudit) {
        this.dataPreAudit = dataPreAudit;
    }

    /**
     * 纸质资料人员递交要求: 接受CRC提交/不接受CRC提交
     */
    public String getPaperDataStaffRequire() {
        return paperDataStaffRequire;
    }

    /**
     * 纸质资料人员递交要求: 接受CRC提交/不接受CRC提交
     */
    public void setPaperDataStaffRequire(String paperDataStaffRequire) {
        this.paperDataStaffRequire = paperDataStaffRequire;
    }

    /**
     * 会前资料预审：1：是；2：否
     */
    public String getPreAudit() {
        return preAudit;
    }

    /**
     * 会前资料预审：1：是；2：否
     */
    public void setPreAudit(String preAudit) {
        this.preAudit = preAudit;
    }

    /**
     * 精装版/简版资料要求：1：电子版；2：纸质版；
     */
    public String getDataType() {
        return dataType;
    }

    /**
     * 精装版/简版资料要求：1：电子版；2：纸质版；
     */
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    /**
     * 纸质材料套数
     */
    public String getDataNum() {
        return dataNum;
    }

    /**
     * 纸质材料套数
     */
    public void setDataNum(String dataNum) {
        this.dataNum = dataNum;
    }

    /**
     * 提交人员
     */
    public String getSubmitStaff() {
        return submitStaff;
    }

    /**
     * 提交人员
     */
    public void setSubmitStaff(String submitStaff) {
        this.submitStaff = submitStaff;
    }

    /**
     * 送审时间
     */
    public String getSubmitTime() {
        return submitTime;
    }

    /**
     * 送审时间
     */
    public void setSubmitTime(String submitTime) {
        this.submitTime = submitTime;
    }

    /**
     * 装订要求
     */
    public String getBindAsk() {
        return bindAsk;
    }

    /**
     * 装订要求
     */
    public void setBindAsk(String bindAsk) {
        this.bindAsk = bindAsk;
    }

    /**
     * 需要ppt：1：是；2：否
     */
    public String getPpt() {
        return ppt;
    }

    /**
     * 需要ppt：1：是；2：否
     */
    public void setPpt(String ppt) {
        this.ppt = ppt;
    }

    /**
     * ppt模板
     */
    public String getPptUrls() {
        return pptUrls;
    }

    /**
     * ppt模板
     */
    public void setPptUrls(String pptUrls) {
        this.pptUrls = pptUrls;
    }

    /**
     * 送审地址
     */
    public String getSubmitAddress() {
        return submitAddress;
    }

    /**
     * 送审地址
     */
    public void setSubmitAddress(String submitAddress) {
        this.submitAddress = submitAddress;
    }

    /**
     * 上会要求补充说明
     */
    public String getAskRemark() {
        return askRemark;
    }

    /**
     * 上会要求补充说明
     */
    public void setAskRemark(String askRemark) {
        this.askRemark = askRemark;
    }

    /**
     * 打款时限
     */
    public String getReviewPaymentDeadline() {
        return reviewPaymentDeadline;
    }

    /**
     * 打款时限
     */
    public void setReviewPaymentDeadline(String reviewPaymentDeadline) {
        this.reviewPaymentDeadline = reviewPaymentDeadline;
    }

    /**
     * 发票领取时间及地址
     */
    public String getReviewInvoice() {
        return reviewInvoice;
    }

    /**
     * 发票领取时间及地址
     */
    public void setReviewInvoice(String reviewInvoice) {
        this.reviewInvoice = reviewInvoice;
    }

    /**
     * 相关附件
     */
    public String getAttachment() {
        return attachment;
    }

    /**
     * 相关附件
     */
    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }

    /**
     * 补充说明
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 补充说明
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 补充说明
     */
    public String getAllowQuickVerifyRemark() {
        return allowQuickVerifyRemark;
    }

    /**
     * 补充说明
     */
    public void setAllowQuickVerifyRemark(String allowQuickVerifyRemark) {
        this.allowQuickVerifyRemark = allowQuickVerifyRemark;
    }

    /**
     * 伦理会频率说明
     */
    public String getEthicsFreqDes() {
        return ethicsFreqDes;
    }

    /**
     * 伦理会频率说明
     */
    public void setEthicsFreqDes(String ethicsFreqDes) {
        this.ethicsFreqDes = ethicsFreqDes;
    }

    /**
     * 首次上会伦理费用
     */
    public String getCharge() {
        return charge;
    }

    /**
     * 首次上会伦理费用
     */
    public void setCharge(String charge) {
        this.charge = charge;
    }

    /**
     * 是否影响参会：1：是；2：否
     */
    public String getFirstInfluence() {
        return firstInfluence;
    }

    /**
     * 是否影响参会：1：是；2：否
     */
    public void setFirstInfluence(String firstInfluence) {
        this.firstInfluence = firstInfluence;
    }

    /**
     * 打款备注
     */
    public String getFirstPaymentRemark() {
        return firstPaymentRemark;
    }

    /**
     * 打款备注
     */
    public void setFirstPaymentRemark(String firstPaymentRemark) {
        this.firstPaymentRemark = firstPaymentRemark;
    }

    /**
     * 打款时限
     */
    public String getFirstPaymentDeadline() {
        return firstPaymentDeadline;
    }

    /**
     * 打款时限
     */
    public void setFirstPaymentDeadline(String firstPaymentDeadline) {
        this.firstPaymentDeadline = firstPaymentDeadline;
    }

    /**
     * 发票领取时间及地址
     */
    public String getFirstInvoice() {
        return firstInvoice;
    }

    /**
     * 发票领取时间及地址
     */
    public void setFirstInvoice(String firstInvoice) {
        this.firstInvoice = firstInvoice;
    }

    /**
     * 复审上会伦理费用
     */
    public String getReviewFee() {
        return reviewFee;
    }

    /**
     * 复审上会伦理费用
     */
    public void setReviewFee(String reviewFee) {
        this.reviewFee = reviewFee;
    }

    /**
     * 快审/会审：1：快审；2：会审；
     */
    public String getAuditType() {
        return auditType;
    }

    /**
     * 快审/会审：1：快审；2：会审；
     */
    public void setAuditType(String auditType) {
        this.auditType = auditType;
    }

    /**
     * 是否影响参会：1：是；2：否
     */
    public String getReviewInfluence() {
        return reviewInfluence;
    }

    /**
     * 是否影响参会：1：是；2：否
     */
    public void setReviewInfluence(String reviewInfluence) {
        this.reviewInfluence = reviewInfluence;
    }

    /**
     * 伦理上会须满足条件: 1.伦理费需会前到账, 2.指定伦理汇报人员, 3.伦理上会前需伦理资料预审通过, 4.伦理上会前需完成纸质版资料提交, 5.其他 多选,英文逗号隔开
     */
    public String getMeetingPerCond() {
        return meetingPerCond;
    }

    /**
     * 伦理上会须满足条件: 1.伦理费需会前到账, 2.指定伦理汇报人员, 3.伦理上会前需伦理资料预审通过, 4.伦理上会前需完成纸质版资料提交, 5.其他 多选,英文逗号隔开
     */
    public void setMeetingPerCond(String meetingPerCond) {
        this.meetingPerCond = meetingPerCond;
    }

    /**
     * 伦理上会须满足条件补充说明
     */
    public String getMeetingPerCondRemark() {
        return meetingPerCondRemark;
    }

    /**
     * 伦理上会须满足条件补充说明
     */
    public void setMeetingPerCondRemark(String meetingPerCondRemark) {
        this.meetingPerCondRemark = meetingPerCondRemark;
    }

    /**
     * 伦理上会须满足条件
     */
    public String getEthicalMeetingRequire() {
        return ethicalMeetingRequire;
    }

    /**
     * 伦理上会须满足条件
     */
    public void setEthicalMeetingRequire(String ethicalMeetingRequire) {
        this.ethicalMeetingRequire = ethicalMeetingRequire;
    }

    /**
     * EC会议至公布结果时长
     */
    public String getEcResultTime() {
        return ecResultTime;
    }

    /**
     * EC会议至公布结果时长
     */
    public void setEcResultTime(String ecResultTime) {
        this.ecResultTime = ecResultTime;
    }

    /**
     * 是否有明确接待日要求：是/否
     */
    public String getOpenDay() {
        return openDay;
    }

    /**
     * 是否有明确接待日要求：是/否
     */
    public void setOpenDay(String openDay) {
        this.openDay = openDay;
    }

    /**
     * 接待日要求
     */
    public String getOpenDayRequire() {
        return openDayRequire;
    }

    /**
     * 接待日要求
     */
    public void setOpenDayRequire(String openDayRequire) {
        this.openDayRequire = openDayRequire;
    }

    /**
     * 伦理资料上传系统账户要求
     */
    public String getEthicalDataAccountRequire() {
        return ethicalDataAccountRequire;
    }

    /**
     * 伦理资料上传系统账户要求
     */
    public void setEthicalDataAccountRequire(String ethicalDataAccountRequire) {
        this.ethicalDataAccountRequire = ethicalDataAccountRequire;
    }

    /**
     * 伦理资料上传网络要求
     */
    public String getEthicalDataNetworkRequire() {
        return ethicalDataNetworkRequire;
    }

    /**
     * 伦理资料上传网络要求
     */
    public void setEthicalDataNetworkRequire(String ethicalDataNetworkRequire) {
        this.ethicalDataNetworkRequire = ethicalDataNetworkRequire;
    }

    /**
     * 线上审核流程
     */
    public String getOnlineAuditProcess() {
        return onlineAuditProcess;
    }

    /**
     * 线上审核流程
     */
    public void setOnlineAuditProcess(String onlineAuditProcess) {
        this.onlineAuditProcess = onlineAuditProcess;
    }

    /**
     * 是否需要纸质资料
     */
    public String getPaperData() {
        return paperData;
    }

    /**
     * 是否需要纸质资料
     */
    public void setPaperData(String paperData) {
        this.paperData = paperData;
    }

    /**
     * 不接受CRC递交-补充说明
     */
    public String getNoCrcRemark() {
        return noCrcRemark;
    }

    /**
     * 不接受CRC递交-补充说明
     */
    public void setNoCrcRemark(String noCrcRemark) {
        this.noCrcRemark = noCrcRemark;
    }

    /**
     * 伦理资料每轮反馈时限
     */
    public String getEthicsDataDeadline() {
        return ethicsDataDeadline;
    }

    /**
     * 伦理资料每轮反馈时限
     */
    public void setEthicsDataDeadline(String ethicsDataDeadline) {
        this.ethicsDataDeadline = ethicsDataDeadline;
    }

    /**
     * 资料发送要求补充说明
     */
    public String getDataSendRemark() {
        return dataSendRemark;
    }

    /**
     * 资料发送要求补充说明
     */
    public void setDataSendRemark(String dataSendRemark) {
        this.dataSendRemark = dataSendRemark;
    }

    /**
     * 纸质材料盖章要求
     */
    public String getStampAsk() {
        return stampAsk;
    }

    /**
     * 纸质材料盖章要求
     */
    public void setStampAsk(String stampAsk) {
        this.stampAsk = stampAsk;
    }

    /**
     * 伦理审查形式
     */
    public String getEthicsAuditType() {
        return ethicsAuditType;
    }

    /**
     * 伦理审查形式
     */
    public void setEthicsAuditType(String ethicsAuditType) {
        this.ethicsAuditType = ethicsAuditType;
    }

    /**
     * 加急费用
     */
    public String getUrgentFee() {
        return urgentFee;
    }

    /**
     * 加急费用
     */
    public void setUrgentFee(String urgentFee) {
        this.urgentFee = urgentFee;
    }

    /**
     * 加急费用是否需要提前到账
     */
    public String getUrgentFeeAhead() {
        return urgentFeeAhead;
    }

    /**
     * 加急费用是否需要提前到账
     */
    public void setUrgentFeeAhead(String urgentFeeAhead) {
        this.urgentFeeAhead = urgentFeeAhead;
    }

    /**
     * 快审费用
     */
    public String getQuickFee() {
        return quickFee;
    }

    /**
     * 快审费用
     */
    public void setQuickFee(String quickFee) {
        this.quickFee = quickFee;
    }

    /**
     * 快审费用是否需要提前到账
     */
    public String getQuickFeeAhead() {
        return quickFeeAhead;
    }

    /**
     * 快审费用是否需要提前到账
     */
    public void setQuickFeeAhead(String quickFeeAhead) {
        this.quickFeeAhead = quickFeeAhead;
    }

    /**
     * 伦理付款账号
     */
    public String getPaymentCardNo() {
        return paymentCardNo;
    }

    /**
     * 伦理付款账号
     */
    public void setPaymentCardNo(String paymentCardNo) {
        this.paymentCardNo = paymentCardNo;
    }

    /**
     * 相关附件说明
     */
    public String getAttachmentRemark() {
        return attachmentRemark;
    }

    /**
     * 相关附件说明
     */
    public void setAttachmentRemark(String attachmentRemark) {
        this.attachmentRemark = attachmentRemark;
    }

    /**
     * 上会PPT模板及递交伦理相关文件说明
     */
    public String getPptUrlsRemark() {
        return pptUrlsRemark;
    }

    /**
     * 上会PPT模板及递交伦理相关文件说明
     */
    public void setPptUrlsRemark(String pptUrlsRemark) {
        this.pptUrlsRemark = pptUrlsRemark;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TCenterEthics other = (TCenterEthics) that;
        return (this.getEthicsId() == null ? other.getEthicsId() == null : this.getEthicsId().equals(other.getEthicsId()))
            && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()))
            && (this.getDeleteFlag() == null ? other.getDeleteFlag() == null : this.getDeleteFlag().equals(other.getDeleteFlag()))
            && (this.getCenterId() == null ? other.getCenterId() == null : this.getCenterId().equals(other.getCenterId()))
            && (this.getProcessTimeline() == null ? other.getProcessTimeline() == null : this.getProcessTimeline().equals(other.getProcessTimeline()))
            && (this.getWorkflow() == null ? other.getWorkflow() == null : this.getWorkflow().equals(other.getWorkflow()))
            && (this.getDownloadEthicsMaterials() == null ? other.getDownloadEthicsMaterials() == null : this.getDownloadEthicsMaterials().equals(other.getDownloadEthicsMaterials()))
            && (this.getEthicalApprovalRequirements() == null ? other.getEthicalApprovalRequirements() == null : this.getEthicalApprovalRequirements().equals(other.getEthicalApprovalRequirements()))
            && (this.getLeaderApproval() == null ? other.getLeaderApproval() == null : this.getLeaderApproval().equals(other.getLeaderApproval()))
            && (this.getWithPpt() == null ? other.getWithPpt() == null : this.getWithPpt().equals(other.getWithPpt()))
            && (this.getPptOutlines() == null ? other.getPptOutlines() == null : this.getPptOutlines().equals(other.getPptOutlines()))
            && (this.getAllowQuickVerify() == null ? other.getAllowQuickVerify() == null : this.getAllowQuickVerify().equals(other.getAllowQuickVerify()))
            && (this.getMeetingWait() == null ? other.getMeetingWait() == null : this.getMeetingWait().equals(other.getMeetingWait()))
            && (this.getMeetingWaitInfo() == null ? other.getMeetingWaitInfo() == null : this.getMeetingWaitInfo().equals(other.getMeetingWaitInfo()))
            && (this.getWaitTime() == null ? other.getWaitTime() == null : this.getWaitTime().equals(other.getWaitTime()))
            && (this.getCutLineWill() == null ? other.getCutLineWill() == null : this.getCutLineWill().equals(other.getCutLineWill()))
            && (this.getAllowAddlEc() == null ? other.getAllowAddlEc() == null : this.getAllowAddlEc().equals(other.getAllowAddlEc()))
            && (this.getPrstRqmt() == null ? other.getPrstRqmt() == null : this.getPrstRqmt().equals(other.getPrstRqmt()))
            && (this.getReviewPaymentRemark() == null ? other.getReviewPaymentRemark() == null : this.getReviewPaymentRemark().equals(other.getReviewPaymentRemark()))
            && (this.getSubscribe() == null ? other.getSubscribe() == null : this.getSubscribe().equals(other.getSubscribe()))
            && (this.getSubscribeType() == null ? other.getSubscribeType() == null : this.getSubscribeType().equals(other.getSubscribeType()))
            && (this.getSubscribeTypeInfo() == null ? other.getSubscribeTypeInfo() == null : this.getSubscribeTypeInfo().equals(other.getSubscribeTypeInfo()))
            && (this.getStaffAsk() == null ? other.getStaffAsk() == null : this.getStaffAsk().equals(other.getStaffAsk()))
            && (this.getSubscribeAsk() == null ? other.getSubscribeAsk() == null : this.getSubscribeAsk().equals(other.getSubscribeAsk()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getCreateBy() == null ? other.getCreateBy() == null : this.getCreateBy().equals(other.getCreateBy()))
            && (this.getUpdateBy() == null ? other.getUpdateBy() == null : this.getUpdateBy().equals(other.getUpdateBy()))
            && (this.getPlusStaffRemark() == null ? other.getPlusStaffRemark() == null : this.getPlusStaffRemark().equals(other.getPlusStaffRemark()))
            && (this.getReport() == null ? other.getReport() == null : this.getReport().equals(other.getReport()))
            && (this.getReportRemark() == null ? other.getReportRemark() == null : this.getReportRemark().equals(other.getReportRemark()))
            && (this.getReportAsk() == null ? other.getReportAsk() == null : this.getReportAsk().equals(other.getReportAsk()))
            && (this.getAccompany() == null ? other.getAccompany() == null : this.getAccompany().equals(other.getAccompany()))
            && (this.getAccompanyType() == null ? other.getAccompanyType() == null : this.getAccompanyType().equals(other.getAccompanyType()))
            && (this.getAccompanyRemark() == null ? other.getAccompanyRemark() == null : this.getAccompanyRemark().equals(other.getAccompanyRemark()))
            && (this.geteAuditType() == null ? other.geteAuditType() == null : this.geteAuditType().equals(other.geteAuditType()))
            && (this.getSystemAuditType() == null ? other.getSystemAuditType() == null : this.getSystemAuditType().equals(other.getSystemAuditType()))
            && (this.getSystemAuditLink() == null ? other.getSystemAuditLink() == null : this.getSystemAuditLink().equals(other.getSystemAuditLink()))
            && (this.getEmail() == null ? other.getEmail() == null : this.getEmail().equals(other.getEmail()))
            && (this.getEmailAuditAddress() == null ? other.getEmailAuditAddress() == null : this.getEmailAuditAddress().equals(other.getEmailAuditAddress()))
            && (this.getEmailReply() == null ? other.getEmailReply() == null : this.getEmailReply().equals(other.getEmailReply()))
            && (this.getDataSendRequire() == null ? other.getDataSendRequire() == null : this.getDataSendRequire().equals(other.getDataSendRequire()))
            && (this.getAuditRemark() == null ? other.getAuditRemark() == null : this.getAuditRemark().equals(other.getAuditRemark()))
            && (this.getEthicalApprovalRequirementsRemark() == null ? other.getEthicalApprovalRequirementsRemark() == null : this.getEthicalApprovalRequirementsRemark().equals(other.getEthicalApprovalRequirementsRemark()))
            && (this.getLeaderApprovalRemark() == null ? other.getLeaderApprovalRemark() == null : this.getLeaderApprovalRemark().equals(other.getLeaderApprovalRemark()))
            && (this.getRateRemark() == null ? other.getRateRemark() == null : this.getRateRemark().equals(other.getRateRemark()))
            && (this.getProjectNum() == null ? other.getProjectNum() == null : this.getProjectNum().equals(other.getProjectNum()))
            && (this.getAbutmentStaff() == null ? other.getAbutmentStaff() == null : this.getAbutmentStaff().equals(other.getAbutmentStaff()))
            && (this.getAbutmentStaffRemark() == null ? other.getAbutmentStaffRemark() == null : this.getAbutmentStaffRemark().equals(other.getAbutmentStaffRemark()))
            && (this.getPoint() == null ? other.getPoint() == null : this.getPoint().equals(other.getPoint()))
            && (this.getPlus() == null ? other.getPlus() == null : this.getPlus().equals(other.getPlus()))
            && (this.getPlusCondition() == null ? other.getPlusCondition() == null : this.getPlusCondition().equals(other.getPlusCondition()))
            && (this.getEthicalPre() == null ? other.getEthicalPre() == null : this.getEthicalPre().equals(other.getEthicalPre()))
            && (this.getEthicalPrecondition() == null ? other.getEthicalPrecondition() == null : this.getEthicalPrecondition().equals(other.getEthicalPrecondition()))
            && (this.getSubmit() == null ? other.getSubmit() == null : this.getSubmit().equals(other.getSubmit()))
            && (this.getDuration() == null ? other.getDuration() == null : this.getDuration().equals(other.getDuration()))
            && (this.getEcTime() == null ? other.getEcTime() == null : this.getEcTime().equals(other.getEcTime()))
            && (this.getAccessoryId() == null ? other.getAccessoryId() == null : this.getAccessoryId().equals(other.getAccessoryId()))
            && (this.getDataPreAudit() == null ? other.getDataPreAudit() == null : this.getDataPreAudit().equals(other.getDataPreAudit()))
            && (this.getPaperDataStaffRequire() == null ? other.getPaperDataStaffRequire() == null : this.getPaperDataStaffRequire().equals(other.getPaperDataStaffRequire()))
            && (this.getPreAudit() == null ? other.getPreAudit() == null : this.getPreAudit().equals(other.getPreAudit()))
            && (this.getDataType() == null ? other.getDataType() == null : this.getDataType().equals(other.getDataType()))
            && (this.getDataNum() == null ? other.getDataNum() == null : this.getDataNum().equals(other.getDataNum()))
            && (this.getSubmitStaff() == null ? other.getSubmitStaff() == null : this.getSubmitStaff().equals(other.getSubmitStaff()))
            && (this.getSubmitTime() == null ? other.getSubmitTime() == null : this.getSubmitTime().equals(other.getSubmitTime()))
            && (this.getBindAsk() == null ? other.getBindAsk() == null : this.getBindAsk().equals(other.getBindAsk()))
            && (this.getPpt() == null ? other.getPpt() == null : this.getPpt().equals(other.getPpt()))
            && (this.getPptUrls() == null ? other.getPptUrls() == null : this.getPptUrls().equals(other.getPptUrls()))
            && (this.getSubmitAddress() == null ? other.getSubmitAddress() == null : this.getSubmitAddress().equals(other.getSubmitAddress()))
            && (this.getAskRemark() == null ? other.getAskRemark() == null : this.getAskRemark().equals(other.getAskRemark()))
            && (this.getReviewPaymentDeadline() == null ? other.getReviewPaymentDeadline() == null : this.getReviewPaymentDeadline().equals(other.getReviewPaymentDeadline()))
            && (this.getReviewInvoice() == null ? other.getReviewInvoice() == null : this.getReviewInvoice().equals(other.getReviewInvoice()))
            && (this.getAttachment() == null ? other.getAttachment() == null : this.getAttachment().equals(other.getAttachment()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getAllowQuickVerifyRemark() == null ? other.getAllowQuickVerifyRemark() == null : this.getAllowQuickVerifyRemark().equals(other.getAllowQuickVerifyRemark()))
            && (this.getEthicsFreqDes() == null ? other.getEthicsFreqDes() == null : this.getEthicsFreqDes().equals(other.getEthicsFreqDes()))
            && (this.getCharge() == null ? other.getCharge() == null : this.getCharge().equals(other.getCharge()))
            && (this.getFirstInfluence() == null ? other.getFirstInfluence() == null : this.getFirstInfluence().equals(other.getFirstInfluence()))
            && (this.getFirstPaymentRemark() == null ? other.getFirstPaymentRemark() == null : this.getFirstPaymentRemark().equals(other.getFirstPaymentRemark()))
            && (this.getFirstPaymentDeadline() == null ? other.getFirstPaymentDeadline() == null : this.getFirstPaymentDeadline().equals(other.getFirstPaymentDeadline()))
            && (this.getFirstInvoice() == null ? other.getFirstInvoice() == null : this.getFirstInvoice().equals(other.getFirstInvoice()))
            && (this.getReviewFee() == null ? other.getReviewFee() == null : this.getReviewFee().equals(other.getReviewFee()))
            && (this.getAuditType() == null ? other.getAuditType() == null : this.getAuditType().equals(other.getAuditType()))
            && (this.getReviewInfluence() == null ? other.getReviewInfluence() == null : this.getReviewInfluence().equals(other.getReviewInfluence()))
            && (this.getMeetingPerCond() == null ? other.getMeetingPerCond() == null : this.getMeetingPerCond().equals(other.getMeetingPerCond()))
            && (this.getMeetingPerCondRemark() == null ? other.getMeetingPerCondRemark() == null : this.getMeetingPerCondRemark().equals(other.getMeetingPerCondRemark()))
            && (this.getEthicalMeetingRequire() == null ? other.getEthicalMeetingRequire() == null : this.getEthicalMeetingRequire().equals(other.getEthicalMeetingRequire()))
            && (this.getEcResultTime() == null ? other.getEcResultTime() == null : this.getEcResultTime().equals(other.getEcResultTime()))
            && (this.getOpenDay() == null ? other.getOpenDay() == null : this.getOpenDay().equals(other.getOpenDay()))
            && (this.getOpenDayRequire() == null ? other.getOpenDayRequire() == null : this.getOpenDayRequire().equals(other.getOpenDayRequire()))
            && (this.getEthicalDataAccountRequire() == null ? other.getEthicalDataAccountRequire() == null : this.getEthicalDataAccountRequire().equals(other.getEthicalDataAccountRequire()))
            && (this.getEthicalDataNetworkRequire() == null ? other.getEthicalDataNetworkRequire() == null : this.getEthicalDataNetworkRequire().equals(other.getEthicalDataNetworkRequire()))
            && (this.getOnlineAuditProcess() == null ? other.getOnlineAuditProcess() == null : this.getOnlineAuditProcess().equals(other.getOnlineAuditProcess()))
            && (this.getPaperData() == null ? other.getPaperData() == null : this.getPaperData().equals(other.getPaperData()))
            && (this.getNoCrcRemark() == null ? other.getNoCrcRemark() == null : this.getNoCrcRemark().equals(other.getNoCrcRemark()))
            && (this.getEthicsDataDeadline() == null ? other.getEthicsDataDeadline() == null : this.getEthicsDataDeadline().equals(other.getEthicsDataDeadline()))
            && (this.getDataSendRemark() == null ? other.getDataSendRemark() == null : this.getDataSendRemark().equals(other.getDataSendRemark()))
            && (this.getStampAsk() == null ? other.getStampAsk() == null : this.getStampAsk().equals(other.getStampAsk()))
            && (this.getEthicsAuditType() == null ? other.getEthicsAuditType() == null : this.getEthicsAuditType().equals(other.getEthicsAuditType()))
            && (this.getUrgentFee() == null ? other.getUrgentFee() == null : this.getUrgentFee().equals(other.getUrgentFee()))
            && (this.getUrgentFeeAhead() == null ? other.getUrgentFeeAhead() == null : this.getUrgentFeeAhead().equals(other.getUrgentFeeAhead()))
            && (this.getQuickFee() == null ? other.getQuickFee() == null : this.getQuickFee().equals(other.getQuickFee()))
            && (this.getQuickFeeAhead() == null ? other.getQuickFeeAhead() == null : this.getQuickFeeAhead().equals(other.getQuickFeeAhead()))
            && (this.getPaymentCardNo() == null ? other.getPaymentCardNo() == null : this.getPaymentCardNo().equals(other.getPaymentCardNo()))
            && (this.getAttachmentRemark() == null ? other.getAttachmentRemark() == null : this.getAttachmentRemark().equals(other.getAttachmentRemark()))
            && (this.getPptUrlsRemark() == null ? other.getPptUrlsRemark() == null : this.getPptUrlsRemark().equals(other.getPptUrlsRemark()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getEthicsId() == null) ? 0 : getEthicsId().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        result = prime * result + ((getDeleteFlag() == null) ? 0 : getDeleteFlag().hashCode());
        result = prime * result + ((getCenterId() == null) ? 0 : getCenterId().hashCode());
        result = prime * result + ((getProcessTimeline() == null) ? 0 : getProcessTimeline().hashCode());
        result = prime * result + ((getWorkflow() == null) ? 0 : getWorkflow().hashCode());
        result = prime * result + ((getDownloadEthicsMaterials() == null) ? 0 : getDownloadEthicsMaterials().hashCode());
        result = prime * result + ((getEthicalApprovalRequirements() == null) ? 0 : getEthicalApprovalRequirements().hashCode());
        result = prime * result + ((getLeaderApproval() == null) ? 0 : getLeaderApproval().hashCode());
        result = prime * result + ((getWithPpt() == null) ? 0 : getWithPpt().hashCode());
        result = prime * result + ((getPptOutlines() == null) ? 0 : getPptOutlines().hashCode());
        result = prime * result + ((getAllowQuickVerify() == null) ? 0 : getAllowQuickVerify().hashCode());
        result = prime * result + ((getMeetingWait() == null) ? 0 : getMeetingWait().hashCode());
        result = prime * result + ((getMeetingWaitInfo() == null) ? 0 : getMeetingWaitInfo().hashCode());
        result = prime * result + ((getWaitTime() == null) ? 0 : getWaitTime().hashCode());
        result = prime * result + ((getCutLineWill() == null) ? 0 : getCutLineWill().hashCode());
        result = prime * result + ((getAllowAddlEc() == null) ? 0 : getAllowAddlEc().hashCode());
        result = prime * result + ((getPrstRqmt() == null) ? 0 : getPrstRqmt().hashCode());
        result = prime * result + ((getReviewPaymentRemark() == null) ? 0 : getReviewPaymentRemark().hashCode());
        result = prime * result + ((getSubscribe() == null) ? 0 : getSubscribe().hashCode());
        result = prime * result + ((getSubscribeType() == null) ? 0 : getSubscribeType().hashCode());
        result = prime * result + ((getSubscribeTypeInfo() == null) ? 0 : getSubscribeTypeInfo().hashCode());
        result = prime * result + ((getStaffAsk() == null) ? 0 : getStaffAsk().hashCode());
        result = prime * result + ((getSubscribeAsk() == null) ? 0 : getSubscribeAsk().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getPlusStaffRemark() == null) ? 0 : getPlusStaffRemark().hashCode());
        result = prime * result + ((getReport() == null) ? 0 : getReport().hashCode());
        result = prime * result + ((getReportRemark() == null) ? 0 : getReportRemark().hashCode());
        result = prime * result + ((getReportAsk() == null) ? 0 : getReportAsk().hashCode());
        result = prime * result + ((getAccompany() == null) ? 0 : getAccompany().hashCode());
        result = prime * result + ((getAccompanyType() == null) ? 0 : getAccompanyType().hashCode());
        result = prime * result + ((getAccompanyRemark() == null) ? 0 : getAccompanyRemark().hashCode());
        result = prime * result + ((geteAuditType() == null) ? 0 : geteAuditType().hashCode());
        result = prime * result + ((getSystemAuditType() == null) ? 0 : getSystemAuditType().hashCode());
        result = prime * result + ((getSystemAuditLink() == null) ? 0 : getSystemAuditLink().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getEmailAuditAddress() == null) ? 0 : getEmailAuditAddress().hashCode());
        result = prime * result + ((getEmailReply() == null) ? 0 : getEmailReply().hashCode());
        result = prime * result + ((getDataSendRequire() == null) ? 0 : getDataSendRequire().hashCode());
        result = prime * result + ((getAuditRemark() == null) ? 0 : getAuditRemark().hashCode());
        result = prime * result + ((getEthicalApprovalRequirementsRemark() == null) ? 0 : getEthicalApprovalRequirementsRemark().hashCode());
        result = prime * result + ((getLeaderApprovalRemark() == null) ? 0 : getLeaderApprovalRemark().hashCode());
        result = prime * result + ((getRateRemark() == null) ? 0 : getRateRemark().hashCode());
        result = prime * result + ((getProjectNum() == null) ? 0 : getProjectNum().hashCode());
        result = prime * result + ((getAbutmentStaff() == null) ? 0 : getAbutmentStaff().hashCode());
        result = prime * result + ((getAbutmentStaffRemark() == null) ? 0 : getAbutmentStaffRemark().hashCode());
        result = prime * result + ((getPoint() == null) ? 0 : getPoint().hashCode());
        result = prime * result + ((getPlus() == null) ? 0 : getPlus().hashCode());
        result = prime * result + ((getPlusCondition() == null) ? 0 : getPlusCondition().hashCode());
        result = prime * result + ((getEthicalPre() == null) ? 0 : getEthicalPre().hashCode());
        result = prime * result + ((getEthicalPrecondition() == null) ? 0 : getEthicalPrecondition().hashCode());
        result = prime * result + ((getSubmit() == null) ? 0 : getSubmit().hashCode());
        result = prime * result + ((getDuration() == null) ? 0 : getDuration().hashCode());
        result = prime * result + ((getEcTime() == null) ? 0 : getEcTime().hashCode());
        result = prime * result + ((getAccessoryId() == null) ? 0 : getAccessoryId().hashCode());
        result = prime * result + ((getDataPreAudit() == null) ? 0 : getDataPreAudit().hashCode());
        result = prime * result + ((getPaperDataStaffRequire() == null) ? 0 : getPaperDataStaffRequire().hashCode());
        result = prime * result + ((getPreAudit() == null) ? 0 : getPreAudit().hashCode());
        result = prime * result + ((getDataType() == null) ? 0 : getDataType().hashCode());
        result = prime * result + ((getDataNum() == null) ? 0 : getDataNum().hashCode());
        result = prime * result + ((getSubmitStaff() == null) ? 0 : getSubmitStaff().hashCode());
        result = prime * result + ((getSubmitTime() == null) ? 0 : getSubmitTime().hashCode());
        result = prime * result + ((getBindAsk() == null) ? 0 : getBindAsk().hashCode());
        result = prime * result + ((getPpt() == null) ? 0 : getPpt().hashCode());
        result = prime * result + ((getPptUrls() == null) ? 0 : getPptUrls().hashCode());
        result = prime * result + ((getSubmitAddress() == null) ? 0 : getSubmitAddress().hashCode());
        result = prime * result + ((getAskRemark() == null) ? 0 : getAskRemark().hashCode());
        result = prime * result + ((getReviewPaymentDeadline() == null) ? 0 : getReviewPaymentDeadline().hashCode());
        result = prime * result + ((getReviewInvoice() == null) ? 0 : getReviewInvoice().hashCode());
        result = prime * result + ((getAttachment() == null) ? 0 : getAttachment().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getAllowQuickVerifyRemark() == null) ? 0 : getAllowQuickVerifyRemark().hashCode());
        result = prime * result + ((getEthicsFreqDes() == null) ? 0 : getEthicsFreqDes().hashCode());
        result = prime * result + ((getCharge() == null) ? 0 : getCharge().hashCode());
        result = prime * result + ((getFirstInfluence() == null) ? 0 : getFirstInfluence().hashCode());
        result = prime * result + ((getFirstPaymentRemark() == null) ? 0 : getFirstPaymentRemark().hashCode());
        result = prime * result + ((getFirstPaymentDeadline() == null) ? 0 : getFirstPaymentDeadline().hashCode());
        result = prime * result + ((getFirstInvoice() == null) ? 0 : getFirstInvoice().hashCode());
        result = prime * result + ((getReviewFee() == null) ? 0 : getReviewFee().hashCode());
        result = prime * result + ((getAuditType() == null) ? 0 : getAuditType().hashCode());
        result = prime * result + ((getReviewInfluence() == null) ? 0 : getReviewInfluence().hashCode());
        result = prime * result + ((getMeetingPerCond() == null) ? 0 : getMeetingPerCond().hashCode());
        result = prime * result + ((getMeetingPerCondRemark() == null) ? 0 : getMeetingPerCondRemark().hashCode());
        result = prime * result + ((getEthicalMeetingRequire() == null) ? 0 : getEthicalMeetingRequire().hashCode());
        result = prime * result + ((getEcResultTime() == null) ? 0 : getEcResultTime().hashCode());
        result = prime * result + ((getOpenDay() == null) ? 0 : getOpenDay().hashCode());
        result = prime * result + ((getOpenDayRequire() == null) ? 0 : getOpenDayRequire().hashCode());
        result = prime * result + ((getEthicalDataAccountRequire() == null) ? 0 : getEthicalDataAccountRequire().hashCode());
        result = prime * result + ((getEthicalDataNetworkRequire() == null) ? 0 : getEthicalDataNetworkRequire().hashCode());
        result = prime * result + ((getOnlineAuditProcess() == null) ? 0 : getOnlineAuditProcess().hashCode());
        result = prime * result + ((getPaperData() == null) ? 0 : getPaperData().hashCode());
        result = prime * result + ((getNoCrcRemark() == null) ? 0 : getNoCrcRemark().hashCode());
        result = prime * result + ((getEthicsDataDeadline() == null) ? 0 : getEthicsDataDeadline().hashCode());
        result = prime * result + ((getDataSendRemark() == null) ? 0 : getDataSendRemark().hashCode());
        result = prime * result + ((getStampAsk() == null) ? 0 : getStampAsk().hashCode());
        result = prime * result + ((getEthicsAuditType() == null) ? 0 : getEthicsAuditType().hashCode());
        result = prime * result + ((getUrgentFee() == null) ? 0 : getUrgentFee().hashCode());
        result = prime * result + ((getUrgentFeeAhead() == null) ? 0 : getUrgentFeeAhead().hashCode());
        result = prime * result + ((getQuickFee() == null) ? 0 : getQuickFee().hashCode());
        result = prime * result + ((getQuickFeeAhead() == null) ? 0 : getQuickFeeAhead().hashCode());
        result = prime * result + ((getPaymentCardNo() == null) ? 0 : getPaymentCardNo().hashCode());
        result = prime * result + ((getAttachmentRemark() == null) ? 0 : getAttachmentRemark().hashCode());
        result = prime * result + ((getPptUrlsRemark() == null) ? 0 : getPptUrlsRemark().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", ethicsId=").append(ethicsId);
        sb.append(", isDelete=").append(isDelete);
        sb.append(", deleteFlag=").append(deleteFlag);
        sb.append(", centerId=").append(centerId);
        sb.append(", processTimeline=").append(processTimeline);
        sb.append(", workflow=").append(workflow);
        sb.append(", downloadEthicsMaterials=").append(downloadEthicsMaterials);
        sb.append(", ethicalApprovalRequirements=").append(ethicalApprovalRequirements);
        sb.append(", leaderApproval=").append(leaderApproval);
        sb.append(", withPpt=").append(withPpt);
        sb.append(", pptOutlines=").append(pptOutlines);
        sb.append(", allowQuickVerify=").append(allowQuickVerify);
        sb.append(", meetingWait=").append(meetingWait);
        sb.append(", meetingWaitInfo=").append(meetingWaitInfo);
        sb.append(", waitTime=").append(waitTime);
        sb.append(", cutLineWill=").append(cutLineWill);
        sb.append(", allowAddlEc=").append(allowAddlEc);
        sb.append(", prstRqmt=").append(prstRqmt);
        sb.append(", reviewPaymentRemark=").append(reviewPaymentRemark);
        sb.append(", subscribe=").append(subscribe);
        sb.append(", subscribeType=").append(subscribeType);
        sb.append(", subscribeTypeInfo=").append(subscribeTypeInfo);
        sb.append(", staffAsk=").append(staffAsk);
        sb.append(", subscribeAsk=").append(subscribeAsk);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", plusStaffRemark=").append(plusStaffRemark);
        sb.append(", report=").append(report);
        sb.append(", reportRemark=").append(reportRemark);
        sb.append(", reportAsk=").append(reportAsk);
        sb.append(", accompany=").append(accompany);
        sb.append(", accompanyType=").append(accompanyType);
        sb.append(", accompanyRemark=").append(accompanyRemark);
        sb.append(", eAuditType=").append(eAuditType);
        sb.append(", systemAuditType=").append(systemAuditType);
        sb.append(", systemAuditLink=").append(systemAuditLink);
        sb.append(", email=").append(email);
        sb.append(", emailAuditAddress=").append(emailAuditAddress);
        sb.append(", emailReply=").append(emailReply);
        sb.append(", dataSendRequire=").append(dataSendRequire);
        sb.append(", auditRemark=").append(auditRemark);
        sb.append(", ethicalApprovalRequirementsRemark=").append(ethicalApprovalRequirementsRemark);
        sb.append(", leaderApprovalRemark=").append(leaderApprovalRemark);
        sb.append(", rateRemark=").append(rateRemark);
        sb.append(", projectNum=").append(projectNum);
        sb.append(", abutmentStaff=").append(abutmentStaff);
        sb.append(", abutmentStaffRemark=").append(abutmentStaffRemark);
        sb.append(", point=").append(point);
        sb.append(", plus=").append(plus);
        sb.append(", plusCondition=").append(plusCondition);
        sb.append(", ethicalPre=").append(ethicalPre);
        sb.append(", ethicalPrecondition=").append(ethicalPrecondition);
        sb.append(", submit=").append(submit);
        sb.append(", duration=").append(duration);
        sb.append(", ecTime=").append(ecTime);
        sb.append(", accessoryId=").append(accessoryId);
        sb.append(", dataPreAudit=").append(dataPreAudit);
        sb.append(", paperDataStaffRequire=").append(paperDataStaffRequire);
        sb.append(", preAudit=").append(preAudit);
        sb.append(", dataType=").append(dataType);
        sb.append(", dataNum=").append(dataNum);
        sb.append(", submitStaff=").append(submitStaff);
        sb.append(", submitTime=").append(submitTime);
        sb.append(", bindAsk=").append(bindAsk);
        sb.append(", ppt=").append(ppt);
        sb.append(", pptUrls=").append(pptUrls);
        sb.append(", submitAddress=").append(submitAddress);
        sb.append(", askRemark=").append(askRemark);
        sb.append(", reviewPaymentDeadline=").append(reviewPaymentDeadline);
        sb.append(", reviewInvoice=").append(reviewInvoice);
        sb.append(", attachment=").append(attachment);
        sb.append(", remark=").append(remark);
        sb.append(", allowQuickVerifyRemark=").append(allowQuickVerifyRemark);
        sb.append(", ethicsFreqDes=").append(ethicsFreqDes);
        sb.append(", charge=").append(charge);
        sb.append(", firstInfluence=").append(firstInfluence);
        sb.append(", firstPaymentRemark=").append(firstPaymentRemark);
        sb.append(", firstPaymentDeadline=").append(firstPaymentDeadline);
        sb.append(", firstInvoice=").append(firstInvoice);
        sb.append(", reviewFee=").append(reviewFee);
        sb.append(", auditType=").append(auditType);
        sb.append(", reviewInfluence=").append(reviewInfluence);
        sb.append(", meetingPerCond=").append(meetingPerCond);
        sb.append(", meetingPerCondRemark=").append(meetingPerCondRemark);
        sb.append(", ethicalMeetingRequire=").append(ethicalMeetingRequire);
        sb.append(", ecResultTime=").append(ecResultTime);
        sb.append(", openDay=").append(openDay);
        sb.append(", openDayRequire=").append(openDayRequire);
        sb.append(", ethicalDataAccountRequire=").append(ethicalDataAccountRequire);
        sb.append(", ethicalDataNetworkRequire=").append(ethicalDataNetworkRequire);
        sb.append(", onlineAuditProcess=").append(onlineAuditProcess);
        sb.append(", paperData=").append(paperData);
        sb.append(", noCrcRemark=").append(noCrcRemark);
        sb.append(", ethicsDataDeadline=").append(ethicsDataDeadline);
        sb.append(", dataSendRemark=").append(dataSendRemark);
        sb.append(", stampAsk=").append(stampAsk);
        sb.append(", ethicsAuditType=").append(ethicsAuditType);
        sb.append(", urgentFee=").append(urgentFee);
        sb.append(", urgentFeeAhead=").append(urgentFeeAhead);
        sb.append(", quickFee=").append(quickFee);
        sb.append(", quickFeeAhead=").append(quickFeeAhead);
        sb.append(", paymentCardNo=").append(paymentCardNo);
        sb.append(", attachmentRemark=").append(attachmentRemark);
        sb.append(", pptUrlsRemark=").append(pptUrlsRemark);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}