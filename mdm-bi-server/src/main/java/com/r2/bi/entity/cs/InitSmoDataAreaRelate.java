package com.r2.bi.entity.cs;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * SMO数据初始化表
 * @TableName init_smo_data_area_relate
 */
@TableName(value ="init_smo_data_area_relate")
public class InitSmoDataAreaRelate implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * cta
     */
    private String cta;

    /**
     * 部门
     */
    private String dept;

    /**
     * 区域对接负责人
     */
    private String director;

    /**
     * 组别
     */
    private String cohort;

    /**
     * 负责省区
     */
    private String province;

    /**
     * 省对接负责人
     */
    private String provinceDirector;

    /**
     * tl
     */
    private String tl;

    /**
     * 负责城市
     */
    private String city;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    public Integer getId() {
        return id;
    }

    /**
     * 主键id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 区域名称
     */
    public String getAreaName() {
        return areaName;
    }

    /**
     * 区域名称
     */
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    /**
     * cta
     */
    public String getCta() {
        return cta;
    }

    /**
     * cta
     */
    public void setCta(String cta) {
        this.cta = cta;
    }

    /**
     * 部门
     */
    public String getDept() {
        return dept;
    }

    /**
     * 部门
     */
    public void setDept(String dept) {
        this.dept = dept;
    }

    /**
     * 区域对接负责人
     */
    public String getDirector() {
        return director;
    }

    /**
     * 区域对接负责人
     */
    public void setDirector(String director) {
        this.director = director;
    }

    /**
     * 组别
     */
    public String getCohort() {
        return cohort;
    }

    /**
     * 组别
     */
    public void setCohort(String cohort) {
        this.cohort = cohort;
    }

    /**
     * 负责省区
     */
    public String getProvince() {
        return province;
    }

    /**
     * 负责省区
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * 省对接负责人
     */
    public String getProvinceDirector() {
        return provinceDirector;
    }

    /**
     * 省对接负责人
     */
    public void setProvinceDirector(String provinceDirector) {
        this.provinceDirector = provinceDirector;
    }

    /**
     * tl
     */
    public String getTl() {
        return tl;
    }

    /**
     * tl
     */
    public void setTl(String tl) {
        this.tl = tl;
    }

    /**
     * 负责城市
     */
    public String getCity() {
        return city;
    }

    /**
     * 负责城市
     */
    public void setCity(String city) {
        this.city = city;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        InitSmoDataAreaRelate other = (InitSmoDataAreaRelate) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAreaName() == null ? other.getAreaName() == null : this.getAreaName().equals(other.getAreaName()))
            && (this.getCta() == null ? other.getCta() == null : this.getCta().equals(other.getCta()))
            && (this.getDept() == null ? other.getDept() == null : this.getDept().equals(other.getDept()))
            && (this.getDirector() == null ? other.getDirector() == null : this.getDirector().equals(other.getDirector()))
            && (this.getCohort() == null ? other.getCohort() == null : this.getCohort().equals(other.getCohort()))
            && (this.getProvince() == null ? other.getProvince() == null : this.getProvince().equals(other.getProvince()))
            && (this.getProvinceDirector() == null ? other.getProvinceDirector() == null : this.getProvinceDirector().equals(other.getProvinceDirector()))
            && (this.getTl() == null ? other.getTl() == null : this.getTl().equals(other.getTl()))
            && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAreaName() == null) ? 0 : getAreaName().hashCode());
        result = prime * result + ((getCta() == null) ? 0 : getCta().hashCode());
        result = prime * result + ((getDept() == null) ? 0 : getDept().hashCode());
        result = prime * result + ((getDirector() == null) ? 0 : getDirector().hashCode());
        result = prime * result + ((getCohort() == null) ? 0 : getCohort().hashCode());
        result = prime * result + ((getProvince() == null) ? 0 : getProvince().hashCode());
        result = prime * result + ((getProvinceDirector() == null) ? 0 : getProvinceDirector().hashCode());
        result = prime * result + ((getTl() == null) ? 0 : getTl().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", areaName=").append(areaName);
        sb.append(", cta=").append(cta);
        sb.append(", dept=").append(dept);
        sb.append(", director=").append(director);
        sb.append(", cohort=").append(cohort);
        sb.append(", province=").append(province);
        sb.append(", provinceDirector=").append(provinceDirector);
        sb.append(", tl=").append(tl);
        sb.append(", city=").append(city);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}