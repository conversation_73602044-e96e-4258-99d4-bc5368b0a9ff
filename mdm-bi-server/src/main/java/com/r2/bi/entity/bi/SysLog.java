package com.r2.bi.entity.bi;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统日志(SysLog)表实体类
 *
 * <AUTHOR>
 * @since 2022-10-21 10:04:10
 */
@SuppressWarnings("serial")
@TableName("sys_log")
@Data
public class SysLog extends Model<SysLog> {
    //ID
    @TableId(type = IdType.AUTO)
    private Long id;
    //模块名称
    private String moduleName;
    //操作类型
    private String operateType;
    //操作状态
    private Integer operateStatus;
    //描述
    private String description;
    //日志类型
    private String logType;
    //请求方式
    private String method;
    //请求参数
    private String params;
    //请求ip
    private String requestIp;
    //请求uri
    private String requestUri;
    //日志时间戳
    private Long time;
    //用户名
    private String username;
    //访问地址
    private String address;
    //响应结果
    private String exceptionDetail;
    //创建时间
    private LocalDateTime createTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.id;
    }
    }

