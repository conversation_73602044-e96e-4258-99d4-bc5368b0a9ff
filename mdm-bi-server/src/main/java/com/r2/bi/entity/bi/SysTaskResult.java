package com.r2.bi.entity.bi;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 定时任务执行结果表(SysTaskResult)实体类
 *
 * <AUTHOR>
 * @since 2023-01-03 16:43:07
 */
@Data
@TableName("t_sys_task_result")
public class SysTaskResult implements Serializable {
    private static final long serialVersionUID = -34476323524413287L;
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 执行结果描述
     */
    private String result;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}

