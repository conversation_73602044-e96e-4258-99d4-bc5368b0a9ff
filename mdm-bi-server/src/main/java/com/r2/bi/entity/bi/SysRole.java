package com.r2.bi.entity.bi;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 角色表(Role)表实体类
 *
 * <AUTHOR>
 * @since 2022-10-21 09:55:47
 */
@SuppressWarnings("serial")
@TableName("t_role")
@Data
public class SysRole extends Model<SysRole> {
    //ID
    @TableId(type = IdType.AUTO)
    private Long id;
    //名称
    private String name;
    //编码
    private String code;
    //角色级别
    private Integer level;
    //描述
    private String description;
    //数据权限
    private String dataScope;
    //接口状态 1成功 2失败
    private Integer status;
    //创建者
    private String createBy;
    //更新者
    private String updateBy;
    //创建日期
    private LocalDateTime createTime;
    //更新时间
    private LocalDateTime updateTime;
    //是否删除：1：是；0：否
    private Integer isDelete;
    //uuid
    private String deleteFlag;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.id;
    }
    }

