package com.r2.bi.entity.dc;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 中心基本信息(Center)表实体类
 *
 * <AUTHOR>
 * @since 2022-11-09 10:17:13
 */
@TableName("center")
@Data
@SuppressWarnings("serial")
public class Center extends Model<Center> {
    //中心id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //是否删除 0未删除 1删除
    @TableField("is_delete")
    private Boolean isDelete;

    @TableField(value = "dateCreated", fill = FieldFill.INSERT)
    private Date datecreated;
    private Date datemodified;
    //中心标准名称
    private String centerName;
    //中心标准编码
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String standardCode;
    //中心性质
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String type;
    //中心等级
    @TableField(value = "class",updateStrategy = FieldStrategy.IGNORED)
    private String class1;
    //省
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String province;
    //市
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String city;
    //区
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String area;
    //地址
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String address;
    //网址
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String url;
    //总机电话
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String tel;
    //数据来源
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String source;
    //状态
    private String status;
    //是否中心（1中心 0医院）
    private String isCenter;

    /**
     * 中心别名
     */
    private String alias;
    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    public String getMdmField(String fieldName) {
        switch (fieldName) {
            case "ID":
                return id.toString();
            case "中心标准编码":
                return standardCode;
            case "中心标准名称":
                return centerName;
            case "中心别名":
                return alias;
            case "详细地址":
                return address;
            // 其他字段...
            default:
                throw new IllegalArgumentException("Unknown field: " + fieldName);
        }
    }

    }

