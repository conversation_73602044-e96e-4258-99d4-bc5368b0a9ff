package com.r2.bi.entity.cs;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (DataAreaRelateReport)实体类
 *
 * <AUTHOR>
 * @since 2023-05-30 11:31:26
 */
@Data
@TableName("t_data_area_relate_report")
public class DataAreaRelateReport implements Serializable {
    private static final long serialVersionUID = -11146733681820220L;
    /**
     * 主键id
     */
    private Long id;
    /**
     * 中心id
     */
    private Long centerId;

    /**
     * 区域id
     */
    private Integer areaId;
    /**
     * 区域名称
     */
    private String areaName;

    /**
     * v1.1 区域中心完整度 5模块
     */
    private String areaCenterIntegrity;

    /**
     * v1.1 区域中心无效数据占比 5模块
     */
    private String areaInvalidData;

    /**
     * v1.2 区域中心完整度 7模块
     */
    private String areaCenterIntegrityPlus;

    /**
     * v1.2 区域中心无效数据占比 7模块
     */
    private String areaInvalidDataPlus;

    /**
     * 中心名称
     */
    private String centerName;
    /**
     * 城市
     */
    private String city;
    /**
     * cta
     */
    private String cta;
    /**
     * 部门
     */
    private String dept;
    /**
     * 区域对接负责人
     */
    private String director;
    /**
     * 组别
     */
    private String groups;
    /**
     * 省
     */
    private String province;
    /**
     * 省对接负责人
     */
    private String provinceDirector;
    /**
     * tl
     */
    private String tl;

    /**
     * 中心省份
     */
    private String centerProvince;

    /**
     * 中心城市
     */
    private String centerCity;

    /**
     * 更新人
     */
    private String updator;

    /**
     * 更新人是否与tl一致
     */
    private Integer updatorIsTl;

    /**
     * 中心最新更新时间
     */
    private String centerUpdateTime;

    /**
     * smo更新人登录次数 2023年
     */
    private Integer smoUpdatorLoginNum;

    /**
     * smo更新人最后登录时间
     */
    private String smoUpdatorLoginTime;

    /**
     * 中心字段填写完整度 v1.1 5模块
     */
    private String centerIntegrity;
    /**
     * v1.1中心无效数据占比
     */
    private String centerInvalidData;

    /**
     * 中心字段填写完整度 v1.2 7模块
     */
    private String centerIntegrityPlus;

    /**
     * v1.2中心无效数据占比
     */
//    @ExcelProperty("v1.2中心无效数据占比")
    private String centerInvalidDataPlus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 中心别名
     */
    private String alias;

    /**
     * 中心标准编码
     */
    private String standardCode;

    /**
     * 中心详细地址
     */
    private String centerAddress;

    /**
     * 中心科室数量
     */
//    @ExcelProperty("中心科室数量")
    private String deptSize;

    /**
     * 中心科室数据完整度
     */
//    @ExcelProperty("中心科室数据完整度")
    private String deptCenterIntegrity;

    /**
     * 中心科室无效数据占比
     */
//    @ExcelProperty("中心科室无效数据占比")
    private String invalidDeptIntegrity;

    /**
     * 中心人员数量
     */
//    @ExcelProperty("中心人员数量")
    private String memberSize;


    /**
     * 中心人员数据完整度
     */
//    @ExcelProperty("中心人员数据完整度")
    private String memberCenterIntegrity;
    /**
     * 中心人员无效数据占比
     */
//    @ExcelProperty("中心人员无效数据占比")
    private String invalidMemberIntegrity;
}

