package com.r2.bi.entity.bi;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("t_user")
public class BiUser extends Model<BiUser> {
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //中台用户id
    private Long userId;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String jobNumber;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String mobile;

    private String username;

    private String userAccount;

    //密码
    private String password;
    //盐
    private String salt;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String organizeCode;

    /**
     * 直属领导 姓名
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String poiDempAdminName;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String email;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String dutyName;

    //是否删除：0：未删除；1：已删除
    @TableField(value = "is_delete")
    private Boolean isDelete;
    //创建时间
    private Date createTime;
    //更新时间
    private Date updateTime;

    private String searchKey;

    /**
     * 所有数据权限：1：是；0：否
     */
    private Integer dataPermission;

    /**
     * 来源【1：内部用户；2：外部用户】
     */
    private Integer sourceType;

    /**
     * 重置日期
     */
    private String resetDay;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}