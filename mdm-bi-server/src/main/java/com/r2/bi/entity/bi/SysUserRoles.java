package com.r2.bi.entity.bi;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

/**
 * (SysUserRoles)表实体类
 *
 * <AUTHOR>
 * @since 2022-10-31 20:22:13
 */
@SuppressWarnings("serial")
@TableName("t_sys_user_roles")
public class SysUserRoles extends Model<SysUserRoles> {

    private Long roleId;

    private Long userId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

}

