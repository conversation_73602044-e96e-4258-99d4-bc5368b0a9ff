package com.r2.bi.entity.dc;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员(SysUser)表实体类
 *
 * <AUTHOR>
 * @since 2022-10-21 10:07:35
 */
@SuppressWarnings("serial")
@Data
@TableName("user")
public class SysUser extends Model<SysUser> {
    //主键id
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //中台用户id
    private Long userid;

    private Date resetDay;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String jobNumber;

    private String username;

    private String userAccount;

    private Integer status;

    //密码
    private String password;
    //盐
    private String salt;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String organizeCode;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String secondLevelOrgCode;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer organizeStatus;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String positionCode;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long poiDempAdmin;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String superiorEmployeeCode;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String email;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String mobile;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String onBoarding;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String dutyName;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer sex;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String qq;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String wechat;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String birthday;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String idCard;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String addr;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer employeeStatus;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String isCurrentRecord;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer employType;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date updateDay;
    //是否删除：0：未删除；1：已删除
    @TableField(value = "is_delete")
    private Boolean isDelete;
    //数据权限：0：无；1：有
    @TableField(value = "data_permission")
    private Boolean dataPermission;
    //创建时间
    private Date createTime;
    //更新时间
    private Date updateTime;

    private String searchKey;

    private String deleteFlag;

    private Integer sourceType;

    private Integer autoEdit;


    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}

