package com.r2.bi.entity.cs;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * 启动（新增）
 * @TableName t_center_start_up
 */
@TableName(value ="t_center_start_up")
public class TCenterStartUp implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 是否删除 0未删除 1删除
     */
    private Integer isDelete;

    /**
     * 删除标识 
     */
    private String deleteFlag;

    /**
     * 
     */
    private Date datecreated;

    /**
     * 
     */
    private Date datemodified;

    /**
     * 
     */
    private String createdby;

    /**
     * 
     */
    private String createdbyname;

    /**
     * 
     */
    private String modifiedby;

    /**
     * 
     */
    private String modifiedbyname;

    /**
     * 中心id
     */
    private Long centerId;

    /**
     * 启动会有要求
     */
    private String kickoffRqmt;

    /**
     * 启动会补充说明
     */
    private String kickoffAddlInfo;

    /**
     * 中心启动要求
     */
    private String centerRqmt;

    /**
     * 启动后筛选受试者前置条件
     */
    private String subjectPrecondition;

    /**
     * 安全事件-伦理规定模版
     */
    private String safetyEthicsTemplate;

    /**
     * 安全事件-审查频率及要求
     */
    private String safetyFreqRqmt;

    /**
     * 安全事件-审查方式
     */
    private String safetyReviewMethod;

    /**
     * 安全事件-审查方式补充说明
     */
    private String safetyReviewAddlInfo;

    /**
     * 安全事件-是否会审
     */
    private String safetyJoint;

    /**
     * 安全事件-费用
     */
    private String safetyFee;

    /**
     * 安全事件-打款备注
     */
    private String safetyPaymentRemarks;

    /**
     * 安全事件-打款时限
     */
    private String safetyPayLimit;

    /**
     * 安全事件-发票领取时间及地址
     */
    private String safetyInvoice;

    /**
     * 安全事件-递交时间
     */
    private String safetySubmitTime;

    /**
     * 安全事件-递交人员
     */
    private String safetySubmitor;

    /**
     * 安全事件-补充说明
     */
    private String safetyAddlInfo;

    /**
     * 定期跟踪审查-伦理规定模版
     */
    private String periodiethicsTemplate;

    /**
     * 定期跟踪审查-审查频率及要求
     */
    private String periodifreqRqmt;

    /**
     * 定期跟踪审查-结题流程要求
     */
    private String periodiconclusionRqmt;

    /**
     * 定期跟踪审查-材料递交要求
     */
    private String periodimaterialRqmt;

    /**
     * 定期跟踪审查-审查方式
     */
    private String periodireviewMethod;

    /**
     * 定期跟踪审查-审查方式补充说明
     */
    private String periodireviewAddlInfo;

    /**
     * 定期跟踪审查-是否会审
     */
    private String periodijoint;

    /**
     * 定期跟踪审查-费用
     */
    private String periodifee;

    /**
     * 定期跟踪审查-打款备注
     */
    private String periodipaymentRemarks;

    /**
     * 定期跟踪审查-需要答辩
     */
    private String periodidefense;

    /**
     * 定期跟踪审查-答辩要求
     */
    private String periodidefenseRqmt;

    /**
     * 定期跟踪审查-意见跟进和反馈
     */
    private String periodifeedback;

    /**
     * 定期跟踪审查-打款时限
     */
    private String periodipayLimit;

    /**
     * 定期跟踪审查-发票领取时间及地址
     */
    private String periodiinvoice;

    /**
     * 定期跟踪审查-递交时间
     */
    private String periodisubmitTime;

    /**
     * 定期跟踪审查-补充说明
     */
    private String periodiaddlInfo;

    /**
     * 结题审查-伦理规定模版
     */
    private String conclusionEthicsTemplate;

    /**
     * 结题审查-审查频率及要求
     */
    private String conclusionFreqRqmt;

    /**
     * 结题审查-结题流程要求
     */
    private String conclusionConclusionRqmt;

    /**
     * 结题审查-材料递交要求
     */
    private String conclusionMaterialRqmt;

    /**
     * 结题审查-审查方式
     */
    private String conclusionReviewMethod;

    /**
     * 结题审查-审查方式补充说明
     */
    private String conclusionReviewAddlInfo;

    /**
     * 结题审查-需要答辩
     */
    private String conclusionDefense;

    /**
     * 结题审查-答辩要求
     */
    private String conclusionDefenseRqmt;

    /**
     * 结题审查-意见跟进和反馈
     */
    private String conclusionFeedback;

    /**
     * 结题审查-递交时间
     */
    private String conclusionSubmitTime;

    /**
     * 结题审查-补充说明
     */
    private String conclusionAddlInfo;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 是否删除 0未删除 1删除
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * 是否删除 0未删除 1删除
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 删除标识 
     */
    public String getDeleteFlag() {
        return deleteFlag;
    }

    /**
     * 删除标识 
     */
    public void setDeleteFlag(String deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    /**
     * 
     */
    public Date getDatecreated() {
        return datecreated;
    }

    /**
     * 
     */
    public void setDatecreated(Date datecreated) {
        this.datecreated = datecreated;
    }

    /**
     * 
     */
    public Date getDatemodified() {
        return datemodified;
    }

    /**
     * 
     */
    public void setDatemodified(Date datemodified) {
        this.datemodified = datemodified;
    }

    /**
     * 
     */
    public String getCreatedby() {
        return createdby;
    }

    /**
     * 
     */
    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    /**
     * 
     */
    public String getCreatedbyname() {
        return createdbyname;
    }

    /**
     * 
     */
    public void setCreatedbyname(String createdbyname) {
        this.createdbyname = createdbyname;
    }

    /**
     * 
     */
    public String getModifiedby() {
        return modifiedby;
    }

    /**
     * 
     */
    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    /**
     * 
     */
    public String getModifiedbyname() {
        return modifiedbyname;
    }

    /**
     * 
     */
    public void setModifiedbyname(String modifiedbyname) {
        this.modifiedbyname = modifiedbyname;
    }

    /**
     * 中心id
     */
    public Long getCenterId() {
        return centerId;
    }

    /**
     * 中心id
     */
    public void setCenterId(Long centerId) {
        this.centerId = centerId;
    }

    /**
     * 启动会有要求
     */
    public String getKickoffRqmt() {
        return kickoffRqmt;
    }

    /**
     * 启动会有要求
     */
    public void setKickoffRqmt(String kickoffRqmt) {
        this.kickoffRqmt = kickoffRqmt;
    }

    /**
     * 启动会补充说明
     */
    public String getKickoffAddlInfo() {
        return kickoffAddlInfo;
    }

    /**
     * 启动会补充说明
     */
    public void setKickoffAddlInfo(String kickoffAddlInfo) {
        this.kickoffAddlInfo = kickoffAddlInfo;
    }

    /**
     * 中心启动要求
     */
    public String getCenterRqmt() {
        return centerRqmt;
    }

    /**
     * 中心启动要求
     */
    public void setCenterRqmt(String centerRqmt) {
        this.centerRqmt = centerRqmt;
    }

    /**
     * 启动后筛选受试者前置条件
     */
    public String getSubjectPrecondition() {
        return subjectPrecondition;
    }

    /**
     * 启动后筛选受试者前置条件
     */
    public void setSubjectPrecondition(String subjectPrecondition) {
        this.subjectPrecondition = subjectPrecondition;
    }

    /**
     * 安全事件-伦理规定模版
     */
    public String getSafetyEthicsTemplate() {
        return safetyEthicsTemplate;
    }

    /**
     * 安全事件-伦理规定模版
     */
    public void setSafetyEthicsTemplate(String safetyEthicsTemplate) {
        this.safetyEthicsTemplate = safetyEthicsTemplate;
    }

    /**
     * 安全事件-审查频率及要求
     */
    public String getSafetyFreqRqmt() {
        return safetyFreqRqmt;
    }

    /**
     * 安全事件-审查频率及要求
     */
    public void setSafetyFreqRqmt(String safetyFreqRqmt) {
        this.safetyFreqRqmt = safetyFreqRqmt;
    }

    /**
     * 安全事件-审查方式
     */
    public String getSafetyReviewMethod() {
        return safetyReviewMethod;
    }

    /**
     * 安全事件-审查方式
     */
    public void setSafetyReviewMethod(String safetyReviewMethod) {
        this.safetyReviewMethod = safetyReviewMethod;
    }

    /**
     * 安全事件-审查方式补充说明
     */
    public String getSafetyReviewAddlInfo() {
        return safetyReviewAddlInfo;
    }

    /**
     * 安全事件-审查方式补充说明
     */
    public void setSafetyReviewAddlInfo(String safetyReviewAddlInfo) {
        this.safetyReviewAddlInfo = safetyReviewAddlInfo;
    }

    /**
     * 安全事件-是否会审
     */
    public String getSafetyJoint() {
        return safetyJoint;
    }

    /**
     * 安全事件-是否会审
     */
    public void setSafetyJoint(String safetyJoint) {
        this.safetyJoint = safetyJoint;
    }

    /**
     * 安全事件-费用
     */
    public String getSafetyFee() {
        return safetyFee;
    }

    /**
     * 安全事件-费用
     */
    public void setSafetyFee(String safetyFee) {
        this.safetyFee = safetyFee;
    }

    /**
     * 安全事件-打款备注
     */
    public String getSafetyPaymentRemarks() {
        return safetyPaymentRemarks;
    }

    /**
     * 安全事件-打款备注
     */
    public void setSafetyPaymentRemarks(String safetyPaymentRemarks) {
        this.safetyPaymentRemarks = safetyPaymentRemarks;
    }

    /**
     * 安全事件-打款时限
     */
    public String getSafetyPayLimit() {
        return safetyPayLimit;
    }

    /**
     * 安全事件-打款时限
     */
    public void setSafetyPayLimit(String safetyPayLimit) {
        this.safetyPayLimit = safetyPayLimit;
    }

    /**
     * 安全事件-发票领取时间及地址
     */
    public String getSafetyInvoice() {
        return safetyInvoice;
    }

    /**
     * 安全事件-发票领取时间及地址
     */
    public void setSafetyInvoice(String safetyInvoice) {
        this.safetyInvoice = safetyInvoice;
    }

    /**
     * 安全事件-递交时间
     */
    public String getSafetySubmitTime() {
        return safetySubmitTime;
    }

    /**
     * 安全事件-递交时间
     */
    public void setSafetySubmitTime(String safetySubmitTime) {
        this.safetySubmitTime = safetySubmitTime;
    }

    /**
     * 安全事件-递交人员
     */
    public String getSafetySubmitor() {
        return safetySubmitor;
    }

    /**
     * 安全事件-递交人员
     */
    public void setSafetySubmitor(String safetySubmitor) {
        this.safetySubmitor = safetySubmitor;
    }

    /**
     * 安全事件-补充说明
     */
    public String getSafetyAddlInfo() {
        return safetyAddlInfo;
    }

    /**
     * 安全事件-补充说明
     */
    public void setSafetyAddlInfo(String safetyAddlInfo) {
        this.safetyAddlInfo = safetyAddlInfo;
    }

    /**
     * 定期跟踪审查-伦理规定模版
     */
    public String getPeriodiethicsTemplate() {
        return periodiethicsTemplate;
    }

    /**
     * 定期跟踪审查-伦理规定模版
     */
    public void setPeriodiethicsTemplate(String periodiethicsTemplate) {
        this.periodiethicsTemplate = periodiethicsTemplate;
    }

    /**
     * 定期跟踪审查-审查频率及要求
     */
    public String getPeriodifreqRqmt() {
        return periodifreqRqmt;
    }

    /**
     * 定期跟踪审查-审查频率及要求
     */
    public void setPeriodifreqRqmt(String periodifreqRqmt) {
        this.periodifreqRqmt = periodifreqRqmt;
    }

    /**
     * 定期跟踪审查-结题流程要求
     */
    public String getPeriodiconclusionRqmt() {
        return periodiconclusionRqmt;
    }

    /**
     * 定期跟踪审查-结题流程要求
     */
    public void setPeriodiconclusionRqmt(String periodiconclusionRqmt) {
        this.periodiconclusionRqmt = periodiconclusionRqmt;
    }

    /**
     * 定期跟踪审查-材料递交要求
     */
    public String getPeriodimaterialRqmt() {
        return periodimaterialRqmt;
    }

    /**
     * 定期跟踪审查-材料递交要求
     */
    public void setPeriodimaterialRqmt(String periodimaterialRqmt) {
        this.periodimaterialRqmt = periodimaterialRqmt;
    }

    /**
     * 定期跟踪审查-审查方式
     */
    public String getPeriodireviewMethod() {
        return periodireviewMethod;
    }

    /**
     * 定期跟踪审查-审查方式
     */
    public void setPeriodireviewMethod(String periodireviewMethod) {
        this.periodireviewMethod = periodireviewMethod;
    }

    /**
     * 定期跟踪审查-审查方式补充说明
     */
    public String getPeriodireviewAddlInfo() {
        return periodireviewAddlInfo;
    }

    /**
     * 定期跟踪审查-审查方式补充说明
     */
    public void setPeriodireviewAddlInfo(String periodireviewAddlInfo) {
        this.periodireviewAddlInfo = periodireviewAddlInfo;
    }

    /**
     * 定期跟踪审查-是否会审
     */
    public String getPeriodijoint() {
        return periodijoint;
    }

    /**
     * 定期跟踪审查-是否会审
     */
    public void setPeriodijoint(String periodijoint) {
        this.periodijoint = periodijoint;
    }

    /**
     * 定期跟踪审查-费用
     */
    public String getPeriodifee() {
        return periodifee;
    }

    /**
     * 定期跟踪审查-费用
     */
    public void setPeriodifee(String periodifee) {
        this.periodifee = periodifee;
    }

    /**
     * 定期跟踪审查-打款备注
     */
    public String getPeriodipaymentRemarks() {
        return periodipaymentRemarks;
    }

    /**
     * 定期跟踪审查-打款备注
     */
    public void setPeriodipaymentRemarks(String periodipaymentRemarks) {
        this.periodipaymentRemarks = periodipaymentRemarks;
    }

    /**
     * 定期跟踪审查-需要答辩
     */
    public String getPeriodidefense() {
        return periodidefense;
    }

    /**
     * 定期跟踪审查-需要答辩
     */
    public void setPeriodidefense(String periodidefense) {
        this.periodidefense = periodidefense;
    }

    /**
     * 定期跟踪审查-答辩要求
     */
    public String getPeriodidefenseRqmt() {
        return periodidefenseRqmt;
    }

    /**
     * 定期跟踪审查-答辩要求
     */
    public void setPeriodidefenseRqmt(String periodidefenseRqmt) {
        this.periodidefenseRqmt = periodidefenseRqmt;
    }

    /**
     * 定期跟踪审查-意见跟进和反馈
     */
    public String getPeriodifeedback() {
        return periodifeedback;
    }

    /**
     * 定期跟踪审查-意见跟进和反馈
     */
    public void setPeriodifeedback(String periodifeedback) {
        this.periodifeedback = periodifeedback;
    }

    /**
     * 定期跟踪审查-打款时限
     */
    public String getPeriodipayLimit() {
        return periodipayLimit;
    }

    /**
     * 定期跟踪审查-打款时限
     */
    public void setPeriodipayLimit(String periodipayLimit) {
        this.periodipayLimit = periodipayLimit;
    }

    /**
     * 定期跟踪审查-发票领取时间及地址
     */
    public String getPeriodiinvoice() {
        return periodiinvoice;
    }

    /**
     * 定期跟踪审查-发票领取时间及地址
     */
    public void setPeriodiinvoice(String periodiinvoice) {
        this.periodiinvoice = periodiinvoice;
    }

    /**
     * 定期跟踪审查-递交时间
     */
    public String getPeriodisubmitTime() {
        return periodisubmitTime;
    }

    /**
     * 定期跟踪审查-递交时间
     */
    public void setPeriodisubmitTime(String periodisubmitTime) {
        this.periodisubmitTime = periodisubmitTime;
    }

    /**
     * 定期跟踪审查-补充说明
     */
    public String getPeriodiaddlInfo() {
        return periodiaddlInfo;
    }

    /**
     * 定期跟踪审查-补充说明
     */
    public void setPeriodiaddlInfo(String periodiaddlInfo) {
        this.periodiaddlInfo = periodiaddlInfo;
    }

    /**
     * 结题审查-伦理规定模版
     */
    public String getConclusionEthicsTemplate() {
        return conclusionEthicsTemplate;
    }

    /**
     * 结题审查-伦理规定模版
     */
    public void setConclusionEthicsTemplate(String conclusionEthicsTemplate) {
        this.conclusionEthicsTemplate = conclusionEthicsTemplate;
    }

    /**
     * 结题审查-审查频率及要求
     */
    public String getConclusionFreqRqmt() {
        return conclusionFreqRqmt;
    }

    /**
     * 结题审查-审查频率及要求
     */
    public void setConclusionFreqRqmt(String conclusionFreqRqmt) {
        this.conclusionFreqRqmt = conclusionFreqRqmt;
    }

    /**
     * 结题审查-结题流程要求
     */
    public String getConclusionConclusionRqmt() {
        return conclusionConclusionRqmt;
    }

    /**
     * 结题审查-结题流程要求
     */
    public void setConclusionConclusionRqmt(String conclusionConclusionRqmt) {
        this.conclusionConclusionRqmt = conclusionConclusionRqmt;
    }

    /**
     * 结题审查-材料递交要求
     */
    public String getConclusionMaterialRqmt() {
        return conclusionMaterialRqmt;
    }

    /**
     * 结题审查-材料递交要求
     */
    public void setConclusionMaterialRqmt(String conclusionMaterialRqmt) {
        this.conclusionMaterialRqmt = conclusionMaterialRqmt;
    }

    /**
     * 结题审查-审查方式
     */
    public String getConclusionReviewMethod() {
        return conclusionReviewMethod;
    }

    /**
     * 结题审查-审查方式
     */
    public void setConclusionReviewMethod(String conclusionReviewMethod) {
        this.conclusionReviewMethod = conclusionReviewMethod;
    }

    /**
     * 结题审查-审查方式补充说明
     */
    public String getConclusionReviewAddlInfo() {
        return conclusionReviewAddlInfo;
    }

    /**
     * 结题审查-审查方式补充说明
     */
    public void setConclusionReviewAddlInfo(String conclusionReviewAddlInfo) {
        this.conclusionReviewAddlInfo = conclusionReviewAddlInfo;
    }

    /**
     * 结题审查-需要答辩
     */
    public String getConclusionDefense() {
        return conclusionDefense;
    }

    /**
     * 结题审查-需要答辩
     */
    public void setConclusionDefense(String conclusionDefense) {
        this.conclusionDefense = conclusionDefense;
    }

    /**
     * 结题审查-答辩要求
     */
    public String getConclusionDefenseRqmt() {
        return conclusionDefenseRqmt;
    }

    /**
     * 结题审查-答辩要求
     */
    public void setConclusionDefenseRqmt(String conclusionDefenseRqmt) {
        this.conclusionDefenseRqmt = conclusionDefenseRqmt;
    }

    /**
     * 结题审查-意见跟进和反馈
     */
    public String getConclusionFeedback() {
        return conclusionFeedback;
    }

    /**
     * 结题审查-意见跟进和反馈
     */
    public void setConclusionFeedback(String conclusionFeedback) {
        this.conclusionFeedback = conclusionFeedback;
    }

    /**
     * 结题审查-递交时间
     */
    public String getConclusionSubmitTime() {
        return conclusionSubmitTime;
    }

    /**
     * 结题审查-递交时间
     */
    public void setConclusionSubmitTime(String conclusionSubmitTime) {
        this.conclusionSubmitTime = conclusionSubmitTime;
    }

    /**
     * 结题审查-补充说明
     */
    public String getConclusionAddlInfo() {
        return conclusionAddlInfo;
    }

    /**
     * 结题审查-补充说明
     */
    public void setConclusionAddlInfo(String conclusionAddlInfo) {
        this.conclusionAddlInfo = conclusionAddlInfo;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TCenterStartUp other = (TCenterStartUp) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()))
            && (this.getDeleteFlag() == null ? other.getDeleteFlag() == null : this.getDeleteFlag().equals(other.getDeleteFlag()))
            && (this.getDatecreated() == null ? other.getDatecreated() == null : this.getDatecreated().equals(other.getDatecreated()))
            && (this.getDatemodified() == null ? other.getDatemodified() == null : this.getDatemodified().equals(other.getDatemodified()))
            && (this.getCreatedby() == null ? other.getCreatedby() == null : this.getCreatedby().equals(other.getCreatedby()))
            && (this.getCreatedbyname() == null ? other.getCreatedbyname() == null : this.getCreatedbyname().equals(other.getCreatedbyname()))
            && (this.getModifiedby() == null ? other.getModifiedby() == null : this.getModifiedby().equals(other.getModifiedby()))
            && (this.getModifiedbyname() == null ? other.getModifiedbyname() == null : this.getModifiedbyname().equals(other.getModifiedbyname()))
            && (this.getCenterId() == null ? other.getCenterId() == null : this.getCenterId().equals(other.getCenterId()))
            && (this.getKickoffRqmt() == null ? other.getKickoffRqmt() == null : this.getKickoffRqmt().equals(other.getKickoffRqmt()))
            && (this.getKickoffAddlInfo() == null ? other.getKickoffAddlInfo() == null : this.getKickoffAddlInfo().equals(other.getKickoffAddlInfo()))
            && (this.getCenterRqmt() == null ? other.getCenterRqmt() == null : this.getCenterRqmt().equals(other.getCenterRqmt()))
            && (this.getSubjectPrecondition() == null ? other.getSubjectPrecondition() == null : this.getSubjectPrecondition().equals(other.getSubjectPrecondition()))
            && (this.getSafetyEthicsTemplate() == null ? other.getSafetyEthicsTemplate() == null : this.getSafetyEthicsTemplate().equals(other.getSafetyEthicsTemplate()))
            && (this.getSafetyFreqRqmt() == null ? other.getSafetyFreqRqmt() == null : this.getSafetyFreqRqmt().equals(other.getSafetyFreqRqmt()))
            && (this.getSafetyReviewMethod() == null ? other.getSafetyReviewMethod() == null : this.getSafetyReviewMethod().equals(other.getSafetyReviewMethod()))
            && (this.getSafetyReviewAddlInfo() == null ? other.getSafetyReviewAddlInfo() == null : this.getSafetyReviewAddlInfo().equals(other.getSafetyReviewAddlInfo()))
            && (this.getSafetyJoint() == null ? other.getSafetyJoint() == null : this.getSafetyJoint().equals(other.getSafetyJoint()))
            && (this.getSafetyFee() == null ? other.getSafetyFee() == null : this.getSafetyFee().equals(other.getSafetyFee()))
            && (this.getSafetyPaymentRemarks() == null ? other.getSafetyPaymentRemarks() == null : this.getSafetyPaymentRemarks().equals(other.getSafetyPaymentRemarks()))
            && (this.getSafetyPayLimit() == null ? other.getSafetyPayLimit() == null : this.getSafetyPayLimit().equals(other.getSafetyPayLimit()))
            && (this.getSafetyInvoice() == null ? other.getSafetyInvoice() == null : this.getSafetyInvoice().equals(other.getSafetyInvoice()))
            && (this.getSafetySubmitTime() == null ? other.getSafetySubmitTime() == null : this.getSafetySubmitTime().equals(other.getSafetySubmitTime()))
            && (this.getSafetySubmitor() == null ? other.getSafetySubmitor() == null : this.getSafetySubmitor().equals(other.getSafetySubmitor()))
            && (this.getSafetyAddlInfo() == null ? other.getSafetyAddlInfo() == null : this.getSafetyAddlInfo().equals(other.getSafetyAddlInfo()))
            && (this.getPeriodiethicsTemplate() == null ? other.getPeriodiethicsTemplate() == null : this.getPeriodiethicsTemplate().equals(other.getPeriodiethicsTemplate()))
            && (this.getPeriodifreqRqmt() == null ? other.getPeriodifreqRqmt() == null : this.getPeriodifreqRqmt().equals(other.getPeriodifreqRqmt()))
            && (this.getPeriodiconclusionRqmt() == null ? other.getPeriodiconclusionRqmt() == null : this.getPeriodiconclusionRqmt().equals(other.getPeriodiconclusionRqmt()))
            && (this.getPeriodimaterialRqmt() == null ? other.getPeriodimaterialRqmt() == null : this.getPeriodimaterialRqmt().equals(other.getPeriodimaterialRqmt()))
            && (this.getPeriodireviewMethod() == null ? other.getPeriodireviewMethod() == null : this.getPeriodireviewMethod().equals(other.getPeriodireviewMethod()))
            && (this.getPeriodireviewAddlInfo() == null ? other.getPeriodireviewAddlInfo() == null : this.getPeriodireviewAddlInfo().equals(other.getPeriodireviewAddlInfo()))
            && (this.getPeriodijoint() == null ? other.getPeriodijoint() == null : this.getPeriodijoint().equals(other.getPeriodijoint()))
            && (this.getPeriodifee() == null ? other.getPeriodifee() == null : this.getPeriodifee().equals(other.getPeriodifee()))
            && (this.getPeriodipaymentRemarks() == null ? other.getPeriodipaymentRemarks() == null : this.getPeriodipaymentRemarks().equals(other.getPeriodipaymentRemarks()))
            && (this.getPeriodidefense() == null ? other.getPeriodidefense() == null : this.getPeriodidefense().equals(other.getPeriodidefense()))
            && (this.getPeriodidefenseRqmt() == null ? other.getPeriodidefenseRqmt() == null : this.getPeriodidefenseRqmt().equals(other.getPeriodidefenseRqmt()))
            && (this.getPeriodifeedback() == null ? other.getPeriodifeedback() == null : this.getPeriodifeedback().equals(other.getPeriodifeedback()))
            && (this.getPeriodipayLimit() == null ? other.getPeriodipayLimit() == null : this.getPeriodipayLimit().equals(other.getPeriodipayLimit()))
            && (this.getPeriodiinvoice() == null ? other.getPeriodiinvoice() == null : this.getPeriodiinvoice().equals(other.getPeriodiinvoice()))
            && (this.getPeriodisubmitTime() == null ? other.getPeriodisubmitTime() == null : this.getPeriodisubmitTime().equals(other.getPeriodisubmitTime()))
            && (this.getPeriodiaddlInfo() == null ? other.getPeriodiaddlInfo() == null : this.getPeriodiaddlInfo().equals(other.getPeriodiaddlInfo()))
            && (this.getConclusionEthicsTemplate() == null ? other.getConclusionEthicsTemplate() == null : this.getConclusionEthicsTemplate().equals(other.getConclusionEthicsTemplate()))
            && (this.getConclusionFreqRqmt() == null ? other.getConclusionFreqRqmt() == null : this.getConclusionFreqRqmt().equals(other.getConclusionFreqRqmt()))
            && (this.getConclusionConclusionRqmt() == null ? other.getConclusionConclusionRqmt() == null : this.getConclusionConclusionRqmt().equals(other.getConclusionConclusionRqmt()))
            && (this.getConclusionMaterialRqmt() == null ? other.getConclusionMaterialRqmt() == null : this.getConclusionMaterialRqmt().equals(other.getConclusionMaterialRqmt()))
            && (this.getConclusionReviewMethod() == null ? other.getConclusionReviewMethod() == null : this.getConclusionReviewMethod().equals(other.getConclusionReviewMethod()))
            && (this.getConclusionReviewAddlInfo() == null ? other.getConclusionReviewAddlInfo() == null : this.getConclusionReviewAddlInfo().equals(other.getConclusionReviewAddlInfo()))
            && (this.getConclusionDefense() == null ? other.getConclusionDefense() == null : this.getConclusionDefense().equals(other.getConclusionDefense()))
            && (this.getConclusionDefenseRqmt() == null ? other.getConclusionDefenseRqmt() == null : this.getConclusionDefenseRqmt().equals(other.getConclusionDefenseRqmt()))
            && (this.getConclusionFeedback() == null ? other.getConclusionFeedback() == null : this.getConclusionFeedback().equals(other.getConclusionFeedback()))
            && (this.getConclusionSubmitTime() == null ? other.getConclusionSubmitTime() == null : this.getConclusionSubmitTime().equals(other.getConclusionSubmitTime()))
            && (this.getConclusionAddlInfo() == null ? other.getConclusionAddlInfo() == null : this.getConclusionAddlInfo().equals(other.getConclusionAddlInfo()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        result = prime * result + ((getDeleteFlag() == null) ? 0 : getDeleteFlag().hashCode());
        result = prime * result + ((getDatecreated() == null) ? 0 : getDatecreated().hashCode());
        result = prime * result + ((getDatemodified() == null) ? 0 : getDatemodified().hashCode());
        result = prime * result + ((getCreatedby() == null) ? 0 : getCreatedby().hashCode());
        result = prime * result + ((getCreatedbyname() == null) ? 0 : getCreatedbyname().hashCode());
        result = prime * result + ((getModifiedby() == null) ? 0 : getModifiedby().hashCode());
        result = prime * result + ((getModifiedbyname() == null) ? 0 : getModifiedbyname().hashCode());
        result = prime * result + ((getCenterId() == null) ? 0 : getCenterId().hashCode());
        result = prime * result + ((getKickoffRqmt() == null) ? 0 : getKickoffRqmt().hashCode());
        result = prime * result + ((getKickoffAddlInfo() == null) ? 0 : getKickoffAddlInfo().hashCode());
        result = prime * result + ((getCenterRqmt() == null) ? 0 : getCenterRqmt().hashCode());
        result = prime * result + ((getSubjectPrecondition() == null) ? 0 : getSubjectPrecondition().hashCode());
        result = prime * result + ((getSafetyEthicsTemplate() == null) ? 0 : getSafetyEthicsTemplate().hashCode());
        result = prime * result + ((getSafetyFreqRqmt() == null) ? 0 : getSafetyFreqRqmt().hashCode());
        result = prime * result + ((getSafetyReviewMethod() == null) ? 0 : getSafetyReviewMethod().hashCode());
        result = prime * result + ((getSafetyReviewAddlInfo() == null) ? 0 : getSafetyReviewAddlInfo().hashCode());
        result = prime * result + ((getSafetyJoint() == null) ? 0 : getSafetyJoint().hashCode());
        result = prime * result + ((getSafetyFee() == null) ? 0 : getSafetyFee().hashCode());
        result = prime * result + ((getSafetyPaymentRemarks() == null) ? 0 : getSafetyPaymentRemarks().hashCode());
        result = prime * result + ((getSafetyPayLimit() == null) ? 0 : getSafetyPayLimit().hashCode());
        result = prime * result + ((getSafetyInvoice() == null) ? 0 : getSafetyInvoice().hashCode());
        result = prime * result + ((getSafetySubmitTime() == null) ? 0 : getSafetySubmitTime().hashCode());
        result = prime * result + ((getSafetySubmitor() == null) ? 0 : getSafetySubmitor().hashCode());
        result = prime * result + ((getSafetyAddlInfo() == null) ? 0 : getSafetyAddlInfo().hashCode());
        result = prime * result + ((getPeriodiethicsTemplate() == null) ? 0 : getPeriodiethicsTemplate().hashCode());
        result = prime * result + ((getPeriodifreqRqmt() == null) ? 0 : getPeriodifreqRqmt().hashCode());
        result = prime * result + ((getPeriodiconclusionRqmt() == null) ? 0 : getPeriodiconclusionRqmt().hashCode());
        result = prime * result + ((getPeriodimaterialRqmt() == null) ? 0 : getPeriodimaterialRqmt().hashCode());
        result = prime * result + ((getPeriodireviewMethod() == null) ? 0 : getPeriodireviewMethod().hashCode());
        result = prime * result + ((getPeriodireviewAddlInfo() == null) ? 0 : getPeriodireviewAddlInfo().hashCode());
        result = prime * result + ((getPeriodijoint() == null) ? 0 : getPeriodijoint().hashCode());
        result = prime * result + ((getPeriodifee() == null) ? 0 : getPeriodifee().hashCode());
        result = prime * result + ((getPeriodipaymentRemarks() == null) ? 0 : getPeriodipaymentRemarks().hashCode());
        result = prime * result + ((getPeriodidefense() == null) ? 0 : getPeriodidefense().hashCode());
        result = prime * result + ((getPeriodidefenseRqmt() == null) ? 0 : getPeriodidefenseRqmt().hashCode());
        result = prime * result + ((getPeriodifeedback() == null) ? 0 : getPeriodifeedback().hashCode());
        result = prime * result + ((getPeriodipayLimit() == null) ? 0 : getPeriodipayLimit().hashCode());
        result = prime * result + ((getPeriodiinvoice() == null) ? 0 : getPeriodiinvoice().hashCode());
        result = prime * result + ((getPeriodisubmitTime() == null) ? 0 : getPeriodisubmitTime().hashCode());
        result = prime * result + ((getPeriodiaddlInfo() == null) ? 0 : getPeriodiaddlInfo().hashCode());
        result = prime * result + ((getConclusionEthicsTemplate() == null) ? 0 : getConclusionEthicsTemplate().hashCode());
        result = prime * result + ((getConclusionFreqRqmt() == null) ? 0 : getConclusionFreqRqmt().hashCode());
        result = prime * result + ((getConclusionConclusionRqmt() == null) ? 0 : getConclusionConclusionRqmt().hashCode());
        result = prime * result + ((getConclusionMaterialRqmt() == null) ? 0 : getConclusionMaterialRqmt().hashCode());
        result = prime * result + ((getConclusionReviewMethod() == null) ? 0 : getConclusionReviewMethod().hashCode());
        result = prime * result + ((getConclusionReviewAddlInfo() == null) ? 0 : getConclusionReviewAddlInfo().hashCode());
        result = prime * result + ((getConclusionDefense() == null) ? 0 : getConclusionDefense().hashCode());
        result = prime * result + ((getConclusionDefenseRqmt() == null) ? 0 : getConclusionDefenseRqmt().hashCode());
        result = prime * result + ((getConclusionFeedback() == null) ? 0 : getConclusionFeedback().hashCode());
        result = prime * result + ((getConclusionSubmitTime() == null) ? 0 : getConclusionSubmitTime().hashCode());
        result = prime * result + ((getConclusionAddlInfo() == null) ? 0 : getConclusionAddlInfo().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", isDelete=").append(isDelete);
        sb.append(", deleteFlag=").append(deleteFlag);
        sb.append(", datecreated=").append(datecreated);
        sb.append(", datemodified=").append(datemodified);
        sb.append(", createdby=").append(createdby);
        sb.append(", createdbyname=").append(createdbyname);
        sb.append(", modifiedby=").append(modifiedby);
        sb.append(", modifiedbyname=").append(modifiedbyname);
        sb.append(", centerId=").append(centerId);
        sb.append(", kickoffRqmt=").append(kickoffRqmt);
        sb.append(", kickoffAddlInfo=").append(kickoffAddlInfo);
        sb.append(", centerRqmt=").append(centerRqmt);
        sb.append(", subjectPrecondition=").append(subjectPrecondition);
        sb.append(", safetyEthicsTemplate=").append(safetyEthicsTemplate);
        sb.append(", safetyFreqRqmt=").append(safetyFreqRqmt);
        sb.append(", safetyReviewMethod=").append(safetyReviewMethod);
        sb.append(", safetyReviewAddlInfo=").append(safetyReviewAddlInfo);
        sb.append(", safetyJoint=").append(safetyJoint);
        sb.append(", safetyFee=").append(safetyFee);
        sb.append(", safetyPaymentRemarks=").append(safetyPaymentRemarks);
        sb.append(", safetyPayLimit=").append(safetyPayLimit);
        sb.append(", safetyInvoice=").append(safetyInvoice);
        sb.append(", safetySubmitTime=").append(safetySubmitTime);
        sb.append(", safetySubmitor=").append(safetySubmitor);
        sb.append(", safetyAddlInfo=").append(safetyAddlInfo);
        sb.append(", periodiethicsTemplate=").append(periodiethicsTemplate);
        sb.append(", periodifreqRqmt=").append(periodifreqRqmt);
        sb.append(", periodiconclusionRqmt=").append(periodiconclusionRqmt);
        sb.append(", periodimaterialRqmt=").append(periodimaterialRqmt);
        sb.append(", periodireviewMethod=").append(periodireviewMethod);
        sb.append(", periodireviewAddlInfo=").append(periodireviewAddlInfo);
        sb.append(", periodijoint=").append(periodijoint);
        sb.append(", periodifee=").append(periodifee);
        sb.append(", periodipaymentRemarks=").append(periodipaymentRemarks);
        sb.append(", periodidefense=").append(periodidefense);
        sb.append(", periodidefenseRqmt=").append(periodidefenseRqmt);
        sb.append(", periodifeedback=").append(periodifeedback);
        sb.append(", periodipayLimit=").append(periodipayLimit);
        sb.append(", periodiinvoice=").append(periodiinvoice);
        sb.append(", periodisubmitTime=").append(periodisubmitTime);
        sb.append(", periodiaddlInfo=").append(periodiaddlInfo);
        sb.append(", conclusionEthicsTemplate=").append(conclusionEthicsTemplate);
        sb.append(", conclusionFreqRqmt=").append(conclusionFreqRqmt);
        sb.append(", conclusionConclusionRqmt=").append(conclusionConclusionRqmt);
        sb.append(", conclusionMaterialRqmt=").append(conclusionMaterialRqmt);
        sb.append(", conclusionReviewMethod=").append(conclusionReviewMethod);
        sb.append(", conclusionReviewAddlInfo=").append(conclusionReviewAddlInfo);
        sb.append(", conclusionDefense=").append(conclusionDefense);
        sb.append(", conclusionDefenseRqmt=").append(conclusionDefenseRqmt);
        sb.append(", conclusionFeedback=").append(conclusionFeedback);
        sb.append(", conclusionSubmitTime=").append(conclusionSubmitTime);
        sb.append(", conclusionAddlInfo=").append(conclusionAddlInfo);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}