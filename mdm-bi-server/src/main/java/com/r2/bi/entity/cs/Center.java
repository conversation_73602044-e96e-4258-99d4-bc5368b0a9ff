package com.r2.bi.entity.cs;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 中心基本信息(Center)表实体类
 *
 * <AUTHOR>
 * @since 2022-11-09 10:17:13
 */
@TableName("t_center")
@Data
@SuppressWarnings("serial")
public class Center extends Model<Center> {
    /**
     * 中心id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /**
     * 是否删除 0未删除 1删除
     */
    @TableField("is_delete")
    private Boolean isDelete;

    /**
     * 删除标识
     */
    private String deleteFlag;

    /**
     * 创建时间
     */
    @TableField(value = "dateCreated", fill = FieldFill.INSERT)
    private Date datecreated;

    /**
     * 修改时间
     */
    private Date datemodified;

    /**
     * 创建人
     */
    @TableField(value = "createdBy", fill = FieldFill.INSERT)
    private String createdby;

    /**
     * 创建人姓名
     */
    @TableField(value = "createdByName", fill = FieldFill.INSERT)
    private String createdbyname;

    /**
     * 修改人
     */
    private String modifiedby;

    /**
     * 修改人姓名
     */
    private String modifiedbyname;

    /**
     * 中心标准名称
     */
    private String centerName;

    /**
     * 中心标准编码
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String standardCode;

    /**
     * 中心性质
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String type;

    /**
     * 中心等级
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String class1;

    /**
     * 省
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String province;

    /**
     * 市
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String city;

    /**
     * 区
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String area;

    /**
     * 地址
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String address;

    /**
     * crc人员
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String crc;

    /**
     * 网址
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String url;

    /**
     * 总机电话
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String tel;

    /**
     * 入院要求
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String admittedRqmt;

    /**
     * 入院要求补充说明
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String admittedRqmtInfo;

    /**
     * 入院备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String admittedRemark;

    /**
     * 别名，逗号隔开，动态添加，最多10个
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String alias;

    /**
     * 机构接待日
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String orgReceptionDate;

    /**
     * 伦理接待日
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String ethicsReceptionDate;

    /**
     * 平均启动时长（周）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String startupTime;

    /**
     * 区域id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String zoneId;

    /**
     * 更新人
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String updator;

    /**
     * 备案号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String aqNumber;

    /**
     * 备案状态
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String aqStatus;

    /**
     * 首次备案时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String fristAqTime;

    /**
     * 备案时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String aqTime;

    /**
     * 药物首次备案号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String drugAqFirstNumber;

    /**
     * 药物备案号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String drugAqNumber;

    /**
     * 药物备案状态
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String drugAqStatus;

    /**
     * 药物首次备案时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String drugAqFirstTime;

    /**
     * 药物备案时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String drugAqTime;

    /**
     * 药物备案联系人
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String drugAqContacts;

    /**
     * 药物备案联系电话
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String drugAqContactsPhone;

    /**
     * 药物备案专业
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String drugAqMajor;

    /**
     * 器械备案号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deviceAqNumber;

    /**
     * 器械备案状态
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deviceAqStatus;

    /**
     * 器械备案时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deviceAqTime;

    /**
     * 器械备案联系人
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deviceAqContacts;

    /**
     * 器械备案联系电话
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deviceAqContactsPhone;

    /**
     * 器械备案专业
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deviceAqMajor;

    /**
     * 有优选smo名单
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String haveSmo;

    /**
     * 联斯达是否为优选smo
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String linkstartSmo;

    /**
     * CRO战略合作中心
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String croCorpor;

    /**
     * 科室人员配合度
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deptGrade;

    /**
     * 机构人员配合度
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String orgGrade;

    /**
     * 伦理人员配合度
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String ethicsGrade;

    /**
     * 办事流程满意度
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String processGrade;

    /**
     * 项目进度配合度
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String projectGrade;

    /**
     * 联斯达合作情况
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String linkstartCorp;

    /**
     * 联斯达合作情况备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String linkstartCorpRemark;

    /**
     * 是否联斯达人员覆盖
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String linkstartCover;

    /**
     * 本院合作优/良点体现
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String performInfo;

    /**
     * 体现备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String performRemark;

    /**
     * 数据来源
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String source;

    /**
     * 状态
     */
    private String status;

    /**
     * 积分
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String score;

    /**
     * 工作进展
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String workFlow;

    /**
     * 项目流程
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String projectProgress;

    /**
     * 科室配合度
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deptDegree;

    /**
     * 组织配合度
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String orgDegree;

    /**
     * 伦理配合度
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String ethicsDegree;

    /**
     * CRO区域id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String croZoneId;

    /**
     * CRO更新人
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String croUpdator;

    /**
     * site选择推荐程度
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String siteRecommend;

    /**
     * 推荐理由
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String recommendReason;

    /**
     * 社区
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String community;

    /**
     * 我司在中心是否可以承接临床项目
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String clinicalProjects;

    /**
     * 对SMO要求
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String smoAsk;

    /**
     * 对SMO要求补充说明
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String smoAskRemark;

    /**
     * 康龙临床战略合作中心
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String strategyCorpor;

    /**
     * 联斯达人员覆盖数
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String personnelCoverage;

    /**
     * 选为优选时间Preferred time
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String preferredTime;

    /**
     * 评选附件
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String selectionFile;

    /**
     * 优选SMO评选要求
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String smoSelectionAsk;

    /**
     * 是否接受CRO/SMO来自同一集团下
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String sameGroup;

    /**
     * 是否接受CRO/SMO来自同一集团下补充说明
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String sameGroupRemark;

    /**
     * 可加速流程
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String speedProcess;

    /**
     * 新项目调研流程/顺序
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String researchProcess;

    /**
     * 新调研人员要求
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String requestResearchers;

    /**
     * 新项目调研方式
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String researchMethods;

    /**
     * 新调研人员要求补充说明
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String researchMethodsSupplement;


    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    public String getField(String fieldName) {
        switch (fieldName) {
            case "ID":
                return id.toString();
            case "中心名称":
                return centerName;
            case"详细地址":
                return address;
            default:
                throw new IllegalArgumentException("Unknown field: " + fieldName);
        }
    }
}



