package com.r2.bi.entity.bi;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;

/**
 * 角色菜单关联(SysRolesMenus)表实体类
 *
 * <AUTHOR>
 * @since 2022-10-21 10:04:10
 */
@SuppressWarnings("serial")
@TableName("t_sys_roles_menus")
public class SysRolesMenus extends Model<SysRolesMenus> {
    //菜单ID
    private Long menuId;
    //角色ID
    private Long roleId;


    public Long getMenuId() {
        return menuId;
    }

    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.menuId;
    }
    }

