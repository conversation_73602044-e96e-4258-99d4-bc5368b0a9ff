package com.r2.bi.entity.dc;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 权限访问控制管理表(SysAccessSystem)实体类
 *
 * <AUTHOR>
 * @since 2023-09-27 10:40:18
 */
@TableName("sys_access_system")
@Data
public class SysAccessSystem implements Serializable {
    private static final long serialVersionUID = -54951086273275323L;
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 系统名称
     */
    private String name;
    /**
     * 系统编码
     */
    private String code;
    /**
     * 英文名称
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String nameEn;
    /**
     * 别名
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String alias;
    /**
     * 部门
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String dept;
    /**
     * 系统说明
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String description;
    /**
     * 状态：正常/已停用
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private String status;

    /**
     * 类型：1：系统来源，2：自定义表格
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL)
    private Integer type;
    /**
     * 自定义表格名称
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String tabulatioName;


    /**
     * 是否删除：0：未删除；1：已删除
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新人
     */
    private String updateBy;
}

