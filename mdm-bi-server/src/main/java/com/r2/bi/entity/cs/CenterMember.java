package com.r2.bi.entity.cs;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 中心人员(CenterMember)实体类
 *
 * <AUTHOR>
 * @since 2022-11-10 20:30:39
 */
@Data
@TableName("t_center_member")
public class CenterMember extends Model<CenterMember> {
    private static final long serialVersionUID = 403212744529057116L;
    /**
     * 中心人员id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 是否删除 0未删除 1删除
     */
    private Integer isDelete;
    /**
     * 删除标识
     */
    private String deleteFlag;

    private Date datecreated;

    private Date datemodified;

    private String createdby;

    private String createdbyname;

    private String modifiedby;

    private String modifiedbyname;
    /**
     * 中心id
     */
    private Long centerId;
    /**
     * 人员姓名
     */
    private String name;
    /**
     * 所在科室
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String deptId;
    /**
     * 性别
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String gender;
    /**
     * 职称
     */
    private String title;
    /**
     * 职位
     */
    private String position;
    /**
     * 擅长领域
     */
    private String field;
    /**
     * 联系电话
     */
    private String tel;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 可做为PI单独接项目
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String projAlone;
    /**
     * 单独接项目需SMO支持
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String reqSmo;
    /**
     * 与联斯达合作
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String coopLinkstart;
    /**
     * 试验支持度：0：无；1：机构；2：科研；3：伦理；4：遗传办；5：合同；6：启动；
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String supportTrial;
    /**
     * 对临床试验关注点
     */
    private String trialFocus;
    /**
     * 地址
     */
    private String address;

    /**
     * 备注
     */
    private String remark;
    /**
     * 特点
     */
    private String hobby;
    /**
     * 状态
     */
    private String status;
    /**
     * 数据来源
     */
    private String dataSource;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 银行卡号
     */
    private String bankCard;
    /**
     * 出诊时间（日历）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String visitTime;
    /**
     * 方便拜访时间
     */
    private String avlHour;
    /**
     * 备案专业
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String major;
    /**
     * 微信
     */
    private String wechat;
    /**
     * 合作情况
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String corporation;
    /**
     * 任职信息(机构、遗传办等)，多任职逗号分隔
     */
    private String contactType;
    /**
     * 角色，多角色逗号分隔
     */
    private String role;
    /**
     * 角色备注
     */
    private String roleRemark;
    /**
     * 关键信息备注
     */
    private String keyRemark;
    /**
     * 简历
     */
    private String resume;
    /**
     * 证书
     */
    private String certificate;

    /**
     * 接待要求
     */
    private String receptionContent;

    /**
     * 接待开始时间
     */
    private String startReceptionTime;

    /**
     * 接待结束时间
     */
    private String endReceptionTime;

    /**
     * 通讯录部门id
     */
    private Integer addressBookId;
}

