package com.r2.bi.entity.bi;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统菜单(SysMenu)表实体类
 *
 * <AUTHOR>
 * @since 2022-10-21 10:04:10
 */
@SuppressWarnings("serial")
@TableName("t_sys_menu")
@Data
public class SysMenu extends Model<SysMenu> {
    //ID
    @TableId(type = IdType.AUTO)
    private Long id;
    //上级菜单ID
    private Long pid;
    //子菜单数目
    private Integer subCount;
    //菜单类型
    private Integer type;
    //菜单标题
    private String title;
    //组件名称
    private String name;
    //组件
    private String component;
    //排序
    private Integer menuSort;
    //图标
    private String icon;
    //权限
    private String permission;
    //路由地址
    private String route;
    //状态[菜单是否可见] 1启用 2停用
    private Integer status;
    //创建者
    private String createBy;
    //更新者
    private String updateBy;
    //创建日期
    private LocalDateTime createTime;
    //更新时间
    private LocalDateTime updateTime;
    //状态 1启用 2停用
    private Integer isDelete;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    protected Serializable pkVal() {
        return this.id;
    }
    }

