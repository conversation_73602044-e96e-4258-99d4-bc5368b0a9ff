package com.r2.bi.entity.dc;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据映射关联表
 * <AUTHOR>
 * @TableName association_table
 */
@Data
@TableName(value ="association_table")
public class AssociationTable implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 系统来源名称/自定义表格名称
     */
    private String systemSource;

    /**
     * 分类1：中心，2：科室，3：适应症
     */
    private String type;

    /**
     * 第三方id
     */
    private String thirdPartyId;

    /**
     * 第三方中心名称
     */
    private String thirdPartyCenterName;

    /**
     * 第三方详细地址
     */
    private String thirdPartyAddress;

    /**
     * 第三方是否删除
     */
    private String deleteFlag;

    /**
     * 省-市
     */
    private String provinceAndCity;

    /**
     * 中台ID
     */
    private String mdmId;

    /**
     * mdm中心标准编码
     */
    private String mdmCenterCode;

    /**
     * mdm中心标准名称
     */
    private String mdmCenterName;

    /**
     * mdm中心别名
     */
    private String mdmCenterAlias;

    /**
     * mdm详细地址
     */
    private String mdmAddress;

    /**
     * 第三方适应症Id
     */
    private String thirdPartyIndicationId;

    /**
     * 第三方适应症名称
     */
    private String thirdPartyIndication;

    /**
     * mdm适应症ID
     */
    private String mdmIndicationId;

    /**
     * mdm适应症（中）
     */
    private String mdmIndicationZh;

    /**
     * mdm适应症（英）
     */
    private String mdmIndicationEn;

    /**
     * mdm适应症缩写
     */
    private String mdmIndicationAbbr;

    /**
     * 删除（0未删除 1已删除）
     */
    private Integer isDelete;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public Integer getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 系统来源名称
     */
    public String getSystemSource() {
        return systemSource;
    }

    /**
     * 系统来源名称
     */
    public void setSystemSource(String systemSource) {
        this.systemSource = systemSource;
    }

    /**
     * 分类1：中心，2：科室，3：适应症
     */
    public String getType() {
        return type;
    }

    /**
     * 分类1：中心，2：科室，3：适应症
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 第三方id
     */
    public String getThirdPartyId() {
        return thirdPartyId;
    }

    /**
     * 第三方id
     */
    public void setThirdPartyId(String thirdPartyId) {
        this.thirdPartyId = thirdPartyId;
    }

    /**
     * 第三方中心名称
     */
    public String getThirdPartyCenterName() {
        return thirdPartyCenterName;
    }

    /**
     * 第三方中心名称
     */
    public void setThirdPartyCenterName(String thirdPartyCenterName) {
        this.thirdPartyCenterName = thirdPartyCenterName;
    }

    /**
     * 中台ID
     */
    public String getMdmId() {
        return mdmId;
    }

    /**
     * 中台ID
     */
    public void setMdmId(String mdmId) {
        this.mdmId = mdmId;
    }

    /**
     * mdm中心标准编码
     */
    public String getMdmCenterCode() {
        return mdmCenterCode;
    }

    /**
     * mdm中心标准编码
     */
    public void setMdmCenterCode(String mdmCenterCode) {
        this.mdmCenterCode = mdmCenterCode;
    }

    /**
     * mdm中心标准名称
     */
    public String getMdmCenterName() {
        return mdmCenterName;
    }

    /**
     * mdm中心标准名称
     */
    public void setMdmCenterName(String mdmCenterName) {
        this.mdmCenterName = mdmCenterName;
    }

    /**
     * mdm中心别名
     */
    public String getMdmCenterAlias() {
        return mdmCenterAlias;
    }

    /**
     * mdm中心别名
     */
    public void setMdmCenterAlias(String mdmCenterAlias) {
        this.mdmCenterAlias = mdmCenterAlias;
    }

    /**
     * mdm详细地址
     */
    public String getMdmAddress() {
        return mdmAddress;
    }

    /**
     * mdm详细地址
     */
    public void setMdmAddress(String mdmAddress) {
        this.mdmAddress = mdmAddress;
    }

    /**
     * 第三方适应症Id
     */
    public String getThirdPartyIndicationId() {
        return thirdPartyIndicationId;
    }

    /**
     * 第三方适应症Id
     */
    public void setThirdPartyIndicationId(String thirdPartyIndicationId) {
        this.thirdPartyIndicationId = thirdPartyIndicationId;
    }

    /**
     * 第三方适应症名称
     */
    public String getThirdPartyIndication() {
        return thirdPartyIndication;
    }

    /**
     * 第三方适应症名称
     */
    public void setThirdPartyIndication(String thirdPartyIndication) {
        this.thirdPartyIndication = thirdPartyIndication;
    }

    /**
     * mdm适应症ID
     */
    public String getMdmIndicationId() {
        return mdmIndicationId;
    }

    /**
     * mdm适应症ID
     */
    public void setMdmIndicationId(String mdmIndicationId) {
        this.mdmIndicationId = mdmIndicationId;
    }

    /**
     * mdm适应症（中）
     */
    public String getMdmIndicationZh() {
        return mdmIndicationZh;
    }

    /**
     * mdm适应症（中）
     */
    public void setMdmIndicationZh(String mdmIndicationZh) {
        this.mdmIndicationZh = mdmIndicationZh;
    }

    /**
     * mdm适应症（英）
     */
    public String getMdmIndicationEn() {
        return mdmIndicationEn;
    }

    /**
     * mdm适应症（英）
     */
    public void setMdmIndicationEn(String mdmIndicationEn) {
        this.mdmIndicationEn = mdmIndicationEn;
    }

    /**
     * mdm适应症缩写
     */
    public String getMdmIndicationAbbr() {
        return mdmIndicationAbbr;
    }

    /**
     * mdm适应症缩写
     */
    public void setMdmIndicationAbbr(String mdmIndicationAbbr) {
        this.mdmIndicationAbbr = mdmIndicationAbbr;
    }

    /**
     * 删除（0未删除 1已删除）
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * 删除（0未删除 1已删除）
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * 创建者
     */
    public String getCreateBy() {
        return createBy;
    }

    /**
     * 创建者
     */
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新者
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 更新者
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }



    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSystemSource() == null) ? 0 : getSystemSource().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getThirdPartyId() == null) ? 0 : getThirdPartyId().hashCode());
        result = prime * result + ((getThirdPartyCenterName() == null) ? 0 : getThirdPartyCenterName().hashCode());
        result = prime * result + ((getMdmId() == null) ? 0 : getMdmId().hashCode());
        result = prime * result + ((getMdmCenterCode() == null) ? 0 : getMdmCenterCode().hashCode());
        result = prime * result + ((getMdmCenterName() == null) ? 0 : getMdmCenterName().hashCode());
        result = prime * result + ((getMdmCenterAlias() == null) ? 0 : getMdmCenterAlias().hashCode());
        result = prime * result + ((getMdmAddress() == null) ? 0 : getMdmAddress().hashCode());
        result = prime * result + ((getThirdPartyIndicationId() == null) ? 0 : getThirdPartyIndicationId().hashCode());
        result = prime * result + ((getThirdPartyIndication() == null) ? 0 : getThirdPartyIndication().hashCode());
        result = prime * result + ((getMdmIndicationId() == null) ? 0 : getMdmIndicationId().hashCode());
        result = prime * result + ((getMdmIndicationZh() == null) ? 0 : getMdmIndicationZh().hashCode());
        result = prime * result + ((getMdmIndicationEn() == null) ? 0 : getMdmIndicationEn().hashCode());
        result = prime * result + ((getMdmIndicationAbbr() == null) ? 0 : getMdmIndicationAbbr().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        result = prime * result + ((getCreateBy() == null) ? 0 : getCreateBy().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateBy() == null) ? 0 : getUpdateBy().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", systemSource=").append(systemSource);
        sb.append(", type=").append(type);
        sb.append(", thirdPartyId=").append(thirdPartyId);
        sb.append(", thirdPartyCenterName=").append(thirdPartyCenterName);
        sb.append(", mdmId=").append(mdmId);
        sb.append(", mdmCenterCode=").append(mdmCenterCode);
        sb.append(", mdmCenterName=").append(mdmCenterName);
        sb.append(", mdmCenterAlias=").append(mdmCenterAlias);
        sb.append(", mdmAddress=").append(mdmAddress);
        sb.append(", thirdPartyIndicationId=").append(thirdPartyIndicationId);
        sb.append(", thirdPartyIndication=").append(thirdPartyIndication);
        sb.append(", mdmIndicationId=").append(mdmIndicationId);
        sb.append(", mdmIndicationZh=").append(mdmIndicationZh);
        sb.append(", mdmIndicationEn=").append(mdmIndicationEn);
        sb.append(", mdmIndicationAbbr=").append(mdmIndicationAbbr);
        sb.append(", isDelete=").append(isDelete);
        sb.append(", createBy=").append(createBy);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}