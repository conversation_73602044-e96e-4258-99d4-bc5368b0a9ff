package com.r2.bi.entity.cs;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 日历(CenterMeeting)表实体类
 *
 * <AUTHOR>
 * @since 2022-11-10 17:58:24
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_center_meeting")
public class CenterMeeting implements Serializable {
    private static final long serialVersionUID = 1L;
        
    @TableId(value = "id", type=IdType.AUTO)
    private Long id;
    //是否删除 0未删除 1删除
    private Integer isDelete;
    //删除标识 
    private String deleteFlag;
    //创建日期
    private Date datecreated;
    //修改日期
    private Date datemodified;
    //创建用户id
    private String createdby;
    //创建用户
    private String createdbyname;
    //修改用户id
    private String modifiedby;
    //修改用户
    private String modifiedbyname;
    //中心id
    private Long centerId;
    //会议类型：立项会/伦理会
    private String type;
    //会议时间段：上午/下午
    private String period;
    //重复类型：月/周/指定周/指定日期
    private String repeatType;
    //重复次数
    private String repeatNum;
    //重复信息（英文逗号隔开）
    private String repeatDetails;
    //开始日期
    private String startDate;
    //结束日期
    private String endDate;
    //重复周期
    private String repeatWeek;

}
