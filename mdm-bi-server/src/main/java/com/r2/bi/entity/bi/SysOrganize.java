package com.r2.bi.entity.bi;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 组织部门(SysOrganize)表实体类
 *
 * <AUTHOR>
 * @since 2022-11-02 10:41:19
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sys_organize")
public class SysOrganize {
    //主键id
    @TableId(type = IdType.AUTO)
    private Long id;

    private String organizeCode;
    
    private String organizeName;
    
    private String parentCode;

    private String secondLevelOrgCode;
    
    private Integer status;

    private Long personInCharge;
    
    private Date updateDay;
    //是否删除：0：未删除；1：已删除
    private Integer isDelete;
    //创建时间
    private Date createTime;
    //更新时间
    private Date updateTime;

}
