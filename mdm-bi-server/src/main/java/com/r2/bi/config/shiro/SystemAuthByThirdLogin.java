package com.r2.bi.config.shiro;

import org.apache.shiro.authc.UsernamePasswordToken;

public class SystemAuthByThirdLogin extends UsernamePasswordToken {

    public SystemAuthByThirdLogin(String username, char[] password) {
        this.username = username;
        this.password = password;
    }

    private String username;

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public void setUsername(String username) {
        this.username = username;
    }

    private char[] password;

    @Override
    public char[] getPassword() {
        return password;
    }

    @Override
    public void setPassword(char[] password) {
        this.password = password;
    }
}
