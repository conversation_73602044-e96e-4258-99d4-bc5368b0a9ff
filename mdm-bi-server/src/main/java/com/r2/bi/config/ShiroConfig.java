package com.r2.bi.config;

import com.r2.bi.config.shiro.ShiroProperties;
import com.r2.bi.config.shiro.cache.RedissonShiroCacheManager;
import com.r2.bi.config.shiro.session.RedissonSessionDao;
import com.r2.bi.config.shiro.session.RedissonSessionManager;
import com.r2.bi.constant.GlobalConstants;
import com.r2.bi.filter.MyPermissionFilter;
import com.r2.bi.filter.TokenFilter;
import com.r2.bi.service.SysMenuService;
import com.r2.bi.service.SysUserService;
import com.r2.bi.service.impl.SysMenuServiceImpl;
import org.apache.shiro.cache.CacheManager;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.session.mgt.SessionManager;
import org.apache.shiro.spring.LifecycleBeanPostProcessor;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.filter.DelegatingFilterProxy;

import javax.servlet.Filter;
import java.util.LinkedHashMap;
import java.util.Map;

@Configuration
public class ShiroConfig {

    @Bean
    public ShiroProperties getBean(){
        ShiroProperties shiroProperties = new ShiroProperties();
        return shiroProperties;
    }

   @Bean
   public SysMenuService sysMenuService(){
        return new SysMenuServiceImpl();
   }



    @Bean
    public ManagerRealm userRealm(@Lazy SysUserService sysUserService, @Lazy SysMenuService sysMenuService) {
        ManagerRealm employeeRealm = new ManagerRealm(sysUserService, sysMenuService,  GlobalConstants.SHIRO_AUTHORIZATION_CACHE);
        employeeRealm.setCredentialsMatcher(new CredentialsMatcher());
        return employeeRealm;
    }

    /**
     * 自定义cache manager，可以用来缓存employee认证以及授权信息
     *
     * @return
     */
    @Bean
    public CacheManager shiroCacheManager(RedissonClient redissonClient) {
        return new RedissonShiroCacheManager(redissonClient);
    }

    /**
     * 自定义会话管理器，会话管理器默认从request的header中取会话的id，通过sessionDao匹配会话
     *
     * @return
     */
    @Bean
    public SessionManager sessionManager(RedissonClient redisson, ShiroProperties shiroProperties) {
        RedissonSessionManager redissonWebSessionManager = new RedissonSessionManager();
        redissonWebSessionManager.setGlobalSessionTimeout(shiroProperties.getSessionTimeOut());
        redissonWebSessionManager.setSessionDAO(new RedissonSessionDao(redisson, null));
        return redissonWebSessionManager;
    }


    @Bean
    public SecurityManager securityManager(ManagerRealm managerRealm, CacheManager cacheManager, SessionManager sessionManager) {
        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
        // 设置realm.
        securityManager.setRealm(managerRealm);
        // 自定义缓存实现 使用redis
        securityManager.setCacheManager(cacheManager);
        // 自定义session管理 使用redis
        securityManager.setSessionManager(sessionManager);
        return securityManager;
    }

    @Bean
    public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager, MessageSource messageSource) {
        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
        shiroFilterFactoryBean.setSecurityManager(securityManager);
        //自定义拦截器，加入自定义的tokenFilter
        Map<String, Filter> filtersMap = new LinkedHashMap<>();
        filtersMap.put("token", new TokenFilter());
        filtersMap.put("perms", new MyPermissionFilter());
        shiroFilterFactoryBean.setFilters(filtersMap);
        // 匿名访问接口
        Map<String, String> filterChainDefinitionMap = new LinkedHashMap<>(16);
        filterChainDefinitionMap.put("/auth/login", "anon");
        filterChainDefinitionMap.put("/auth/thirdLoginBi", "anon");
        filterChainDefinitionMap.put("/auth/syncOrganizationBackdoor", "anon");
        filterChainDefinitionMap.put("/**.**", "anon");
        filterChainDefinitionMap.put("/**", "token");
        shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);
        return shiroFilterFactoryBean;
    }

    /**
     * 设置shiro拦截器（filter）
     *
     * @return
     */
    @Bean
    public FilterRegistrationBean<DelegatingFilterProxy> delegatingFilterProxy() {
        FilterRegistrationBean<DelegatingFilterProxy> filterRegistrationBean = new FilterRegistrationBean<>();
        DelegatingFilterProxy proxy = new DelegatingFilterProxy();
        proxy.setTargetFilterLifecycle(true);
        proxy.setTargetBeanName("shiroFilter");
        filterRegistrationBean.setFilter(proxy);
        return filterRegistrationBean;
    }

    /**
     * bean post processor，没有做增强，只是做了bean的生命周期管理
     *
     * @return
     */
    @Bean
    public LifecycleBeanPostProcessor lifecycleBeanPostProcessor() {
        return new LifecycleBeanPostProcessor();
    }

    /**
     * RequireXXX advisor

     * @return
     */
    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(SecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor advisor = new AuthorizationAttributeSourceAdvisor();
        advisor.setSecurityManager(securityManager);
        return advisor;
    }

    /**
     * 设置proxyTargetClass为true，使用cglib
     *
     * @return
     */
    @Bean
    public DefaultAdvisorAutoProxyCreator getDefaultAdvisorAutoProxyCreator() {
        DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator = new DefaultAdvisorAutoProxyCreator();
        defaultAdvisorAutoProxyCreator.setProxyTargetClass(true);
        return defaultAdvisorAutoProxyCreator;
    }
}
