package com.r2.bi.config;

import com.r2.framework.exception.CodeException;
import com.r2.bi.config.shiro.NoPwdToken;
import com.r2.bi.config.shiro.SystemAuthByThirdLogin;
import com.r2.bi.constant.GlobalConstants;
import com.r2.bi.entity.bi.BiUser;
import com.r2.bi.enums.ThirdApiResponseCodeEnum;
import com.r2.bi.service.SysMenuService;
import com.r2.bi.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.util.ByteSource;

import java.util.List;

@Slf4j
public class ManagerRealm extends AuthorizingRealm {

    private SysUserService sysUserService;

    private SysMenuService sysMenuService;

    ManagerRealm(SysUserService sysUserService, SysMenuService sysMenuService, String authorizationCacheName) {
        this.sysUserService = sysUserService;
        this.sysMenuService = sysMenuService;
        super.setAuthorizationCacheName(authorizationCacheName);
    }

    /**
     * realm的名称
     *
     * @return
     */
    @Override
    public String getName() {
        return GlobalConstants.REALM_NAME;
    }


    /**
     * 权限信息缓存的key
     *
     * @param principals
     * @return
     */
    @Override
    protected Object getAuthorizationCacheKey(PrincipalCollection principals) {
        return ((BiUser)principals.getPrimaryPrincipal()).getId();
    }
    /**
     * 授权
     * @param principalCollection
     * @return
     */
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principalCollection) {
        BiUser sysUser = (BiUser) principalCollection.getPrimaryPrincipal();
        log.info("doGetAuthorizationInfo, user is {}", sysUser.getId());
        List<String> permissions = sysMenuService.getPermissionsByUserId(sysUser.getId());
        SimpleAuthorizationInfo simpleAuthorizationInfo = new SimpleAuthorizationInfo();
        simpleAuthorizationInfo.addStringPermissions(permissions);
        return simpleAuthorizationInfo;
    }

    /**
     *
     * @param authenticationToken
     * @return
     * @throws AuthenticationException
     */
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authenticationToken) throws AuthenticationException {
        UsernamePasswordToken token = (UsernamePasswordToken) authenticationToken;
        String username = token.getUsername();
        log.info("doGetAuthorizationInfo, username is {}", username);

        BiUser user = new BiUser();
        //权限控制管理三方系统登录
        if (authenticationToken instanceof SystemAuthByThirdLogin) {
            user = sysUserService.getOneByJobNumber(username);
        } else {
            user = sysUserService.getByUserAccount(username);
        }
        if (user == null) {
            log.error("未查询到此用户 username={}", username);
            throw new CodeException(ThirdApiResponseCodeEnum.USER_NOT_EXIST.getCode(), ThirdApiResponseCodeEnum.USER_NOT_EXIST.getMessage());
        }
        //ad域 免密登陆
        if (authenticationToken instanceof NoPwdToken) {
            return new SimpleAuthenticationInfo(user, "", this.getClass().getSimpleName());
        } else {
            return new SimpleAuthenticationInfo(user, user.getPassword(),
                    ByteSource.Util.bytes(user.getSalt()), getName());
        }
    }

    /**
     * 清空缓存
     */
    public void clearCachedAuthorizationInfo() {
        this.clearCachedAuthorizationInfo(SecurityUtils.getSubject().getPrincipals());
    }
}
