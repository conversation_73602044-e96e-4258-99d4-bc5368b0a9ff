package com.r2.bi.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.r2.bi.dao.bi.SysOrganizeDao;
import com.r2.bi.entity.bi.SysOrganize;
import com.r2.bi.entity.bi.SysTaskResult;
import com.r2.bi.service.SysTaskResultService;
import com.r2.bi.vo.sysorg.SyncOranizeInfoRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@EnableScheduling
public class SyncOrganizationTaskService {

    @Resource
    private SysOrganizeDao sysOrganizeDao;

    @Resource
    private SysTaskResultService sysTaskResultService;

    @Value("${middle.url}")
    private String middleUrl;

    private final String path = "/bs/organization";

    private final Integer page = 9999;

    /**
     * 每天早上4点触发
     */
    @SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
    @Scheduled(cron = "0 0 4 * * ?")
    public void handelSyncOrganizationTask() {
        log.info("------------------同步中台组织部门信息定时任务开始-----------------");
        log.info("同步中台组织部门信息定时任务访问地址：" + middleUrl + path);
        try {
            //数据总条数
            Integer totalDataNum = 0;
            //新增数据条数
            Integer newDataNum = 0;
            //更新数据条数
            Integer updateDataNum = 0;

            //定时任务记录
            SysTaskResult sysTaskResult = new SysTaskResult();
            sysTaskResult.setName("同步中台组织部门信息定时任务");
            sysTaskResult.setCreateTime(new Date());
            sysTaskResultService.insert(sysTaskResult);

            //超时设置
            HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
            httpRequestFactory.setConnectionRequestTimeout(60000);
            httpRequestFactory.setConnectTimeout(60000);
            httpRequestFactory.setReadTimeout(60000);

            RestTemplate restTemplate = new RestTemplate(httpRequestFactory);
            List<HttpMessageConverter<?>> converterList = restTemplate.getMessageConverters();
            converterList.remove(1);
            HttpMessageConverter<?> converter = new StringHttpMessageConverter(Charset.forName("UTF-8"));
            converterList.add(1, converter);
            restTemplate.setMessageConverters(converterList);
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json");
            ResponseEntity<String> responseEntity = null;

            // 存储所有页面的组织信息
            List<SyncOranizeInfoRespVO> allOrganizeData = new ArrayList<>();

            for (int i = 0; i < this.page; i++) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("day", "0");
                jsonObject.put("parm", "code,name,oId,pOIdOrgAdmin,secondLevelOrganization,firstLevelOrganization,status,personincharge,modifiedTime,leaderWithSpecificDuty");
                jsonObject.put("page", i);
                HttpEntity<String> formEntity = new HttpEntity<>(JSON.toJSONString(jsonObject), headers);
                responseEntity = restTemplate.postForEntity(middleUrl + path, formEntity, String.class);
                if (ObjectUtil.isNotEmpty(responseEntity)) {
                    log.info("同步中台组织部门信息当前页：" + i);
                    String body = responseEntity.getBody();
                    if (StrUtil.isNotEmpty(body)) {
                        JSONObject jsonObj = JSON.parseObject(body);
                        String data = jsonObj.getString("data");
                        if (StrUtil.isNotEmpty(data)) {
                            JSONArray organizeInfos = JSON.parseArray(data);
                            List<SyncOranizeInfoRespVO> syncOranizeInfoRespVOS = BeanUtil.copyToList(organizeInfos, SyncOranizeInfoRespVO.class);
                            log.info("同步中台组织部门信息当前页数据：" + JSON.toJSONString(syncOranizeInfoRespVOS));

                            if (CollectionUtil.isNotEmpty(syncOranizeInfoRespVOS)) {
                                // 将当前页数据加入总列表
                                allOrganizeData.addAll(syncOranizeInfoRespVOS);
                            } else {
                                log.info("同步中台组织部门信息当前页无数据返回：" + i);
                                break;
                            }
                        }
                    }
                }
            }
            // 组装数据
            Map<Long, List<SyncOranizeInfoRespVO>> collect = allOrganizeData.stream()
                    .collect(Collectors.groupingBy(SyncOranizeInfoRespVO::getOId));

            for (SyncOranizeInfoRespVO syncOranizeInfoRespVO : allOrganizeData) {
                if ("0".equals(syncOranizeInfoRespVO.getOId().toString())) {
                    syncOranizeInfoRespVO.setParentcode("RootOrg");
                    syncOranizeInfoRespVO.setOId(1L);
                } else if (Objects.isNull(syncOranizeInfoRespVO.getFirstLevelOrganization()) &&
                        Objects.isNull(syncOranizeInfoRespVO.getOId())) {
                    syncOranizeInfoRespVO.setParentcode("RootOrg");
                } else {
                    if (StrUtil.isNotBlank(syncOranizeInfoRespVO.getSecondLevelOrganization())) {
                        List<SyncOranizeInfoRespVO> infoRespVOS = collect.get(Long.valueOf(syncOranizeInfoRespVO.getSecondLevelOrganization()));
                        if (Objects.nonNull(infoRespVOS)) {
                            for (SyncOranizeInfoRespVO oranizeInfoRespVO : infoRespVOS) {
                                if (oranizeInfoRespVO.getOId().toString().equals(syncOranizeInfoRespVO.getSecondLevelOrganization())) {
                                    syncOranizeInfoRespVO.setSecondlevelorgcode(oranizeInfoRespVO.getCode());
                                }
                            }
                        }
                    }
                    if (StrUtil.isNotBlank(syncOranizeInfoRespVO.getPOIdOrgAdmin().toString())) {
                        List<SyncOranizeInfoRespVO> oranizeInfoRespVOList = collect.get(syncOranizeInfoRespVO.getPOIdOrgAdmin());
                        if (Objects.nonNull(oranizeInfoRespVOList)) {
                            for (SyncOranizeInfoRespVO oranizeInfoRespVO : oranizeInfoRespVOList) {
                                if (oranizeInfoRespVO.getOId().toString().equals(syncOranizeInfoRespVO.getPOIdOrgAdmin().toString())) {
                                    syncOranizeInfoRespVO.setParentcode(oranizeInfoRespVO.getCode());
                                }
                            }
                        }
                    }
                }
                totalDataNum++;
                // 查看当前组织部门是否为新增
                QueryWrapper<SysOrganize> sysOrganizeQueryWrapper = new QueryWrapper<>();
                if (StrUtil.isNotEmpty(syncOranizeInfoRespVO.getCode())) {
                    sysOrganizeQueryWrapper.lambda().eq(SysOrganize::getOrganizeCode, syncOranizeInfoRespVO.getCode());
                }
                sysOrganizeQueryWrapper.lambda().eq(SysOrganize::getIsDelete, 0);
                sysOrganizeQueryWrapper.lambda().last("limit 1");

                SysOrganize sysOrganize = sysOrganizeDao.selectOne(sysOrganizeQueryWrapper);
                SysOrganize sysOrganizeEntity = makeSysOrganizeEntity(syncOranizeInfoRespVO);

                if (ObjectUtil.isNotEmpty(sysOrganize)) {
                    sysOrganizeEntity.setId(sysOrganize.getId());
                    sysOrganizeEntity.setUpdateTime(new Date());
                    sysOrganizeDao.updateById(sysOrganizeEntity);
                    updateDataNum++;
                } else {
                    sysOrganizeEntity.setCreateTime(new Date());
                    sysOrganizeEntity.setUpdateTime(new Date());
                    sysOrganizeDao.insert(sysOrganizeEntity);
                    newDataNum++;
                }
            }
            sysTaskResult.setResult("数据总条数:" + totalDataNum + ";更新数据条数:" + updateDataNum + ";新增数据条数:" + newDataNum);
            sysTaskResult.setUpdateTime(new Date());
            sysTaskResultService.updateById(sysTaskResult);
        } catch (Exception e) {
            log.error("中心定时任务异常 error={}", e.getMessage());
        }
        log.info("------------------同步中台组织部门信息定时任务结束-----------------");
    }

    private SysOrganize makeSysOrganizeEntity(SyncOranizeInfoRespVO syncOranizeInfoRespVO) {
        SysOrganize sysOrganize = new SysOrganize();
        sysOrganize.setOrganizeCode(syncOranizeInfoRespVO.getCode());
        sysOrganize.setId(syncOranizeInfoRespVO.getOId());
        sysOrganize.setOrganizeName(syncOranizeInfoRespVO.getName());
        sysOrganize.setParentCode(syncOranizeInfoRespVO.getParentcode());
        sysOrganize.setSecondLevelOrgCode(syncOranizeInfoRespVO.getSecondlevelorgcode());
        sysOrganize.setStatus(syncOranizeInfoRespVO.getStatus());
//        sysOrganize.setOld(syncOranizeInfoRespVO.getOId().toString());
        if (StrUtil.isNotEmpty(syncOranizeInfoRespVO.getPersonincharge())) {
            sysOrganize.setPersonInCharge(Long.parseLong(syncOranizeInfoRespVO.getPersonincharge()));
        }
        sysOrganize.setUpdateDay(DateUtil.parse(syncOranizeInfoRespVO.getModifiedTime(), "yyyy-MM-dd"));
        sysOrganize.setUpdateTime(new Date());

        return sysOrganize;
    }
}
