package com.r2.bi.task;

import com.r2.bi.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
@EnableScheduling
public class SyncUserTaskService {

    @Resource
    private SysUserService sysUserService;
    
    /**
     * 每天早上3点触发
     */
    @Scheduled(cron = "0 0 5 * * ?")
    public void handelSyncOrganizationTask() {
        log.info("------------------同步主数据平台用户信息定时任务开始-----------------");
        sysUserService.syncUser();
        log.info("------------------同步主数据平台用户信息定时任务结束-----------------");
    }
}
