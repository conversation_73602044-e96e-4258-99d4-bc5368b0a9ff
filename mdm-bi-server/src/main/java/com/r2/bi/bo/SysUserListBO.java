package com.r2.bi.bo;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class SysUserListBO {


    /**
     * 用户id
     */
    private Long id;

    /**
     * 主数据平台 用户id
     */
    private Long userId;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 手机号
     */
    private String mobile;
    /**
     * 用户名称
     */
    private String username;
    /**
     * 角色
     */
    private String roleIds;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 职务
     */
    private String positionCode;
    /**
     * 直属领导姓名
     */
    private String poiDempAdminName;
    /**
     * 部门
     */
    private String organizeCode;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 来源类型
     */
    private Integer sourceType;
    /**
     * 职位
     */
    private String dutyName;

    /**
     * 所有数据权限：1：是；0：否
     */
    private Integer dataPermission;

    /**
     * 创建时间
     */
    private String createTime;
}
