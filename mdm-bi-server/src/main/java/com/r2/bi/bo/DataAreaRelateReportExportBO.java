package com.r2.bi.bo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DataAreaRelateReportExportBO {
    /**
     *区域
     */
    private String areaName;

    /**
     * v1.2区域内中心平均完整度
     */
    private String areaCenterIntegrityPlus;

    /**
     * v1.2 区域中心无效数据占比 7模块
     */
    private String areaInvalidDataPlus;

    /**
     * 区域负责人
     */
    private String director;

    /**
     * 中心名称
     */
    private String centerName;

    /**
     * SMO更新人
     */
    private String updator;

    /**
     * 更新人登录次数
     */
    private String smoUpdatorLoginNum;

    /**
     * 更新人最后登录时间
     */
    private String smoUpdatorLoginTime;


    /**
     * v1.2字段填写完整度
     */
    private String centerIntegrityPlus;

    /**
     * v1.2中心无效数据占比
     */
    private String centerInvalidDataPlus;

    /**
     * 中心最新更新时间
     */
    private String centerUpdateTime;

    /**
     * 中心科室数量
     */
    private String deptSize;

    /**
     * 中心科室数据完整度
     */
    private String deptCenterIntegrity;

    /**
     * 中心科室无效数据占比
     */
    private String invalidDeptIntegrity;

    /**
     * 中心人员数量
     */
    private String memberSize;

    /**
     * 中心人员数据完整度
     */
    private String memberCenterIntegrity;
    /**
     * 中心人员无效数据占比
     */
    private String invalidMemberIntegrity;

}
