package com.r2.bi.bo;

/**
 * <AUTHOR>
 */
public class BiStartSpeedResponseDTO {

    private ContractCountDTO sivCount;
    private ContractCountDTO projectCount;
    private ContractCountDTO ethicsCount;
    private ContractCountDTO contractCount;
    private ContractCountDTO hgrCount;
    /**
     * 是否立项前置
     * */
    private Boolean kickoffRqmt;
    /**
     * 是否伦理前置
     * */
    private Boolean ethicalPre;

    private String leaderApproval;
    private String ethicsAuditType;
    private String isGroupLeaderFile;
    private String communicationLetter;
    private Long centerId;

    // Getter & Setter（或用 Lombok 的 @Data）

    public ContractCountDTO getSivCount() {
        return sivCount;
    }

    public void setSivCount(ContractCountDTO sivCount) {
        this.sivCount = sivCount;
    }

    public ContractCountDTO getProjectCount() {
        return projectCount;
    }

    public void setProjectCount(ContractCountDTO projectCount) {
        this.projectCount = projectCount;
    }

    public ContractCountDTO getEthicsCount() {
        return ethicsCount;
    }

    public void setEthicsCount(ContractCountDTO ethicsCount) {
        this.ethicsCount = ethicsCount;
    }

    public ContractCountDTO getContractCount() {
        return contractCount;
    }

    public void setContractCount(ContractCountDTO contractCount) {
        this.contractCount = contractCount;
    }

    public ContractCountDTO getHgrCount() {
        return hgrCount;
    }

    public void setHgrCount(ContractCountDTO hgrCount) {
        this.hgrCount = hgrCount;
    }

    public Boolean getKickoffRqmt() {
        return kickoffRqmt;
    }

    public void setKickoffRqmt(Boolean kickoffRqmt) {
        this.kickoffRqmt = kickoffRqmt;
    }

    public String getLeaderApproval() {
        return leaderApproval;
    }

    public void setLeaderApproval(String leaderApproval) {
        this.leaderApproval = leaderApproval;
    }

    public String getEthicsAuditType() {
        return ethicsAuditType;
    }

    public void setEthicsAuditType(String ethicsAuditType) {
        this.ethicsAuditType = ethicsAuditType;
    }

    public String getIsGroupLeaderFile() {
        return isGroupLeaderFile;
    }

    public void setIsGroupLeaderFile(String isGroupLeaderFile) {
        this.isGroupLeaderFile = isGroupLeaderFile;
    }

    public String getCommunicationLetter() {
        return communicationLetter;
    }

    public void setCommunicationLetter(String communicationLetter) {
        this.communicationLetter = communicationLetter;
    }

    public Long getCenterId() {
        return centerId;
    }

    public void setCenterId(Long centerId) {
        this.centerId = centerId;
    }

    public Boolean getEthicalPre() {
        return ethicalPre;
    }

    public void setEthicalPre(Boolean ethicalPre) {
        this.ethicalPre = ethicalPre;
    }
}