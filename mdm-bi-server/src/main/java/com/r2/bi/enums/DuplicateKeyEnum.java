package com.r2.bi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2019/12/17
 * 枚举类，用来表示数据来源类型
 */

@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
@Getter
@AllArgsConstructor
public enum DuplicateKeyEnum {

    EMAIL("uniq_email", "邮箱"),
    MOBILE("uniq_mobile", "手机号"),
    USER_ACCOUNT("uniq_user_account", "用户账号"),
    JOB_NUMBER("uniq_job_number", "工号");

    private String code;
    private String name;

    public static String getNameByCode(String content) {
        for (DuplicateKeyEnum keyEnum : DuplicateKeyEnum.values()) {
            if (content.contains(keyEnum.getCode())) {
                return keyEnum.getName();
            }
        }
        return "参数";
    }

}
