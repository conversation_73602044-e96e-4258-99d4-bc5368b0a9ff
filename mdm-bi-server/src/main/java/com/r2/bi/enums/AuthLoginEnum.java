package com.r2.bi.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/27 16:58
 */
@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
@Getter
public enum AuthLoginEnum {

    SUCCESS(200, "成功"),
    PARAM_ERROR(4001, "参数异常"),
    SYSTEM_NOT_EXIST(4002, "系统不存在"),
    SYSTEM_STATUS_ERROR(4003, "系统状态异常"),
    USER_NOT_EXIST(4004, "用户不存在"),
    USER_STATUS_ERROR(4005, "用户状态异常"),
    USER_PASSWORD_ERROR(4006, "用户名或者密码错误"),
    USER_PASSWORD_EXPIRE(4007, "密码过期——您的密码已超过有效期，请重新设置密码"),
    LOCKED(4008, "密码错误超过5次——错误次数超过5次，请于24小时后再试"),
    SAME_AS_OLD(4009, "新密码不能与旧密码相同"),
    MISMATCH(4010, "旧密码不匹配"),
    VALID(4011, "密码不符合要求——新密码要求为8-16位英文（大+小写）+数字+符号组合"),
    SOURCE_ERROR(4012, "用户来源异常，不支持修改密码操作"),
    EMAIL_ERROR(4013, "用户邮箱异常"),
    CODE_VALID(4014, "验证码不存在或已失效"),
    CODE_ERROR(4015, "验证码不正确"),
    DOUBLE_SEND(4016, "请勿连续点击发送"),
    FIRST_LOGIN(4017, "外部用户首次登录，请修改密码"),
    CONFIRM_PASSWORD_NODE_MATCH(4017,"两次密码输入不一致！"),
    DOMAIN_ERROR(5001, "域用户登录异常，请联系管理员"),
    DOMAIN_PASSWORD_ERROR(5002, "域用户登录异常，密码过期"),
    NOT_INTERNAL_USERS(5003,"内部用户无法通过此入口修改密码！");

    AuthLoginEnum(Integer errorCode, String message) {
        this.errorCode = errorCode;
        this.message = message;
    }

    private Integer errorCode;

    private String message;
}
