package com.r2.bi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/10/17
 * 第三方API响应码枚举类
 */

@SuppressWarnings("AlibabaEnumConstantsMustHaveComment")
@Getter
@AllArgsConstructor
public enum ThirdApiResponseCodeEnum {

    SUCCESS(200, "成功"),
    PARAM_ERROR(4001, "MDM:参数异常"),
    SYSTEM_NOT_EXIST(4002, "MDM:系统不存在"),
    SYSTEM_STATUS_ERROR(4003, "MDM:系统状态异常"),
    USER_NOT_EXIST(4004, "MDM:用户不存在"),
    USER_STATUS_ERROR(4005, "MDM:用户状态异常"),
    USER_PASSWORD_ERROR(4006, "MDM:用户名或者密码错误"),
    USER_PASSWORD_EXPIRE(4007, "MDM:密码过期——您的密码已超过有效期，请重新设置密码"),
    LOCKED(4008, "MDM:密码错误超过5次——错误次数超过5次，请于10分钟后再试"),
    SAME_AS_OLD(4009, "MDM:新密码不能与旧密码相同"),
    MISMATCH(4010, "MDM:旧密码不匹配"),
    VALID(4011, "MDM:密码不符合要求——新密码要求为8-16位英文（大+小写）+数字+符号组合"),
    SOURCE_ERROR(4012, "MDM:用户来源异常，不支持修改密码操作"),
    EMAIL_ERROR(4013, "MDM:用户邮箱异常"),
    CODE_VALID(4014, "MDM:验证码不存在或已失效"),
    CODE_ERROR(4015, "MDM:验证码不正确"),
    DOUBLE_SEND(4016, "MDM:请勿连续点击发送"),
    FIRST_LOGIN_PASSWORD_UPDATE(4017, "MDM:外部用户首次登录，请修改密码"),
    DOMAIN_ERROR(5001, "AD:域用户登录异常，请联系管理员"),
    DOMAIN_PASSWORD_EXPIRE(5002, "AD:域用户登录异常，密码过期"),
    DOMAIN_NOT_EXIST(5003, "AD:用户不存在"),
    DOMAIN_INVALID(5004, "AD:密码或凭证无效"),
    DOMAIN_NOT_ALLOW_LOGIN(5005, "AD:此时不允许登录"),
    DOMAIN_ACCOUNT_DISABLE(5006, "AD:账户禁用"),
    DOMAIN_ACCOUNT_EXPIRE(5007, "AD:账户过期"),
    DOMAIN_RESET_PASSWORD(5008, "AD:用户必须重置密码"),
    DOMAIN_ACCOUNT_LOCK(5009, "AD:用户账户锁定");

    private int code;
    private String message;

    public static int getCodeByMessage(String message) {
        for (ThirdApiResponseCodeEnum responseCode : ThirdApiResponseCodeEnum.values()) {
            if (responseCode.getMessage().equals(message)) {
                return responseCode.getCode();
            }
        }
        throw new IllegalArgumentException("No matching code for message: " + message);
    }

    public static String getMessageByCode(int code) {
        for (ThirdApiResponseCodeEnum responseCode : ThirdApiResponseCodeEnum.values()) {
            if (responseCode.getCode() == code) {
                return responseCode.getMessage();
            }
        }
        throw new IllegalArgumentException("No matching message for code: " + code);
    }
}
