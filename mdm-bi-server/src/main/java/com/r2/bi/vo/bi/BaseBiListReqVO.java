package com.r2.bi.vo.bi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseBiListReqVO implements Serializable {
    private static final long serialVersionUID = -5039061426369257455L;

    /**
     * 页
     */
    private int page = 1;

    /**
     * 页大小
     */
    private int size = 10;

    /**
     * 是否需要分页
     */
    private Boolean needPage = true;

    /**
     * 偏移量
     */
    private Integer offset;

    public int getOffset() {
        if (null == offset) {
            return (Math.max(this.getPage() - 1, 0)) * this.getSize();
        }
        return offset;
    }
}
