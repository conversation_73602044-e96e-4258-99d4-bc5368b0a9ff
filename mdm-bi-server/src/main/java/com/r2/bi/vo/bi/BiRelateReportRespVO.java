package com.r2.bi.vo.bi;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BiRelateReportRespVO {


    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 省市
     */
    private String provinceCity;

    /**
     * SMO更新人
     */
    private String updator;

    /**
     * smo更新人登录次数 2023年
     */
    private Integer smoUpdatorLoginNum;


    /**
     * smo更新人最后登录时间
     */
    private String smoUpdatorLoginTime;

    /**
     * 中心字段填写完整度 v1.2 7模块
     */
    private String centerIntegrityPlus;

    /**
     * v1.2中心无效数据占比
     */
    private String centerInvalidDataPlus;

    /**
     * 中心最新更新时间
     */
    private String centerUpdateTime;
    /**
     * 中心科室数量
     */
    private String deptSize;


    /**
     * 中心科室数据完整度
     */
    private String deptCenterIntegrity;

    /**
     * 中心科室无效数据占比
     */
    private String invalidDeptIntegrity;

    /**
     * 中心人员数量
     */
    private String memberSize;

    /**
     * 中心人员数据完整度
     */
    private String memberCenterIntegrity;
    /**
     * 中心人员无效数据占比
     */
    private String invalidMemberIntegrity;

    /**
     * 中心名称
     */
    private String centerName;


}
