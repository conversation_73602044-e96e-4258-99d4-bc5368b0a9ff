package com.r2.bi.vo.bi.center;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SummaryVO {

    /**
    *  区域列表
    * */
    private List<AreaRespVO> regions;

    /**
     * 中心数
     **/
    private Integer totalCenters;

    /**
     * 中心覆盖数
     **/
    private Integer totalCenterCoverNum;

    /**
     * SMO优选数
     **/

    private Integer totalSmoNumber;
    /**
     * pi数
     **/
    private Integer totalPiNumber;

    /**
     * 本月更新数
     **/
    private Integer totalUpdateNumber;
    /**
     * 覆盖率
     **/
    private String coverageRate;

    /**
     * 信息完成率
     **/
    private String informationCompletion;

    /**
     * 最快启动时长
     **/
    private String fastestTime;
    /**
     * 平均启动时长
     **/
    private String averageTime;

}

