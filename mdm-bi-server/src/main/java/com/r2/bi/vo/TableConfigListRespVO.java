package com.r2.bi.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TableConfigListRespVO {

    /**
     * 字段显示名称
     */
    private String displayName;

    /**
     * db字段名称
     */
    private String columnName;
    /**
     * 字段备注
     */
    private String columnComment;
    /**
     * 是否必填：1：是，0：否
     */
    private String isNullable;

    /**
     * 是否隐藏：1：是，0：否
     */
    private Integer isHidden;

    /**
     * 是否可编辑：1：是，0：否
     */
    private Integer isEdit;

    /**
     * 字段类型：1.字符串[单行] 2. 字符串[多行] 3.下拉框 4. 单选 5. 省市区
     *
     */
    private String columnType;

    /**
     * 默认数据
     */
    private List columnData;

    /**
     * 排序
     */
    private String sort;

    /**
     * 是否动态编辑：1：是，0：否
     */
    private List<Integer> dynamicEdit;

    /**
     * 是否请求接口
     */
    private String requestApi;

    /**
     * 是否全量查询
     */
    private Integer queryAll;

    /**
     * 模糊搜索
     */
    private Integer queryLike;

    /**
     * 字段长度
     */
    private Integer maxLength;

    /**
     * 是否动态输入：1：是，0：否
     */
    private List<Integer> dynamicInput;

    /**
     * 字段提示
     */
    private String tips;

    /**
     * 将数据填充至配置字段
     */
    private String fillColumn;
}
