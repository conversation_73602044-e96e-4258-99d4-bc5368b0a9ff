package com.r2.bi.vo.sysUser;

import lombok.Data;
@SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
@Data
public class SyncSysUserInfoRespVO {
    /**
     * 用户id
     **/
    private Long userid;
    //工号
    private String jobnumber;
    //姓名
    private String username;
    //组织编码
    private String organizecode;
    //二级组织编码
    private String secondlevelorgcode;
    //组织状态
    private Integer organizestatus;
    //职位编码
    private String positioncode;
    //上级员工id
    private Long poidempadmin;
    //上级员工编码
    private String superioremployeecode;
    //邮箱地址
    private String email;
    //手机号码
    private String mobile;
    //员工入职日期
    private String onboarding;
    //职务编码
    private String dutyname;
    //性别
    private Integer sex;
    //QQ号
    private String qq;
    //微信号
    private String wechat;
    //生日
    private String birthday;
    //身份证号
    private String idcard;
    //备注信息
    private String remark;
    //地址
    private String addr;
    //员工状态
    private String employeestatus;
    //是否当前记录
    private String iscurrentrecord;
    //员工类型
    private Integer employtype;
    //更新时间
    private String update_day;
    //最后工作日期
    private String lastworkday;
}
