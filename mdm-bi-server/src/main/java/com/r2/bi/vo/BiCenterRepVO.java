package com.r2.bi.vo;

import com.r2.bi.vo.bi.BaseBiListReqVO;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BiCenterRepVO extends BaseBiListReqVO {
    /**
     * 中心（中心名称、中心别名）
     */
    private String name;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;


    /**
     * 是否先支付首付款再启动
     */
    private String centerRqmt;


    /**
     * 是否伦理前置
     */
    private String ethicalPre;

    /**
     * 代表使用哪个字段排序
     */
    private String field;


    /**
     * 代表升序或降序——ascend 升序，descend 降序
     */
    private String order;

}
