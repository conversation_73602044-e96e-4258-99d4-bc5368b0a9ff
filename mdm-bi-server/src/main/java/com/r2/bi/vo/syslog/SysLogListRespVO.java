package com.r2.bi.vo.syslog;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SysLogListRespVO {
    /**
     * id
     */
    private Long id;
    /**
     * 系统模块
     */
    private String moduleName;
    /**
     * 操作类型
     */
    private String operateType;
    /**
     * 请求方式
     */
    private String method;
    /**
     * 操作人员
     */
    private String username;
    /**
     * 请求参数
     */
    private String params;
    /**
     * 响应结果
     */
    private String exceptionDetail;
    /**
     * 操作地址
     */
    private String requestIp;
    /**
     * 操作状态：1：成功；2：失败
     */
    private Integer operateStatus;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
