package com.r2.bi.vo.centermeeting;

import lombok.Data;

/**
 * 日历(CenterMeeting)表字符串拼接查询返回模型
 *
 * <AUTHOR>
 * @since 2022-11-10 17:58:32
 */
@Data
public class CenterMeetingQueryStrRespVO {

    private Long id;
    //中心id
    private Long centerId;
    /**
    * 中心标准编码
    * */
    private String standardCode;
    /**
     * 中心别名
     * */
    private String alias;

    /**
     * 省
     * */
    private String province;

    /**
     * 市
     * */
    private String city;

    //会议类型：立项会/伦理会/合同会
    private String type;
    //重复类型：月/周/指定周/指定日期
    private String repeatType;
    //重复类型：周期类型
    private String cycle;
    //重复次数
    private String repeatNum;
    //开始日期
    private String startDate;
    //结束日期
    private String endDate;
    //会议时间段：上午/下午
    private String period;
    //重复信息（英文逗号隔开）
    private String repeatDetails;
    //重复周
    private String repeatWeek;

    //中心名称,生成日历格式用
    private String centerName;
}

