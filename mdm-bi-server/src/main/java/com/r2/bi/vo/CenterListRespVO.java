package com.r2.bi.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class CenterListRespVO {

    /**
     * id
     */
    private Long id;
    /**
     * 中心标准名称
     */
    @JSONField(name = "center_name")
    private String centerName;

    /**
     * 下拉列表使用
     */
    private String name;

    /**
     * 中心标准编码
     */
    private String standardCode;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 中心科室
     */
    private Integer deptNum;

    /**
     * 来源
     */
    private String source;

    /**
     * 更新时间
     */
    private Date dateModified;
}
