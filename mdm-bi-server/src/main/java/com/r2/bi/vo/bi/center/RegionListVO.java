package com.r2.bi.vo.bi.center;

import com.r2.bi.entity.cs.Center;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class RegionListVO {
    @Data
    public static class RegionVO {
        /**
        * 区域id
        * */
        private String regionId;
        /**
         * 区域名称
         * */
        private String regionName;
        /**
        * 省份
        * */
        private List<DistrictVO> districts = new ArrayList<>();

        // Getters and setters
    }
    @Data
    public static class DistrictVO {
        /**
         * 省份名称
         * */
        private String districtName;
        /**
         * 城市
         * */
        private List<CityVO> cities = new ArrayList<>();

        /**
         * 中心
         * */
        private List<Center> centers = new ArrayList<>();

        // Getters and setters
    }
    @Data
    public static class CityVO {
        /**
         * 城市名称
         * */
        private String cityName;
        /**
         * 中心
         * */
        private List<Center> centers = new ArrayList<>();

        // Getters and setters
    }
}