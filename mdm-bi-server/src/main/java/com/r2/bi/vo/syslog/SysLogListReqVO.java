package com.r2.bi.vo.syslog;

import com.r2.bi.vo.BaseListReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysLogListReqVO extends BaseListReqVO {
    /**
     * id
     */
    private Integer id;

    /**
     * 系统模块名称
     */
    private String moduleName;

    /**
     * 操作人员
     */
    private String username;

    /**
     * 操作类型 新增，删除，修改
     */
    private String operateType;

    /**
     * 操作状态：1：成功；2：失败
     */
    private Integer operateStatus;

    /**
     * 响应结果
     */
    private String exceptionDetail;

    /**
     * 操作开始时间
     */
    private LocalDateTime startTime;

    /**
     * 操作结束时间
     */
    private LocalDateTime endTime;
}
