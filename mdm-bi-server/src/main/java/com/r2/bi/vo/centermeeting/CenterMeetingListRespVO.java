package com.r2.bi.vo.centermeeting;

import com.r2.bi.entity.cs.CenterMeetingCycle;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 日历(CenterMeeting)表列表请求返回模型
 *
 * <AUTHOR>
 * @since 2022-11-10 17:58:32
 */
@Data
public class CenterMeetingListRespVO {
        
    private Long id;
    //是否删除 0未删除 1删除
    private Integer isDelete;
    //删除标识 
    private String deleteFlag;
    //创建日期
    private Date datecreated;
    //修改日期
    private Date datemodified;
    //创建用户id
    private String createdby;
    //创建用户
    private String createdbyname;
    //修改用户id
    private String modifiedby;
    //修改用户
    private String modifiedbyname;
    //中心id
    private Long centerId;
    //会议类型：立项会/伦理会
    private String type;
    //重复类型：月/周/指定周/指定日期
    private String repeatType;
    //重复次数
    private String repeatNum;
    //开始日期
    private String startDate;
    //结束日期
    private String endDate;
    //周期表
    private List<CenterMeetingCycle> meetingCycleList;
}

