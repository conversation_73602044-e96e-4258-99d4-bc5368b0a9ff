package com.r2.bi.vo.sysaccesssystem;

import lombok.Data;

@Data
public class SystemUserRespVO {

    /**
     * 用户id
     */
    private Long id;


    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 姓名
     */
    private String username;

    /**
     * 姓名
     */
    private String userAccount;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 职位
     */
    private String dutyName;

    /**
     * 部门
     */
    private String dept;
    /**
     * 组织部门code
     */
    private String organizeCode;
    /**
     * 直属领导userid
     */
    private Long poiDempAdmin;

    /**
     * 直属领导名称
     */
    private String poiDempAdminName;

    /**
     * 在职状态 在职：2,3
     */
    private String employeeStatus;

    /**
     * 用户来源
     */
    private String sourceType;

    /**
     * 权限状态
     */
    private String authStatus;

    /**
     * 手动修改/自动修改：1：自动；2：手动
     */
    private Integer autoEdit;

    private Integer manually;

    private Integer administrator;

    /**
     * 密码重置时间
     */
    private String resetDay;

}
