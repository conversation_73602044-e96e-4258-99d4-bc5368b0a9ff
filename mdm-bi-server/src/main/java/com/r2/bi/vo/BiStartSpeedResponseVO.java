package com.r2.bi.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BiStartSpeedResponseVO {
    /**
     * 中心
     * */
    private Long centerId;

    /**
     * 是否立项前置
     * */
    private Boolean kickoffRqmt;
    /**
     * 是否伦理前置
     * */
    private Boolean ethicalPre;

    /**
     * 立项需要组长单位批件
     * */
    private String isGroupLeaderFile;

    /**
     * 无国家局批件立项是否可以前置
     * */
    private String communicationLetter;

    /**
     * 无组长单位批件伦理是否可以前置
     * */
    private String leaderApproval;

    /**
     * 伦理审查形式
     * */
    private String ethicsAuditType;
    /**
    * 递交至SIV
    * */
    private ContractCount sivCount;
    /**
     * 立项递交至立项完成
     * */
    private ContractCount projectCount;
    /**
     * 伦理递交至伦理完成
     * */
    private ContractCount ethicsCount;
    /**
     * 合同递交至合同签署
     * */
    private ContractCount contractCount;
    /**
     * 分中心HGR备案递交至盖章完成
     * */
    private ContractCount hgrCount;


    @Data
    public static class ContractCount {
        private int days;
        private int offset;
        private int diffDays;

        @JsonIgnore
        public int getDiffDays() { return diffDays; } // 忽略此字段
        public void setDiffDays(int diffDays) { this.diffDays = diffDays; }
    }




}
