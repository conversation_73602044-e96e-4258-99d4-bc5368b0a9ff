package com.r2.bi.vo.sysaccesssystem;

import lombok.Data;

import java.util.Date;
import java.util.List;

@SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
@Data
public class AccessListRespVO {

    /**
     * id
     */
    private Integer id;
    /**
     * 系统名称
     */
    private String name;
    /**
     * 系统编码
     */
    private String code;
    /**
     * 英文名称
     */
    private String nameEn;
    /**
     * 别名
     */
    private String alias;
    /**
     * 部门
     */
    private List<String> dept;
    /**
     * 部门字符串
     */
    private String deptStr;

    /**
     * 系统说明
     */
    private String description;

    /**
     * 中心未匹配列表数量
     */
    private Integer centerMismatchNUm;

    /**
     * 中心已匹配列表数量
     */
    private Integer centerMatchedNum;

    /**
     * 科室未匹配列表数量
     */
    private Integer deptMismatchNUm;

    /**
     * 科室已匹配列表数量
     */
    private Integer deptMatchedNum;


    /**
     * 适应症未匹配列表数量
     */
    private Integer indicationMismatchNUm;

    /**
     * 适应症已匹配列表数量
     */
    private Integer indicationMatchedNum;

    /**
     * 类型：1：系统 ；2：自定义表格
     */
    private Integer type;

    /**
     * 表格名称
     */
    private String tabulatioName;



    /**
     * 状态：正常/已停用
     */
    private String status;
    /**
     * 是否删除：0：未删除；1：已删除
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 用户数量
     */
    private Integer userNum;
}
