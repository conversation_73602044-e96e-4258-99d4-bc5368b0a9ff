package com.r2.bi.vo.centermeeting;

import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
public class CalendarExportVO {
    /**
    * 区域
    * */
    private String region;
    /**
     * 合并省市
     * */
    private String provinceCity;
    /**
     * 中心编码
     * */
    private String centerCode;
    /**
     * 中心名称
     * */
    private String centerName;
    /**
     * 中心别名
     * */
    private String centerAlias;
    /**
     * 立项会议时间
     * */
    private LocalDate projectMeetingDate;

    /**
     * 会议时间段
     * */
    private String period;
    /**
     * 伦理会议时间
     * */
    private LocalDate ethicsMeetingDate;


}
