package com.r2.bi.vo.bi.center;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AreaRespVO {

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 中心数
     **/
    private Integer centerNum;

    /**
     * 中心覆盖数
     **/
    private Integer centerCoverNum;
    /**
     * 覆盖率
     **/
    private String coverageRate;

    /**
     * SMO优选数
     **/
    private Integer smoNumber;
    /**
     * 当月更新数
     **/
    private Integer updateNumber;

    /**
     * SMO中心数
     **/
    private Integer updateNum;


    /**
     * 信息完成率
     **/
    private String informationCompletion;
    /**
     * pi数
     **/
    private Integer piNumber;
    /**
     * 最快启动时长
     **/
    private String fastestTime;
    /**
     * 平均启动时长
     **/
    private String averageTime;
    /**
     * 省份列表
     */
    private List<ProvinceVO> provinces;

    // Getters and Setters
    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getSmoNumber() {
        return smoNumber;
    }

    public void setSmoNumber(Integer smoNumber) {
        this.smoNumber = smoNumber;
    }

    public List<ProvinceVO> getProvinces() {
        return provinces;
    }

    public void setProvinces(List<ProvinceVO> provinces) {
        this.provinces = provinces;
    }

    public Integer getCenterNum() {
        return centerNum;
    }

    public void setCenterNum(Integer centerNum) {
        this.centerNum = centerNum;
    }

    public void setCenterCoverNum(Integer centerCoverNum) {
        this.centerCoverNum = centerCoverNum;
    }

    public Integer getCenterCoverNum() {
        return centerCoverNum;
    }

    public String getCoverageRate() {
        return coverageRate;
    }

    public void setCoverageRate(String coverageRate) {
        this.coverageRate = coverageRate;
    }

    public Integer getUpdateNumber() {
        return updateNumber;
    }

    public void setUpdateNumber(Integer updateNumber) {
        this.updateNumber = updateNumber;
    }

    public String getInformationCompletion() {
        return informationCompletion;
    }

    public void setInformationCompletion(String informationCompletion) {
        this.informationCompletion = informationCompletion;
    }

    public Integer getPiNumber() {
        return piNumber;
    }

    public void setPiNumber(Integer piNumber) {
        this.piNumber = piNumber;
    }

    public String getFastestTime() {
        return fastestTime;
    }

    public void setFastestTime(String fastestTime) {
        this.fastestTime = fastestTime;
    }

    public String getAverageTime() {
        return averageTime;
    }

    public void setAverageTime(String averageTime) {
        this.averageTime = averageTime;
    }

    // 内部省份 vo 类
    public static class ProvinceVO {
        /**
        * 省份名称
        * */
        private String province;

        /**
         * 中心数
         **/
        private Integer centerNum;

        /**
         * 中心覆盖数
         **/
        private Integer centerCoverNum;
        /**
         * 覆盖率
         **/
        private String coverageRate;

        /**
         * SMO优选数
         **/
        private Integer smoNumber;

        /**
         * 当月更新数
         **/
        private Integer updateNumber;
        /**
         * 信息完成率
         **/
        private String informationCompletion;
        /**
         * pi数
         **/
        private Integer piNumber;
        /**
         * 最快启动时长
         **/
        private String fastestTime;
        /**
         * 平均启动时长
         **/
        private String averageTime;

        /**
         * 城市列表
         * */
        private List<CityVO> cities;

        // Getters and Setters
        public String getProvince() {
            return province;
        }

        public void setProvince(String province) {
            this.province = province;
        }

        public Integer getSmoNumber() {
            return smoNumber;
        }

        public void setSmoNumber(Integer smoNumber) {
            this.smoNumber = smoNumber;
        }

        public List<CityVO> getCities() {
            return cities;
        }

        public void setCities(List<CityVO> cities) {
            this.cities = cities;
        }

        public Integer getCenterNum() {
            return centerNum;
        }

        public void setCenterNum(Integer centerNum) {
            this.centerNum = centerNum;
        }

        public void setCenterCoverNum(Integer centerCoverNum) {
            this.centerCoverNum = centerCoverNum;
        }

        public Integer getCenterCoverNum() {
            return centerCoverNum;
        }

        public String getCoverageRate() {
            return coverageRate;
        }

        public void setCoverageRate(String coverageRate) {
            this.coverageRate = coverageRate;
        }

        public Integer getUpdateNumber() {
            return updateNumber;
        }

        public void setUpdateNumber(Integer updateNumber) {
            this.updateNumber = updateNumber;
        }

        public String getInformationCompletion() {
            return informationCompletion;
        }

        public void setInformationCompletion(String informationCompletion) {
            this.informationCompletion = informationCompletion;
        }

        public Integer getPiNumber() {
            return piNumber;
        }

        public void setPiNumber(Integer piNumber) {
            this.piNumber = piNumber;
        }

        public String getFastestTime() {
            return fastestTime;
        }

        public void setFastestTime(String fastestTime) {
            this.fastestTime = fastestTime;
        }

        public String getAverageTime() {
            return averageTime;
        }

        public void setAverageTime(String averageTime) {
            this.averageTime = averageTime;
        }

        // 内部城市 DTO 类
        public static class CityVO {

            /**
            * 城市名称
            * */
            private String city;

            /**
             * 中心数
             **/
            private Integer centerNum;

            /**
             * 中心覆盖数
             **/
            private Integer centerCoverNum;
            /**
             * 覆盖率
             **/
            private String coverageRate;
            /**
             * SMO优选数
             **/
            private Integer smoNumber;
            /**
             * 当月更新数
             **/
            private Integer updateNumber;
            /**
             * 信息完成率
             **/
            private String informationCompletion;
            /**
             * pi数
             **/
            private Integer piNumber;
            /**
             * 最快启动时长
             **/
            private String fastestTime;
            /**
             * 平均启动时长
             **/
            private String averageTime;

            // Getters and Setters
            public String getCity() {
                return city;
            }

            public void setCity(String city) {
                this.city = city;
            }

            public Integer getSmoNumber() {
                return smoNumber;
            }

            public void setSmoNumber(Integer smoNumber) {
                this.smoNumber = smoNumber;
            }

            public Integer getCenterNum() {
                return centerNum;
            }

            public void setCenterCoverNum(Integer centerCoverNum) {
                this.centerCoverNum = centerCoverNum;
            }

            public Integer getCenterCoverNum() {
                return centerCoverNum;
            }

            public void setCenterNum(Integer centerNum) {
                this.centerNum = centerNum;
            }
            public String getCoverageRate() {
                return coverageRate;
            }

            public void setCoverageRate(String coverageRate) {
                this.coverageRate = coverageRate;
            }

            public Integer getUpdateNumber() {
                return updateNumber;
            }

            public void setUpdateNumber(Integer updateNumber) {
                this.updateNumber = updateNumber;
            }

            public String getInformationCompletion() {
                return informationCompletion;
            }

            public void setInformationCompletion(String informationCompletion) {
                this.informationCompletion = informationCompletion;
            }

            public Integer getPiNumber() {
                return piNumber;
            }

            public void setPiNumber(Integer piNumber) {
                this.piNumber = piNumber;
            }

            public String getFastestTime() {
                return fastestTime;
            }

            public void setFastestTime(String fastestTime) {
                this.fastestTime = fastestTime;
            }

            public String getAverageTime() {
                return averageTime;
            }

            public void setAverageTime(String averageTime) {
                this.averageTime = averageTime;
            }
        }
    }
}

