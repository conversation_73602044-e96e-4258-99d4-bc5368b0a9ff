package com.r2.bi.vo.sysrole;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class SysRoleUpdateReqVO {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Integer id;

    /**
     * 角色编码
     */
    @NotBlank(message = "角色编码不能为空")
    private String code;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    private String name;

    /**
     * 是否脱敏
     */
    private String dataScope;

    /**
     * 角色说明
     */
    private String description;

    /**
     * 角色权限
     */
    private List<Integer> authList;
}
