package com.r2.bi.vo.sysmenu;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SysMenuListRespVO {

    /**
     * id
     */
    private Integer id;

    /**
     * 上级类目id
     */
    private Integer pid;

    /**
     * 菜单标题
     */
    private String title;

    /**
     * 组件名称
     */
    private String name;

    /**
     * 菜单类型：1：目录；2：菜单；3：按钮
     */
    private Integer type;

    /**
     * 排序
     */
    private Integer menuSort;

    /**
     * 图标
     */
    private String icon;

    /**
     * 路由地址
     */
    private String route;

    /**
     * 状态[菜单是否可见] 1启用[可见] 2停用[不可见]
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 子节点
     */
    private List<SysMenuListRespVO> children;
}
