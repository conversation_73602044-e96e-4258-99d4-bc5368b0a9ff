package com.r2.bi.vo.sysorg;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@NoArgsConstructor
@Data
public class SysOrgTreeNodeRespVO {

    private Long id;
    // 上级组织
    private String organizeCode;

    private String organizeName;

    private String parentCode;

    private String secondLevelOrgCode;

    private Integer status;

    private Long personInCharge;

    private Date updateDay;
    // 是否删除：0：未删除；1：已删除
    private Integer isDelete;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;

    /**
     * 子菜单
     */
    public List<SysOrgTreeNodeRespVO> children = new ArrayList<SysOrgTreeNodeRespVO>();

    public List<SysOrgTreeNodeRespVO> getChildren() {
        return children;
    }

    public void setChildren(List<SysOrgTreeNodeRespVO> children) {
        this.children = children;
    }
}
