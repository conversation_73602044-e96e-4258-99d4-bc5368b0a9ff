package com.r2.bi.vo.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.r2.bi.vo.sysdept.SysDeptTreeNodeRespVO;
import com.r2.bi.vo.sysorg.SysOrgTreeNodeRespVO;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Treeselect树结构实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/1 下午3:31
 */
public class TreeSelectVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String label;

    /** 字符类型节点ID */
    private String key;

    /** 节点标题 */
    private String title;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelectVO> children;

    public TreeSelectVO() {

    }

    /**
     * 部门树结构
     * @param dept
     */
    public TreeSelectVO(SysDeptTreeNodeRespVO dept) {
        this.id = dept.getId();
        this.label = dept.getName();
        this.children = dept.getChildren().stream().map(TreeSelectVO::new).collect(Collectors.toList());
    }

    /**
     * 组织树结构
     * @param org
     */
    public TreeSelectVO(SysOrgTreeNodeRespVO org) {
        this.key = org.getOrganizeCode();
        this.title = org.getOrganizeName();
        // 关联用户id
        this.id = org.getPersonInCharge();
        this.children = org.getChildren().stream().map(TreeSelectVO::new).collect(Collectors.toList());
    }

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label) { this.label = label; }

    public List<TreeSelectVO> getChildren()
    {
        return children;
    }

    public void setChildren(List<TreeSelectVO> children)
    {
        this.children = children;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

}
