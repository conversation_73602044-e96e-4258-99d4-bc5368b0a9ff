package com.r2.bi.vo.sysorg;

import lombok.Data;

@Data
public class SyncOranizeInfoRespVO {

//    private String organizecode;
//    private Long oid;
//    private String organizename;
//    private String parentcode;
//    private String secondlevelorgcode;
//    private Integer status;
//    private String personincharge;
//    @SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
//    private String update_day;
    private String code;
    private Long oId;
    private String name;
    private String parentcode;
    private Long pOIdOrgAdmin;
    private String secondlevelorgcode;
    private String firstLevelOrganization;
    private String secondLevelOrganization;
    private Integer status;
    private String personincharge;
    private String modifiedTime;
}
