package com.r2.bi.vo.sysUser;

import com.r2.bi.vo.BaseListReqVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class SysUserListReqVO extends BaseListReqVO {

    /**
     * 关键字  姓名 手机号码 邮箱 工号
     */
    private String search;
    /**
     * 部门
     */
    private List<String> organizeCode;
    /**
     * 角色
     */
    private Long roleId;
    /**
     * 状态: 1:启用；2:停用
     */
    private Integer status;
    /**
     * 职位
     */
    private String dutyName;
    /**
     * poi管理员姓名
     */
    private String poiDempAdminName;

    /**
     * 用户id
     */
    private Long id;

    /**
     * 用户类型
     */
    private Integer sourceType;
}
