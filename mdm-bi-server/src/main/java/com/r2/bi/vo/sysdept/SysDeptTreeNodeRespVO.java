package com.r2.bi.vo.sysdept;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@NoArgsConstructor
@Data
public class SysDeptTreeNodeRespVO {

    private Long id;
    //上级组织
    private Long pid;
    //子组织数目
    private Integer subCount;
    //名称
    private String name;
    //排序
    private Integer deptSort;
    //状态 1启用 2停用
    private Integer status;
    //创建者
    private String createBy;
    //更新者
    private String updateBy;
    //创建日期
    private Date createTime;
    //更新时间
    private Date updateTime;

    /**
     * 子菜单
     */
    public List<SysDeptTreeNodeRespVO> children = new ArrayList<SysDeptTreeNodeRespVO>();

    public List<SysDeptTreeNodeRespVO> getChildren() {
        return children;
    }

    public void setChildren(List<SysDeptTreeNodeRespVO> children) {
        this.children = children;
    }
}
