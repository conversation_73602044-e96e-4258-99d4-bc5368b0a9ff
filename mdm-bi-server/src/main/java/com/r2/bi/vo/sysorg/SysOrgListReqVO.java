package com.r2.bi.vo.sysorg;


import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/1 下午5:33
 */
@SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
@Data
public class SysOrgListReqVO {

    /**
     * 上级组织
     */
    private String parent_code;

    /**
     * 组织名称
     */
    private String organize_name;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 用户id
     */
    private Long person_in_charge;


    /**
     * 操作类型 新增，删除，修改
     */
    private String operateType;

    /**
     * 操作状态：1：成功；2：失败
     */
    private Integer operateStatus;

    /**
     * 响应结果
     */
    private String exceptionDetail;

    /**
     * 操作开始时间
     */
    private LocalDateTime startTime;

    /**
     * 操作结束时间
     */
    private LocalDateTime endTime;

    /**
     * 组织编码
     */
    private List<String> organizeCodes;
}
