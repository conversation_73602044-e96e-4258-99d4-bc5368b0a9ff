package com.r2.bi.vo.login;

import lombok.Data;

import java.util.Date;

@Data
public class AuthLoginRespVO {

    /**
     * 是否登入
     */
    private Boolean isLogin;

    /**
     * token
     */
    private String token;

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 提示
     */
    private String message;

    /**
     * 用户来源
     */
    private Integer sourceType;

    /**
     * 权限码
     */
    private String authorityCode;


    /**
     * 过期时间
     */
    private Integer sessionTimeOut;

    /**
     * 当前时间
     */
    private String currentTime;


}
