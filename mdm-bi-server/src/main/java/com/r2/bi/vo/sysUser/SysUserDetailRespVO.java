package com.r2.bi.vo.sysUser;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class SysUserDetailRespVO {

    /**
     * id
     */
    private Long id;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 用户名称
     */
    private String username;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 工号
     */
    private String jobNumber;
    /**
     * 职位
     */
    private String dutyName;
    /**
     * 角色
     */
    private List<Long> roleIds;
    /**
     * 直属领导
     */
    private String leaderName;
    /**
     * 部门
     */
    private Long deptId;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 数据来源类型
     */
    private Integer sourceType;
    /**
     * 性别
     */
    private Integer sex;
    /**
     * 部门
     */
    private String organizeCode;
}
