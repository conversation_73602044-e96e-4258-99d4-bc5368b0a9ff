package com.r2.bi.vo.centermeeting;

import lombok.Data;

@Data
public class OverviewVO {
    private String meetingType;
    private int count;

    public OverviewVO(String meetingType, int count) {
        this.meetingType = meetingType;
        this.count = count;
    }

    public String getMeetingType() {
        return meetingType;
    }

    public void setMeetingType(String meetingType) {
        this.meetingType = meetingType;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
