package com.r2.bi.vo.sysaccesssystem;

import com.r2.bi.vo.BaseListReqVO;
import lombok.Data;

import java.util.List;

@Data
public class SystemUserReqVO extends BaseListReqVO {

    /**
     * 系统id
     */
    private Integer systemId;

    /**
     * 用户id
     */
    private List<Long> ids;

    /**
     * 工号/姓名/邮箱
     */
    private String search;

    /**
     * 性质：自动同步/手动修改
     */
    private String character;

    /**
     * 职位
     */
    private String dutyName;

    /**
     * 部门
     */
    private String dept;

    /**
     * 直属领导
     */
    private String poiDempAdmin;

    /**
     * 在职状态
     */
    private String employeeStatus;

    /**
     * 在职状态数值
     */
    private Integer employeeStatusEnum;

    /**
     * 用户来源
     */
    private String sourceType;

    /**
     * 用户来源数值
     */
    private Integer sourceTypeEnum;

    /**
     * 删除
     */
    private Integer isDelete;

    /**
     * 权限状态 正常/已停用
     */
    private String authStatus;
}
