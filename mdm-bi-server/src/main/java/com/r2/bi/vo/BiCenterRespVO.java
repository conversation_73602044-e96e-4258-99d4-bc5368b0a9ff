package com.r2.bi.vo;


import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BiCenterRespVO {

    /**
    * 中心ID
    * */
    private Long id;
    /**
    * 中心名称
    * */
    private String centerName;

    /**
     * 中心别名，逗号隔开，动态添加，最多10个
     * */
    private String alias;


    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;


    /**
     * 地址
     */
    private String address;
    /**
     * 项目
     */
    private Integer item;

    /**
     * 风险
     */
    private String risk;

    /**
     * 启动速度（天）
     */
    private Double speed;


    /**
     * 是否先支付首付款再启动
     */
    private String centerRqmt;


    /**
     * 是否伦理前置
     */
    private String ethicalPre;


}
