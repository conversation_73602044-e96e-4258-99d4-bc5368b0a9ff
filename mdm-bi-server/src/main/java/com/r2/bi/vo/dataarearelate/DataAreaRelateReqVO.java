package com.r2.bi.vo.dataarearelate;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DataAreaRelateReqVO {

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 中心名称
     */
    private String centerName;

    /**
     * SMO更新人
     */
    private String updateBy;


    /**
     * 代表使用哪个字段排序
     */
    private String field;


    /**
     * 代表升序或降序——ascend 升序，descend 降序
     */
    private String order;


    /**
     * 页
     */
    private int page = 1;

    /**
     * 页大小
     */
    private int size = 10;

}
