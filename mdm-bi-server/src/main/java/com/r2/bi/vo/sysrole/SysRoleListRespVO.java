package com.r2.bi.vo.sysrole;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SysRoleListRespVO {

    /**
     * id
     */
    private Integer id;

    /**
     * 角色编码
     */
    private String code;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 角色说明
     */
    private String description;

    /**
     * 权限列表
     */
    private List<Long> authList;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 角色状态 1启用 2停用
     */
    private Integer status;

    /**
     * 是否脱敏
     */
    private String dataScope;
}
