package com.r2.bi.vo.login;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/3/15 19:33
 */
@Data
public class AuthUpdatePasswordReqVO {

    /**
     * 工号
     */
    @NotBlank(message = "工号不能为空")
    private String jobNumber;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    private String code;

    /**
     * 新密码
     */
    @NotBlank(message = "旧密码不能为空")
    private String newPassword;

    /**
     * 确认密码
     */
    @NotBlank(message = "新密码不能为空")
    private String confirmPassword;
}
