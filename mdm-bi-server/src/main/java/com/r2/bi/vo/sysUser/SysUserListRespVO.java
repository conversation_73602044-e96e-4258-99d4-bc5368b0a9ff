package com.r2.bi.vo.sysUser;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class SysUserListRespVO {


    /**
     * 用户id
     */
    private Long id;
    /**
     * 用户工号
     */
    private String jobNumber;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 用户名称
     */
    private String username;
    /**
     * 角色
     */
    private String role;

    /**
     * 角色
     */
    private List<Long> roleIds;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 职务
     */
    private String dutyName;
    /**
     * 直属领导
     */
    private String leaderName;
    /**
     * 部门
     */
    private String dept;

    /**
     * 数据权限0-无数据权限 1-有数据权限
     */
    private Integer dataPermission;


    /**
     * 来源类型
     */
    private Integer sourceType;
}
