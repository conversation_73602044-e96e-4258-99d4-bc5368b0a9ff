package com.r2.bi.vo.sysmenu;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class SysMenuUpdateReqVO {
    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Integer id;

    /**
     * 上级类目id
     */
    private Integer pid;

    /**
     * 菜单标题
     */
    @NotBlank(message = "菜单标题不能为空")
    private String title;

    /**
     * 组件名称
     */
    private String name;

    /**
     * 菜单类型：1：目录；2：菜单；3：按钮
     */
    @NotNull(message = "菜单类型不能为空")
    private Integer type;

    /**
     * 排序
     */
    private Integer menuSort;

    /**
     * 图标
     */
    private String icon;

    /**
     * 路由地址
     */
    private String route;

    /**
     * 状态[菜单是否可见] 1启用[可见] 2停用[不可见]
     */
    private Integer status;
}
