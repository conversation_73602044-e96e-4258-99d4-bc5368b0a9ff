package com.r2.bi.vo.sysrole;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class SysRoleCreateReqVO {

    /**
     * 角色编码
     */
    @NotBlank(message = "角色编码不能为空")
    @Size(max = 10, message = "角色编码长度不能超过10位字符")
    @Pattern(regexp = "^[a-zA-Z0-9]*$", message = "角色编码不满足规则")
    private String code;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    private String name;

    /**
     * 角色说明
     */
    private String description;

    /**
     * 是否脱敏
     */
    private String dataScope;

    /**
     * 角色权限
     */
    private List<Integer> authList;
}
