package com.r2.bi.vo.login;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AuthLoginReqVO {

    @NotBlank(message = "用户名不能为空")
    private String userAccount;

//    @NotBlank(message = "缺少参数")
    private String imgId;

    @NotBlank(message = "密码不能为空")
    private String password;

//    @NotBlank(message = "验证码不能为空")
    private  String imgCode;

    /**
     * 登录类型：1：北森[内部用户]；2：R2
     */
    private Integer type;

}
