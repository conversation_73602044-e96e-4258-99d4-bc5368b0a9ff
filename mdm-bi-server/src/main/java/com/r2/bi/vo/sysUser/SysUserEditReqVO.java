package com.r2.bi.vo.sysUser;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@NoArgsConstructor
@Data
public class SysUserEditReqVO {

    /**
     * id
     */
    private Long id;
    /**
     * 用户账号
     */
    @NotBlank(message = "用户账号不能为空")
    private String userAccount;
    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;
    /**
     * 用户名
     */
    @NotBlank(message = "姓名不能为空")
    private String username;
    /**
     * 手机号
     */
    @NotBlank(message = "手机号码不能为空")
    private String mobile;
    /**
     * 工号
     */
    @NotBlank(message = "工号不能为空")
    private String jobNumber;

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    /**
     * 职位
     */
    private String dutyName;
    /**
     * 角色
     */
    private List<Long> roleIds;
    /**
     * 部门
     */
    private String organizeCode;
    /**
     * 来源类型
     */
    @NotEmpty(message = "参数不能为空")
    private Integer sourceType;
}
