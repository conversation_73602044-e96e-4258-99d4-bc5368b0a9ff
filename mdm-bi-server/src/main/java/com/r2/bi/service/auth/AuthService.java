package com.r2.bi.service.auth;


import com.r2.bi.vo.login.AuthLoginReqVO;
import com.r2.bi.vo.login.AuthLoginRespVO;
import com.r2.bi.vo.login.AuthUpdatePasswordReqVO;
import com.r2.bi.vo.login.AuthUpdatePasswordRespVO;


public interface AuthService {


    /**
     * 三方登录
     * @param key
     * @return
     */
    AuthLoginRespVO thirdLoginBi(String key);

    /**
     * 登入
     * @param vo
     * @return
     */
    AuthLoginRespVO login(AuthLoginReqVO vo);

    /**
     * 登出
     * @return
     */
    Object logout();

    /**
     * 修改密码
     * @param authUpdatePasswordReqVO
     * @return
     */
    AuthUpdatePasswordRespVO updatePassword(AuthUpdatePasswordReqVO authUpdatePasswordReqVO);


}
