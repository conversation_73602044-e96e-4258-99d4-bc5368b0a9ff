package com.r2.bi.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.r2.bi.entity.cs.CenterMeeting;
import com.r2.bi.vo.centermeeting.CalendarListReqVO;
import com.r2.bi.vo.centermeeting.CalendarListRespVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 日历(CenterMeeting)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-10 17:58:24
 */
public interface CenterMeetingService extends IService<CenterMeeting> {



    /**
     * 根据时间段返回日历列表
     *
     * @param listReqVO
     * @return
     */
    List<CalendarListRespVO> listCalendarByScope(CalendarListReqVO listReqVO);




    /**
     * 导出日历列表
     * @param response
     */
    void export(CalendarListReqVO listReqVO,HttpServletResponse response);
}
