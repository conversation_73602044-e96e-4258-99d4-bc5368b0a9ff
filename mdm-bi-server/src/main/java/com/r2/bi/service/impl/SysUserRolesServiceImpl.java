package com.r2.bi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.r2.bi.dao.bi.SysUserRolesDao;
import com.r2.bi.entity.bi.SysUserRoles;
import com.r2.bi.service.SysUserRolesService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (SysUserRoles)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-31 20:22:14
 */
@Service("sysUserRolesService")
public class SysUserRolesServiceImpl extends ServiceImpl<SysUserRolesDao, SysUserRoles> implements SysUserRolesService {

    @Resource
    private SysUserRolesDao sysUserRolesDao;

    @Override
    public List<Long> getByUserIds(Long userId) {
        QueryWrapper<SysUserRoles> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysUserRoles ::getUserId, userId);
        List<SysUserRoles> sysUserRoles = sysUserRolesDao.selectList(queryWrapper);
        return sysUserRoles.stream()
                .map(SysUserRoles ::getRoleId)
                .collect(Collectors.toList());
    }

    @Override
    public Object deleteByUserIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return 0;
        }
        QueryWrapper<SysUserRoles> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SysUserRoles::getUserId, userIds);
        return sysUserRolesDao.delete(queryWrapper);
    }

    @Override
    public Object batchInsert(List<SysUserRoles> userRoles) {
        return saveBatch(userRoles, userRoles.size());
    }
}

