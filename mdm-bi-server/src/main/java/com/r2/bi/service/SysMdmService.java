package com.r2.bi.service;

import com.alibaba.fastjson.JSONObject;
import com.r2.bi.entity.bi.BiUser;
import com.r2.bi.vo.login.AuthUpdatePasswordReqVO;


import java.util.List;

/**
 * 主数据平台api服务
 */
public interface SysMdmService {

    /**
     * 获取系统id
     */
    Integer getSystemId();

    /**
     * 用户登录
     * @return
     */
    JSONObject login(String jobNumber, String password);

    /**
     * 用户同步
     * @return
     */
    List<BiUser> syncUser(Integer systemId, Integer page);



    /**
     * 修改密码
     * @param authUpdatePasswordReqVO
     * @return
     */
    JSONObject updatePassword(AuthUpdatePasswordReqVO authUpdatePasswordReqVO);


}
