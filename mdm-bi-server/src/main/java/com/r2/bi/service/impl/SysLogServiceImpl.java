package com.r2.bi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.r2.bi.dao.bi.SysLogDao;
import com.r2.bi.entity.bi.SysLog;
import com.r2.bi.service.SysLogService;
import com.r2.bi.vo.syslog.SysLogListReqVO;
import com.r2.bi.vo.syslog.SysLogListRespVO;
import com.r2.framework.exception.CodeException;
import com.r2.framework.util.ResultCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 系统日志(SysLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-21 10:04:10
 */
@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogDao, SysLog> implements SysLogService {

    @Resource
    private SysLogDao sysLogDao;

    @Override
    public Page<SysLogListRespVO> queryByPage(SysLogListReqVO reqVO) {
        Page<SysLogListRespVO> page = new Page<>();
        //查询条件
        String querySql = " where 1=1 ";
        //模块名称
        if (StrUtil.isNotEmpty(reqVO.getModuleName())) {
            querySql += " and sl.module_name like '%" + reqVO.getModuleName() + "%' ";
        }
        //操作人员
        if (StrUtil.isNotEmpty(reqVO.getUsername())) {
            querySql += " and sl.username = '" + reqVO.getUsername() + "' ";
        }
        //操作类型
        if (StrUtil.isNotEmpty(reqVO.getOperateType())) {
            querySql += " and sl.operate_type = '" + reqVO.getOperateType() + "' ";
        }
        //操作状态
        if (null != reqVO.getOperateStatus()) {
            querySql += " and sl.operate_status = " + reqVO.getOperateStatus() + " ";
        }
        //操作开始时间
        if (null != reqVO.getStartTime()) {
            querySql += " and sl.create_time >= '" + reqVO.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 00:00:00' ";
        }
        //操作结束时间
        if (null != reqVO.getEndTime()) {
            querySql += " and sl.create_time <= '" + reqVO.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 23:59:59' ";
        }

        //分页
        String limitSql = " limit " + reqVO.getOffset() + "," + reqVO.getSize() + " ";
        //列表数量
        Integer totalCount = sysLogDao.totalCount(querySql);
        page.setTotal(totalCount);
        page.setCurrent(reqVO.getPage());
        page.setSize(reqVO.getSize());
        //列表数据
        List<SysLogListRespVO> dataFieldList = sysLogDao.listByCond(querySql, limitSql);
        page.setRecords(dataFieldList);

        return page;
    }

    @Override
    public SysLogListRespVO queryById(Integer id) {
        SysLogListRespVO sysLogListRespVO = new SysLogListRespVO();
        SysLog sysLog = sysLogDao.selectById(id);
        if (null == id || ObjectUtil.isEmpty(sysLog)) {
            throw new CodeException(ResultCode.Codes.ERROR, "数据不存在");
        }
        sysLogListRespVO.setId(sysLog.getId());
        sysLogListRespVO.setModuleName(sysLog.getModuleName());
        sysLogListRespVO.setUsername(sysLog.getUsername());
        sysLogListRespVO.setMethod(sysLog.getMethod());
        // 处理 params 字段
        String params = sysLog.getParams();
        if (StringUtils.isNotBlank(params)) {
            JSONObject paramsJson = JSON.parseObject(params);
            if (paramsJson.containsKey("password")) {
                paramsJson.remove("password");
            }
            sysLogListRespVO.setParams(JSON.toJSONString(paramsJson));
        } else {
            sysLogListRespVO.setParams(null);
        }
        sysLogListRespVO.setExceptionDetail(sysLog.getExceptionDetail());
        sysLogListRespVO.setOperateStatus(sysLog.getOperateStatus());
        sysLogListRespVO.setOperateType(sysLog.getOperateType());
        sysLogListRespVO.setRequestIp(sysLog.getRequestIp());
        sysLogListRespVO.setCreateTime(sysLog.getCreateTime());
        return sysLogListRespVO;
    }
}

