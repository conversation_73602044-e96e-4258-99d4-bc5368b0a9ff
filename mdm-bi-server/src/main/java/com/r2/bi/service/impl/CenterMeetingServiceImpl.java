package com.r2.bi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.r2.bi.dao.cs.BiCenterDao;
import com.r2.bi.dao.cs.CenterMeetingDao;
import com.r2.bi.dao.cs.InitSmoDataAreaRelateMapper;
import com.r2.bi.entity.cs.Center;
import com.r2.bi.entity.cs.CenterMeeting;
import com.r2.bi.entity.cs.InitSmoDataAreaRelate;
import com.r2.bi.service.CenterMeetingService;
import com.r2.bi.util.StringUtils;
import com.r2.bi.vo.centermeeting.*;
import com.r2.framework.exception.CodeException;
import com.r2.framework.util.ResultCode;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日历(CenterMeeting)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-10 17:58:24
 */
@Service("centerMeetingService")
public class CenterMeetingServiceImpl extends ServiceImpl<CenterMeetingDao, CenterMeeting> implements CenterMeetingService {
    @Resource
    private CenterMeetingDao centerMeetingDao;

    @Resource
    private BiCenterDao biCenterDao;

    @Resource
    private InitSmoDataAreaRelateMapper initSmoDataAreaRelateMapper;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    /**
     * 根据时间段返回日历列表
     *
     * @param listReqVO
     * @return
     */
    @Override
    public List<CalendarListRespVO> listCalendarByScope(CalendarListReqVO listReqVO) {
        String startDate = listReqVO.getStartDate();
        if (startDate.length() == 7) {
            startDate = findFirstDayOfMonth(startDate);
        }

        // 查询未删除的中心
        QueryWrapper<Center> centerQueryWrapper = new QueryWrapper<>();
        centerQueryWrapper.eq("is_delete", 0);

        if (StringUtils.isNotBlank(listReqVO.getProvince())) {
            centerQueryWrapper.eq("province", listReqVO.getProvince());
        }
        if (StringUtils.isNotBlank(listReqVO.getCity())) {
            centerQueryWrapper.eq("city", listReqVO.getCity());
        }

        List<Center> centers = biCenterDao.selectList(centerQueryWrapper);

        if (listReqVO.getAreaName() != null) {
            // 查询 InitSmoDataAreaRelate 数据
            QueryWrapper<InitSmoDataAreaRelate> initSmoDataAreaRelateQueryWrapper = new QueryWrapper<>();
            List<InitSmoDataAreaRelate> initSmoDataAreaRelates = initSmoDataAreaRelateMapper.selectList(initSmoDataAreaRelateQueryWrapper);

            List<String> provinces = initSmoDataAreaRelates.stream()
                    .filter(initSmoDataAreaRelate -> initSmoDataAreaRelate.getAreaName().equals(listReqVO.getAreaName()))
                    .map(InitSmoDataAreaRelate::getProvince)
                    .distinct()
                    .collect(Collectors.toList());

            centers = centers.stream()
                    .filter(center -> provinces.contains(center.getProvince()))
                    .collect(Collectors.toList());
        }
        List<Integer> tCenterIdList = centers.stream().map(Center::getId).collect(Collectors.toList());
        List<CalendarListRespVO> calendarList = new ArrayList<>();
        try {
            // 查询指定年月内的会议
            List<CenterMeetingQueryStrRespVO> meetings = centerMeetingDao.queryByDateScope(startDate);

            // 过滤掉不在 t_center 下的会议
            meetings = meetings.stream()
                    .filter(meeting -> tCenterIdList.contains(meeting.getCenterId().intValue()))
                    .collect(Collectors.toList());

            // 获取年月
            String yearMonth = listReqVO.getStartDate();
            String firstDayOfMonth = findFirstDayOfMonth(yearMonth);
            String lastDayOfMonth = findLastDayOfMonth(yearMonth);

            // 计算每个会议的具体时间(放在cycle里)
            increaseCycleToMeetings(meetings, firstDayOfMonth, lastDayOfMonth);

            // 遍历当前月所有天数
            List<String> everyDay = findEveryDay(firstDayOfMonth, lastDayOfMonth);

            for (String day : everyDay) {
                // 创建每天所有会议返回实例
                CalendarListRespVO listCalendarResp = new CalendarListRespVO();
                Map<String, List<CalendarDetailRespVO>> calendarDetailMap = new HashMap<>();
                Map<String, Set<CalendarDetailRespVO>> calendarDetailSetMap = new HashMap<>();
                Map<String, Integer> meetingTypeCount = new HashMap<>();

                // 初始化上午和下午的列表
                calendarDetailMap.put("morning", new ArrayList<>());
                calendarDetailMap.put("afternoon", new ArrayList<>());
                calendarDetailSetMap.put("morning", new HashSet<>());
                calendarDetailSetMap.put("afternoon", new HashSet<>());

                // 遍历会议,根据条件新增至返回实例(去重)
                for (CenterMeetingQueryStrRespVO meeting : meetings) {
                    if (meeting.getCycle() == null || meeting.getPeriod() == null || !meeting.getCycle().contains(day)) {
                        continue;
                    }

                    // 获取当前时间段的会议列表，如果为空则初始化一个新的列表
                    String period = replacePeriod(meeting.getPeriod());
                    List<CalendarDetailRespVO> detailList = calendarDetailMap.get(period);
                    Set<CalendarDetailRespVO> detailSet = calendarDetailSetMap.get(period);

                    if (detailList == null) {
                        detailList = new ArrayList<>();
                        detailSet = new HashSet<>();
                        calendarDetailMap.put(period, detailList);
                        calendarDetailSetMap.put(period, detailSet);
                    }

                    // 检查该时间段的会议列表中是否已存在相同的会议
                    CalendarDetailRespVO calendarDetailRespVO = new CalendarDetailRespVO();
                    calendarDetailRespVO.setCenterId(meeting.getCenterId());
                    calendarDetailRespVO.setCenterName(meeting.getCenterName());
                    calendarDetailRespVO.setMeetingId(meeting.getId());
                    calendarDetailRespVO.setMeetingType(meeting.getType());

                    if (!detailSet.stream().anyMatch(detail -> detail.getCenterId().equals(calendarDetailRespVO.getCenterId()))) {
                        detailSet.add(calendarDetailRespVO);
                        detailList.add(calendarDetailRespVO);

                        // 计数放入 map (根据会议类型)
                        String meetingTypeKey = replaceMeetingType(meeting.getType());
                        meetingTypeCount.merge(meetingTypeKey, 1, Integer::sum);
                    }
                }

                if (calendarDetailMap.get("morning").isEmpty() && calendarDetailMap.get("afternoon").isEmpty()) {
                    continue;
                }

                listCalendarResp.setDate(day);
                listCalendarResp.setCalendarList(calendarDetailMap);
                listCalendarResp.setOverview(meetingTypeCount.entrySet().stream()
                        .map(entry -> new OverviewVO(entry.getKey(), entry.getValue()))
                        .collect(Collectors.toList()));

                // 添加到结果列表
                calendarList.add(listCalendarResp);
            }
        } catch (Exception e) {
            log.error("日历查询异常", e);
            throw new CodeException(ResultCode.Codes.ERROR, "日历查询异常!");
        }
        return calendarList;
    }


    // 辅助方法：查找某个月的最后一天
    private String findLastDayOfMonth(String yearMonth) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(sdf.parse(yearMonth));
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            return sdf.format(cal.getTime()) + "-" + cal.get(Calendar.DAY_OF_MONTH);
        } catch (ParseException e) {
            log.error("日期解析异常", e);
            throw new CodeException(ResultCode.Codes.ERROR, "日期解析异常!");
        }
    }

    private String findFirstDayOfMonth(String yearMonth) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(sdf.parse(yearMonth));
            cal.set(Calendar.DAY_OF_MONTH, 1);
            return sdf.format(cal.getTime()) + "-01";
        } catch (ParseException e) {
            log.error("日期解析异常", e);
            throw new CodeException(ResultCode.Codes.ERROR, "日期解析异常!");
        }
    }

    // 辅助方法：找出指定日期范围内的每一天
    private List<String> findEveryDay(String startDate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<String> days = new ArrayList<>();
        try {
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);
            Calendar cal = Calendar.getInstance();
            cal.setTime(start);
            while (!cal.getTime().after(end)) {
                days.add(sdf.format(cal.getTime()));
                cal.add(Calendar.DATE, 1);
            }
        } catch (ParseException e) {
            log.error("日期解析异常", e);
            throw new CodeException(ResultCode.Codes.ERROR, "日期解析异常!");
        }
        return days;
    }

    // 替换时间段
    private String replacePeriod(String period) {
        if ("上午".equals(period)) {
            return "morning";
        } else if ("下午".equals(period)) {
            return "afternoon";
        }
        return period;
    }

    // 替换会议类型
    private String replaceMeetingType(String meetingType) {
        if ("立项会".equals(meetingType)) {
            return "Approval";
        } else if ("伦理会".equals(meetingType)) {
            return "Ethics";
        }
        return meetingType;
    }

    /**
     * 会议中添加具体开会日期(cycle)
     *
     * @param meetings         会议
     * @param formatDatePrefix 格式化日期前缀
     * @param endDate          截止日期
     */
    private void increaseCycleToMeetings(List<CenterMeetingQueryStrRespVO> meetings, String formatDatePrefix, String endDate) {
        for (CenterMeetingQueryStrRespVO meeting : meetings) {
            String finalDate = endDate;
            try {
                if (StringUtils.isNotEmpty(meeting.getEndDate()) && !"Invalid date".equals(meeting.getEndDate())) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date dEnd = sdf.parse(endDate);
                    Date meetingEnd = sdf.parse(meeting.getEndDate());
                    finalDate = dEnd.after(meetingEnd) ? meeting.getEndDate() : endDate;
                }
            } catch (Exception e) {
                log.error("会议截止日期格式错误", e);
                continue;
            }
            // 指定日期
            if ("指定日期".equals(meeting.getRepeatType())) {
                String cycle = splitExpireDateStr(meeting.getRepeatDetails(), finalDate);
                meeting.setCycle(cycle);
            } else if (StringUtils.isEmpty(meeting.getRepeatWeek())) {
                // 指定周期(按月)
                insertCycleByMonth(meeting, formatDatePrefix, finalDate);
            } else {
                // 指定周期(按周)
                insertCycleByWeek(meeting, formatDatePrefix, finalDate);
            }
        }
    }

    /**
     * 生成固定周期内的所有会议(按月)
     *
     * @param meeting          会议
     * @param formatDatePrefix 格式化日期前缀
     * @param endDate          截止日期
     */
    private void insertCycleByMonth(CenterMeetingQueryStrRespVO meeting, String formatDatePrefix, String endDate) {
        if (StringUtils.isEmpty(meeting.getRepeatDetails())) {
            return;
        }
        // 提取 formatDatePrefix 中的年月部分
        String yearMonth = formatDatePrefix.substring(0, 8); // 提取 "yyyy-MM-"
        // 分割重复详情
        String details = meeting.getRepeatDetails().replace("号", "");
        String[] days = details.split(",");
        StringBuilder cycleBuilder = new StringBuilder();
        // 拼接插入cycle
        for (String dayStr : days) {
            int day = Integer.parseInt(dayStr.trim());
            // 格式化日期，确保 day 小于 10 时前面补零
            String formattedDay = (day < 10) ? "0" + day : String.valueOf(day);
            String fullDate = yearMonth + formattedDay;
            // 添加到 cycleBuilder
            if (cycleBuilder.length() > 0) {
                cycleBuilder.append(",");
            }
            cycleBuilder.append(fullDate);
        }
        // 设置 meeting 的 cycle 属性
        meeting.setCycle(splitExpireDateStr(cycleBuilder.toString(), endDate));
    }

    /**
     * 生成固定周期内的所有会议(按周)
     *
     * @param meeting          会议
     * @param formatDatePrefix 格式化日期前缀
     * @param endDate          截止日期
     */
    private void insertCycleByWeek(CenterMeetingQueryStrRespVO meeting, String formatDatePrefix, String endDate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String[] parts = formatDatePrefix.split("-");
            Integer year = Integer.parseInt(parts[0]);
            Integer month = Integer.parseInt(parts[1]);

            // 获取月份的第一天和最后一天
            Calendar cal = Calendar.getInstance();
            cal.set(year, month - 1, 1); // 设置为本月第一天
            int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);

            // 获取月份的第一天是周几（1=周一, 2=周二, ..., 7=周日）
            int startDayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
            int adjustedStartDayOfWeek = (startDayOfWeek == Calendar.SUNDAY) ? 7 : startDayOfWeek - 1;

            String cycle = "";
            for (String week : meeting.getRepeatWeek().split(",")) {
                Integer count = formatWeek(week); // 输入“第二周”应返回1
                for (String weekday : meeting.getRepeatDetails().split(",")) { // 输入“周二”应返回2
                    Integer dayCount = formatWeekDay(weekday);
                    if (dayCount == null) {
                        continue;
                    }

                    // 计算符合条件的日期
                    if (count == 0) { // 第一周
                        // 第一周从月份的第一天开始，持续到该周的周日
                        int firstWeekEndDay = Math.min(lastDay, 7 - adjustedStartDayOfWeek + 1); // 第一周的最后一天
                        if (dayCount >= adjustedStartDayOfWeek && dayCount <= 7) { // 确保当前星期在第一周内
                            int day = dayCount - adjustedStartDayOfWeek + 1; // 计算具体的日期
                            if (day > 0 && day <= firstWeekEndDay) {
                                // 构建日期字符串
                                String currentStrDate = parts[0] + "-" + parts[1] + "-" + (day > 9 ? day : "0" + day);
                                Date currentDate = sdf.parse(currentStrDate);

                                // 只插入最后一天前的日期数据
                                if (currentDate.before(sdf.parse(endDate))) {
                                    cycle += currentStrDate + ",";
                                }
                            }
                        }
                    } else { // 非第一周
                        // 计算第一周的第一个周一的偏移量
                        int offsetFromFirstMonday = (adjustedStartDayOfWeek - 1) % 7;
                        int dayOffset = count * 7 + dayCount - offsetFromFirstMonday;
                        int day = dayOffset; // 计算具体的日期

                        // 如果计算的日期超出了月份的最后一天，则跳过
                        if (day > lastDay || day < 1) { // 确保日期有效
                            continue;
                        }

                        // 构建日期字符串
                        String currentStrDate = parts[0] + "-" + parts[1] + "-" + (day > 9 ? day : "0" + day);
                        Date currentDate = sdf.parse(currentStrDate);

                        // 只插入最后一天前的日期数据
                        if (currentDate.before(sdf.parse(endDate))) {
                            cycle += currentStrDate + ",";
                        }
                    }
                }
            }

            // 移除最后一个逗号
            if (cycle.endsWith(",")) {
                cycle = cycle.substring(0, cycle.length() - 1);
            }

            meeting.setCycle(splitExpireDateStr(cycle, endDate));
        } catch (ParseException e) {
            log.error("固定周期日历生成异常", e);
            throw new CodeException(ResultCode.Codes.ERROR, "固定周期日历生成异常!");
        }
    }

    /**
     * 过滤日期范围外的日历数据
     *
     * @param dateStr
     * @param expire
     * @return
     */
    private String splitExpireDateStr(String dateStr, String expire) {
        try {
            if (StringUtils.isEmpty(dateStr)) {
                return "";
            }
            String finalDateStr = "";
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date dEnd = sdf.parse(expire);
            for (String date : dateStr.split(",")) {
                Date dFinal = sdf.parse(date);
                if (dFinal.after(dEnd)) {
                    continue;
                }
                finalDateStr += date + ",";
            }
            return StringUtils.isEmpty(finalDateStr) ? "" : finalDateStr.substring(0, finalDateStr.length() - 1);
        } catch (Exception e) {
            log.error("ee", e);
            throw new CodeException(ResultCode.Codes.ERROR, "数据异常!");
        }
    }


    private Integer formatWeek(String week) {
        return "第一周".equals(week) ? 0 : "第二周".equals(week) ? 1 : "第三周".equals(week) ? 2 :"第四周".equals(week)? 3:4;
    }

    private Integer formatWeekDay(String weekDay) {
        Map day = new HashMap();
        day.put("周一", 1);
        day.put("周二", 2);
        day.put("周三", 3);
        day.put("周四", 4);
        day.put("周五", 5);
        day.put("周六", 6);
        day.put("周日", 7);
        return (Integer) day.get(weekDay);
    }


    @Override
    public void export(CalendarListReqVO listReqVO, HttpServletResponse response) {
        String startDate = listReqVO.getStartDate();
        if (startDate.length() == 7) {
            startDate = findFirstDayOfMonth(startDate);
        }
        // 查询未删除的中心
        QueryWrapper<Center> centerQueryWrapper = new QueryWrapper<>();
        centerQueryWrapper.eq("is_delete", 0);
        if (StringUtils.isNotBlank(listReqVO.getProvince())) {
            centerQueryWrapper.eq("province", listReqVO.getProvince());
        }
        if (StringUtils.isNotBlank(listReqVO.getCity())) {
            centerQueryWrapper.eq("city", listReqVO.getCity());
        }
        List<Center> centers = biCenterDao.selectList(centerQueryWrapper);

        // 查询 InitSmoDataAreaRelate 数据
        QueryWrapper<InitSmoDataAreaRelate> initSmoDataAreaRelateQueryWrapper = new QueryWrapper<>();
        List<InitSmoDataAreaRelate> initSmoDataAreaRelates = initSmoDataAreaRelateMapper.selectList(initSmoDataAreaRelateQueryWrapper);

        if (listReqVO.getAreaName() != null) {
            List<String> provinces = initSmoDataAreaRelates.stream()
                    .filter(initSmoDataAreaRelate -> initSmoDataAreaRelate.getAreaName().equals(listReqVO.getAreaName()))
                    .map(InitSmoDataAreaRelate::getProvince)
                    .distinct()
                    .collect(Collectors.toList());

            centers = centers.stream()
                    .filter(center -> provinces.contains(center.getProvince()))
                    .sorted(Comparator.comparing(Center::getCenterName))
                    .collect(Collectors.toList());
        }

        List<Integer> tCenterIdList = centers.stream().map(Center::getId).collect(Collectors.toList());

        // 查询指定年月内的会议
        List<CenterMeetingQueryStrRespVO> meetings = centerMeetingDao.queryByDateScope(startDate);
        // 过滤掉不在 t_center 下的会议
        meetings = meetings.stream()
                .filter(meeting -> tCenterIdList.contains(meeting.getCenterId().intValue()))
                .collect(Collectors.toList());

        String yearMonth = listReqVO.getStartDate();
        String firstDayOfMonth = findFirstDayOfMonth(yearMonth);
        String lastDayOfMonth = findLastDayOfMonth(yearMonth);

        // 更新会议的周期信息
        increaseCycleToMeetings(meetings, firstDayOfMonth, lastDayOfMonth);

        // 构建数据结构
        List<CalendarExportVO> calendarExportList = new ArrayList<>();
        for (Center center : centers) {
            // 为每个中心创建一个Map来存储每个周期（如上午、下午）结合时间段的唯一键集合
            Map<String, Set<String>> cyclePeriodProcessedMeetings = new HashMap<>();
            for (CenterMeetingQueryStrRespVO meeting : meetings) {
                if (center.getId().equals(meeting.getCenterId().intValue())) {
                    if (StringUtils.isEmpty(meeting.getCycle())) {
                        continue;
                    }
                    String[] cycles = StringUtils.split(meeting.getCycle(), ",");
                    for (String cycle : cycles) {
                        if (StringUtils.isNotEmpty(cycle)) {
                            LocalDate meetingDate = calculateMeetingDate(meeting, startDate, cycle.trim());
                            // 创建一个 DateTimeFormatter 对象，并指定你想要的日期格式
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                            // 将字符串解析为 LocalDate 对象
                            LocalDate firstDayMonth = LocalDate.parse(firstDayOfMonth, formatter);
                            LocalDate lastDayMonth = LocalDate.parse(lastDayOfMonth, formatter);
                            if (meetingDate != null && !meetingDate.isBefore(firstDayMonth) && !meetingDate.isAfter(lastDayMonth)) {
                                String uniqueKeyBase = meeting.getCenterId() + "-" + meeting.getType() + "-" + meetingDate + "-" + center.getStandardCode();
                                // 使用周期结合时间段作为键值来区分上午和下午
                                String period = meeting.getPeriod();
                                String cyclePeriodKey = cycle.trim() + "-" + period;
                                Set<String> processedCyclePeriodMeetings = cyclePeriodProcessedMeetings.computeIfAbsent(cyclePeriodKey, k -> new HashSet<>());
                                // 检查当前周期和时间段内是否有相同的会议
                                if (!processedCyclePeriodMeetings.contains(uniqueKeyBase)) {
                                    // 创建 CalendarExportVO 对象
                                    CalendarExportVO exportVO = new CalendarExportVO();
                                    List<InitSmoDataAreaRelate> collect = initSmoDataAreaRelates.stream()
                                            .filter(relate -> relate.getProvince().equals(center.getProvince()))
                                            .collect(Collectors.toList());
                                    exportVO.setRegion(collect.get(0).getAreaName());
                                    exportVO.setProvinceCity(center.getProvince() + " " + center.getCity());
                                    exportVO.setCenterCode(center.getStandardCode());
                                    exportVO.setPeriod(period);
                                    exportVO.setCenterName(center.getCenterName());
                                    exportVO.setCenterAlias(center.getAlias());
                                    if ("伦理会".equals(meeting.getType())) {
                                        exportVO.setEthicsMeetingDate(meetingDate);
                                    } else if ("立项会".equals(meeting.getType())) {
                                        exportVO.setProjectMeetingDate(meetingDate);
                                    }
                                    calendarExportList.add(exportVO);
                                    // 添加到已处理会议集合中
                                    processedCyclePeriodMeetings.add(uniqueKeyBase);
                                }
                            }
                        }
                    }
                }
            }
        }
        // 生成 Excel 文件
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Calendar Export");
        // 创建表头
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("大区");
        headerRow.createCell(1).setCellValue("省市"); // 合并后的省市字段
        headerRow.createCell(2).setCellValue("中心标准编码");
        headerRow.createCell(3).setCellValue("中心标准名称");
        headerRow.createCell(4).setCellValue("别名");
        headerRow.createCell(5).setCellValue("会议时间段");
        headerRow.createCell(6).setCellValue("立项会日期");
        headerRow.createCell(7).setCellValue("伦理会日期");

        // 填充数据
        int rowNum = 1;
        for (CalendarExportVO exportVO : calendarExportList) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(exportVO.getRegion());
            row.createCell(1).setCellValue(exportVO.getProvinceCity()); // 填充合并后的省市
            row.createCell(2).setCellValue(exportVO.getCenterCode());
            row.createCell(3).setCellValue(exportVO.getCenterName());
            row.createCell(4).setCellValue(exportVO.getCenterAlias());
            row.createCell(5).setCellValue(exportVO.getPeriod());
            row.createCell(6).setCellValue(exportVO.getProjectMeetingDate() != null ? DATE_FORMATTER.format(exportVO.getProjectMeetingDate()) : "");
            row.createCell(7).setCellValue(exportVO.getEthicsMeetingDate() != null ? DATE_FORMATTER.format(exportVO.getEthicsMeetingDate()) : "");
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        // 设置文件名为当前导出日期加上"日历导出"
        String fileName = "日历导出_" + startDate + ".xlsx";
        response.setHeader("Content-Disposition", "attachment; filename=" + new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        // 写入输出流
        try (OutputStream outputStream = response.getOutputStream()) {
            workbook.write(outputStream);
        } catch (IOException e) {
            e.printStackTrace();
            // 处理异常
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            // 使用 OutputStream 写入错误信息
            try (OutputStream errorOutputStream = response.getOutputStream()) {
                errorOutputStream.write(("Error generating the Excel file: " + e.getMessage()).getBytes("UTF-8"));
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        } finally {
            // 关闭工作簿
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private LocalDate calculateMeetingDate(CenterMeetingQueryStrRespVO meeting, String startDate, String cycle) {
        LocalDate date = LocalDate.parse(startDate, DATE_FORMATTER);

        if ("指定日期".equals(meeting.getRepeatType())) {
            // 如果是“指定日期”，直接解析 cycle 为日期
            return LocalDate.parse(cycle, DATE_FORMATTER);
        } else if (StringUtils.isEmpty(meeting.getRepeatWeek())) {
            // 按月计算
            try {
                return date.plusMonths(Integer.parseInt(cycle));
            } catch (NumberFormatException e) {
                // 如果 cycle 不是整数，可能是日期字符串
                return LocalDate.parse(cycle, DATE_FORMATTER);
            }
        } else {
            // 按周计算
            try {
                return calculateWeeklyMeetingDate(meeting, date, Integer.parseInt(cycle));
            } catch (NumberFormatException e) {
                // 如果 cycle 不是整数，可能是日期字符串
                return parseDateOrReturnNull(cycle);
            }
        }
    }

    private LocalDate parseDateOrReturnNull(String cycle) {
        try {
            return LocalDate.parse(cycle, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            // 如果 cycle 不是有效的日期字符串，返回 null
            return null;
        }
    }

    private LocalDate calculateWeeklyMeetingDate(CenterMeetingQueryStrRespVO meeting, LocalDate date, int cycle) {
        // 解析周几
        DayOfWeek dayOfWeek = DayOfWeek.valueOf(meeting.getRepeatWeek());
        // 找到最近的一个指定周几
        LocalDate nextDate = date.with(TemporalAdjusters.nextOrSame(dayOfWeek));
        // 如果当前日期已经过了最近的指定周几，则跳到下一个
        if (nextDate.isBefore(date)) {
            nextDate = nextDate.plusWeeks(1);
        }
        // 根据周期增加周数
        return nextDate.plusWeeks(cycle - 1); // 减去1是因为 nextDate 已经是第一个周期
    }

}
