package com.r2.bi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.r2.bi.entity.bi.SysLog;
import com.r2.bi.vo.syslog.SysLogListReqVO;
import com.r2.bi.vo.syslog.SysLogListRespVO;


/**
 * 系统日志(SysLog)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-21 10:04:10
 */
public interface SysLogService extends IService<SysLog> {

    /**
     * 分页查询
     *
     * @param reqVO 筛选条件
     * @return 查询结果
     */
    Page<SysLogListRespVO> queryByPage(SysLogListReqVO reqVO);

    /**
     * 详情
     * @param id
     * @return
     */
    SysLogListRespVO queryById(Integer id);
}