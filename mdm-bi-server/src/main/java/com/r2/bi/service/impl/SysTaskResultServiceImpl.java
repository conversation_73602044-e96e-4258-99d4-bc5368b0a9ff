package com.r2.bi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.r2.bi.dao.bi.SysTaskResultDao;
import com.r2.bi.entity.bi.SysTaskResult;
import com.r2.bi.service.SysTaskResultService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 定时任务执行结果表(SysTaskResult)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-03 16:46:33
 */
@Service("sysTaskResultService")
public class SysTaskResultServiceImpl extends ServiceImpl<SysTaskResultDao, SysTaskResult> implements SysTaskResultService {

    @Resource
    private SysTaskResultDao sysTaskResultDao;

    @Override
    public void insert(SysTaskResult sysTaskResult) {
        sysTaskResultDao.insert(sysTaskResult);
    }
}
