package com.r2.bi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.r2.bi.entity.cs.DataAreaRelateReport;
import com.r2.bi.vo.dataarearelate.DataAreaRelateReqVO;
import com.r2.bi.vo.dataarearelate.UpdateDataAreaRelateReqVO;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

public interface DataAreaRelateService {

    Page<DataAreaRelateReport> list(DataAreaRelateReqVO req);

    Map<String, Object> count();

    void export(UpdateDataAreaRelateReqVO reqVO,HttpServletResponse response);

}
