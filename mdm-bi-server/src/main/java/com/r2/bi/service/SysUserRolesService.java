package com.r2.bi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.r2.bi.entity.bi.SysUserRoles;

import java.util.List;

/**
 * (SysUserRoles)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-31 20:22:14
 */
public interface SysUserRolesService extends IService<SysUserRoles> {

    /**
     * 根据用户id查询角色id
     * @param userId
     * @return
     */
    List<Long> getByUserIds(Long userId);

    /**
     * 跟据用户批量删除
     * @param userIds
     * @return
     */
    Object deleteByUserIds(List<Long> userIds);

    /**
     * 批量新增
     * @param userRoles
     * @return
     */
    Object batchInsert(List<SysUserRoles> userRoles);
}

