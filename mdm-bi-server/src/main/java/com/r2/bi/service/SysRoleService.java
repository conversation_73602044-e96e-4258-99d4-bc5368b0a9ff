package com.r2.bi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.r2.bi.entity.bi.SysRole;
import com.r2.bi.vo.sysrole.*;


import java.util.List;

/**
 * 角色表(SysRole)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-21 09:55:47
 */
public interface SysRoleService extends IService<SysRole> {

    /**
     * 跟据角色id查询
     * @param roleIds
     * @return
     */
    List<SysRole> getByIds(List<Long> roleIds);

    /**
     * 简单列表
     *
     * @return
     */
    Page<SysRoleSimpleListRespVO> simpleQueryByPage(SysRoleListReqVO reqVO);


    /**
     * 菜单列表
     *
     * @param reqVO 筛选条件
     * @return 查询结果
     */
    Page<SysRoleListRespVO> queryByPage(SysRoleListReqVO reqVO);


    /**
     * 新增
     *
     * @param reqVO
     * @return
     */
    Boolean insert(SysRoleCreateReqVO reqVO);


    /**
     * 更新
     *
     * @param reqVO
     * @return
     */
    Boolean update(SysRoleUpdateReqVO reqVO);


    /**
     * 停用/启用
     * @param id
     * @param status
     * @return
     */
    Boolean disable(Integer id, Integer status);



    /**
     * 删除
     * @param id
     * @return
     */
    Boolean delete(Integer id);
}

