package com.r2.bi.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.r2.bi.bo.DataAreaRelateReportExportBO;
import com.r2.bi.dao.cs.BiCenterDao;
import com.r2.bi.dao.cs.DataAreaRelateReportDao;
import com.r2.bi.entity.cs.Center;
import com.r2.bi.entity.cs.DataAreaRelateReport;
import com.r2.bi.service.DataAreaRelateService;
import com.r2.bi.vo.BiCenterRespVO;
import com.r2.bi.vo.dataarearelate.DataAreaRelateReqVO;
import com.r2.bi.vo.dataarearelate.UpdateDataAreaRelateReqVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 区域关联主表(DataAreaRelate)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-03 17:59:25
 */
@SuppressWarnings("ALL")
@Slf4j
@Service
public class DataAreaRelateServiceImpl implements DataAreaRelateService {

    @Resource
    private DataAreaRelateReportDao dataAreaRelateReportMapper;

    @Resource
    private BiCenterDao centerMapper;

    @Override
    public Page<DataAreaRelateReport> list(DataAreaRelateReqVO req) {

        Page<DataAreaRelateReport> page = new Page<>(req.getPage(), req.getSize());
        // 构建查询条件
        LambdaQueryWrapper<DataAreaRelateReport> wrapper = new LambdaQueryWrapper<>();

        // 拼接区域名称模糊查询
        if (StringUtils.isNotBlank(req.getAreaName())) {
            wrapper.like(DataAreaRelateReport::getAreaName, req.getAreaName());
        }

        // 拼接中心名称模糊查询
        if (StringUtils.isNotBlank(req.getCenterName())) {
            wrapper.like(DataAreaRelateReport::getCenterName, req.getCenterName());
        }

        // 拼接 SMO 更新人精确查询
        if (StringUtils.isNotBlank(req.getUpdateBy())) {
            wrapper.eq(DataAreaRelateReport::getUpdator, req.getUpdateBy());
        }

        // 排序
        String field = req.getField();
        String order = req.getOrder();

        List<OrderItem> orderItems = new ArrayList<>();

        if (field != null && !field.trim().isEmpty()) {
            String column = "";
            switch (field.toLowerCase()) {
                case "smoupdatorloginnum":
                    column = "smo_updator_login_num"; break;
                case "smoupdatorlogintime":
                    column = "smo_updator_login_time"; break;
                case "centerupdatetime":
                    column = "center_update_time"; break;
                case "centerintegrityplus":
                    column = "center_integrity_plus"; break;
                default:
                    column = StrUtil.toUnderlineCase(field);
            }

            boolean isAsc = !"descend".equalsIgnoreCase(order);
            orderItems.add(new OrderItem(column, isAsc));
        } else {
            // 默认按区域内中心平均完整度降序排序
            orderItems.add(OrderItem.desc("area_center_integrity_plus"));
        }

        page.setOrders(orderItems);

        page = dataAreaRelateReportMapper.selectPage(page, wrapper);


        // 定义输入和输出的日期格式
        DateTimeFormatter inputFormatter = new DateTimeFormatterBuilder()
                .appendPattern("yyyy-M-d H:m:s.S")
                .parseLenient()
                .toFormatter(Locale.getDefault());
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 遍历每条记录并格式化日期
        for (DataAreaRelateReport report : page.getRecords()) {
            formatDateTimeFields(report, inputFormatter, outputFormatter);
        }

        return page;
    }

    private void formatDateTimeFields(DataAreaRelateReport report, DateTimeFormatter inputFormatter, DateTimeFormatter outputFormatter) {
        try {
            String smoUpdatorLoginTime = report.getSmoUpdatorLoginTime();
            if (smoUpdatorLoginTime != null && !smoUpdatorLoginTime.isEmpty()) {
                // 解析字符串为LocalDateTime
                LocalDateTime dateModified = LocalDateTime.parse(smoUpdatorLoginTime, inputFormatter);
                // 重新格式化为所需的格式
                String formattedDateModified = dateModified.format(outputFormatter);
                // 设置修改日期
                report.setSmoUpdatorLoginTime(formattedDateModified);
            } else {
                report.setSmoUpdatorLoginTime(null);
            }

            String centerUpdateTime = report.getCenterUpdateTime();
            if (centerUpdateTime != null && !centerUpdateTime.isEmpty()) {
                // 解析字符串为LocalDateTime
                LocalDateTime updateTime = LocalDateTime.parse(centerUpdateTime, inputFormatter);
                // 重新格式化为所需的格式
                String formatCenterUpdateTime = updateTime.format(outputFormatter);
                // 设置修改日期
                report.setCenterUpdateTime(formatCenterUpdateTime);
            } else {
                report.setCenterUpdateTime(null);
            }
        } catch (Exception e) {
            log.error("日期转换异常", e);
            report.setSmoUpdatorLoginTime(null);
            report.setCenterUpdateTime(null);
        }
    }

    @Override
    public Map<String, Object> count() {
        QueryWrapper<DataAreaRelateReport> reportQueryWrapper = new QueryWrapper<>();
        reportQueryWrapper.lambda()
                .isNotNull(DataAreaRelateReport::getAreaId);
        List<DataAreaRelateReport> reportList = dataAreaRelateReportMapper.selectList(reportQueryWrapper);

        // 提取所有区域名称
        Set<String> areaNames = reportList.stream()
                .map(DataAreaRelateReport::getAreaName)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 批量查询每个区域的中心数
        Map<String, Integer> centerCountMap = getCenterCountsByArea(areaNames);

        List<Map<String, Object>> resultList = new ArrayList<>();
        for (DataAreaRelateReport report : reportList) {
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("areaName", report.getAreaName());
            double integrityPlus = parseCenterIntegrityPlus(report.getAreaCenterIntegrityPlus());
            resultMap.put("areaCenterIntegrityPlus", formatPercentage(integrityPlus));
            resultMap.put("centerCount", centerCountMap.getOrDefault(report.getAreaName(), 0));
            resultList.add(resultMap);
        }

        resultList = resultList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                map -> map.values().toString(),
                                map -> map,
                                (existing, replacement) -> existing),
                        LinkedHashMap::new))
                .values()
                .stream()
                .collect(Collectors.toList());

        // 计算平均信息完整度
        double averageCompletionRate = calculateAverageCompletionRate(resultList);

        // 按信息完整度从大到小排序
        resultList.sort((o1, o2) -> {
            double value1 = parseCenterIntegrityPlus((String) o1.get("areaCenterIntegrityPlus"));
            double value2 = parseCenterIntegrityPlus((String) o2.get("areaCenterIntegrityPlus"));
            return Double.compare(value2, value1);
        });

        Map<String, Object> finalResult = new HashMap<>();
        finalResult.put("averageCompletionRate", formatPercentage(averageCompletionRate));
        finalResult.put("data", resultList);

        return finalResult;
    }

    private Map<String, Integer> getCenterCountsByArea(Set<String> areaNames) {
        Map<String, Integer> centerCountMap = new HashMap<>();

        for (String areaName : areaNames) {
            QueryWrapper<Center> centerQueryWrapper = new QueryWrapper<>();
            centerQueryWrapper.lambda()
                    .in(Center::getCenterName, getCenterNamesByAreaName(areaName))
                    .eq(Center::getIsDelete, false)
                    .isNotNull(Center::getId);

            List<Center> centerList = centerMapper.selectList(centerQueryWrapper);
            List<Center> deduplicatedList = centerList.stream()
                    .collect(Collectors.toMap(
                            center -> center.getCenterName() + "|" + center.getAddress(),
                            center -> center,
                            (existing, replacement) -> existing
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
            centerCountMap.put(areaName, deduplicatedList.size());
        }

        return centerCountMap;
    }

    private Set<String> getCenterNamesByAreaName(String areaName) {
        QueryWrapper<DataAreaRelateReport> reportQueryWrapper = new QueryWrapper<>();
        reportQueryWrapper.lambda()
                .eq(DataAreaRelateReport::getAreaName, areaName)
                .isNotNull(DataAreaRelateReport::getCenterName);

        List<DataAreaRelateReport> reports = dataAreaRelateReportMapper.selectList(reportQueryWrapper);
        return reports.stream()
                .map(DataAreaRelateReport::getCenterName)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    private double calculateAverageCompletionRate(List<Map<String, Object>> resultList) {
        double total = resultList.stream()
                .mapToDouble(map -> parseCenterIntegrityPlus((String) map.get("areaCenterIntegrityPlus")))
                .sum();
        int uniqueAreaCount = resultList.size();
        return total / uniqueAreaCount;
    }

    private double parseCenterIntegrityPlus(String areaCenterIntegrityPlus) {
        if (areaCenterIntegrityPlus == null || areaCenterIntegrityPlus.isEmpty()) {
            return 0.0;
        }
        try {
            // 去掉百分号并转换为 double
            return Double.parseDouble(areaCenterIntegrityPlus.replace("%", ""));
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    private String formatPercentage(double value) {
        BigDecimal bd = new BigDecimal(value).setScale(2, BigDecimal.ROUND_HALF_UP);
        return bd.toString() + "%";
    }

    @Override
    public void export(UpdateDataAreaRelateReqVO reqVO,HttpServletResponse response) {
        QueryWrapper<DataAreaRelateReport> queryWrapper = new QueryWrapper<>();
        // 使用 lambda 表达式构建查询条件
        queryWrapper.lambda()
                // 区域名称模糊匹配（like）
                .like(StringUtils.isNotBlank(reqVO.getAreaName()), DataAreaRelateReport::getAreaName, reqVO.getAreaName())
                // 中心名称模糊匹配（like）
                .like(StringUtils.isNotBlank(reqVO.getCenterName()), DataAreaRelateReport::getCenterName, reqVO.getCenterName())
                // 更新人精确匹配（eq）
                .eq(StringUtils.isNotBlank(reqVO.getUpdateBy()), DataAreaRelateReport::getUpdator, reqVO.getUpdateBy())
                // 按照区域名称升序排序
                .orderByAsc(DataAreaRelateReport::getAreaName);
        List<DataAreaRelateReport> reportList = dataAreaRelateReportMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(reportList)) {
            return;
        }

        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("中心信息完整度");

        List<String> fieldNames = Arrays.stream(DataAreaRelateReportExportBO.class.getDeclaredFields())
                .map(field -> field.getName())
                .collect(Collectors.toList());

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"区域", "v1.2区域内中心平均完整度", "v1.2 区域中心无效数据占比 7模块", "区域负责人", "中心名称", "SMO更新人",
                "更新人登录次数", "更新人最后登录时间", "v1.2字段填写完整度", "v1.2中心无效数据占比", "中心最新更新时间", "中心科室数量",
                "中心科室数据完整度", "中心科室无效数据占比", "中心人员数量", "中心人员数据完整度", "中心人员无效数据占比"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 填充数据
        int rowNum = 1;
        for (DataAreaRelateReport report : reportList) {
            Row row = sheet.createRow(rowNum++);
            for (int i = 0; i < fieldNames.size(); i++) {
                String fieldName = fieldNames.get(i);
                Object value = getFieldValueByName(fieldName, report);
                Cell cell = row.createCell(i);
                if (value instanceof String) {
                    cell.setCellValue((String) value);
                } else if (value instanceof Number) {
                    cell.setCellValue(((Number) value).doubleValue());
                } else if (value instanceof Boolean) {
                    cell.setCellValue((Boolean) value);
                } else if (value instanceof Date) {
                    cell.setCellValue(((Date) value).toString());
                } else {
                    cell.setCellValue(value == null ? "" : value.toString());
                }
            }
        }

        // 自动调整列宽
        for (int i = 0; i < fieldNames.size(); i++) {
            sheet.autoSizeColumn(i);
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
        String fileName = "中心信息完整度_" + dateFormat.format(new Date()) + ".xlsx";

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

        try {
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        try {
            workbook.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    private Object getFieldValueByName(String fieldName, Object obj){
        Class<?> clazz = obj.getClass();
        while (clazz != null) {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                try {
                    return field.get(obj);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        try {
            throw new NoSuchFieldException("Could not find field " + fieldName);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        }
    }
}

