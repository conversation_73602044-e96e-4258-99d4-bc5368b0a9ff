package com.r2.bi.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.r2.bi.bo.SysUserListBO;
import com.r2.bi.config.CredentialsMatcher;
import com.r2.bi.dao.bi.BiSysUserDao;
import com.r2.bi.entity.bi.*;
import com.r2.bi.enums.DuplicateKeyEnum;
import com.r2.bi.enums.SourceTypeEnum;
import com.r2.bi.service.*;
import com.r2.bi.util.DateTimeUtil;
import com.r2.bi.util.Sm2Util;
import com.r2.bi.util.ToolUtil;
import com.r2.bi.vo.BaseListReqVO;
import com.r2.bi.vo.sysUser.*;
import com.r2.framework.exception.CodeException;
import com.r2.framework.util.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 人员(SysUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-21 10:04:10
 */
@Service
@Slf4j
public class SysUserServiceImpl extends ServiceImpl<BiSysUserDao, BiUser> implements SysUserService {

    @Resource
    private BiSysUserDao biSysUserDao;

    @Resource
    private SysOrganizeService sysOrganizeService;

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private SysUserRolesService sysUserRolesService;

    @Resource
    private SysTaskResultService sysTaskResultService;

    @Resource
    private SysMdmService sysMdmService;

    /**
     * sm2密码解密秘钥
     */
    @Value("${password.privateKey}")
    private String privateKey;

    /**
     * 同步用户页数
     */
    private final Integer page = 9999;


    @Override
    public BiUser getByUserAccount(String userAccount) {
        LambdaQueryWrapper<BiUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BiUser::getIsDelete, false)
                .eq(BiUser::getJobNumber, userAccount);
        List<BiUser> users = biSysUserDao.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(users)) {
            return null;
        }
        return users.get(0);
    }

    @Override
    public BiUser getOneByJobNumber(String jobNumber) {
        LambdaQueryWrapper<BiUser> queryWrapper = new QueryWrapper<BiUser>()
                .lambda().eq(BiUser::getJobNumber, jobNumber)
                .eq(BiUser::getIsDelete, false)
                .last("limit 1");
        return biSysUserDao.selectOne(queryWrapper);
    }


    @Override
    public SysUserInfoRespVO getUserInfo() {
        SysUserInfoRespVO vo = new SysUserInfoRespVO();
        BiUser sysUser = (BiUser) SecurityUtils.getSubject().getPrincipal();
        sysUser = biSysUserDao.selectById(sysUser.getId());
        if (sysUser != null) {
            vo.setUsername(sysUser.getUsername());
        }
        vo.setUserId(sysUser.getId());
        return vo;
    }


    @Override
    public SysUserDetailRespVO getUserInfoDetail() {
        BiUser sysUser = (BiUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser == null) {
            throw new CodeException(ResultCode.Codes.ERROR, "用户不存在");
        }
        SysUserDetailRespVO respVO = new SysUserDetailRespVO();
        BiUser sysUserInfo = biSysUserDao.selectById(sysUser.getId());
        if (sysUserInfo == null || sysUserInfo.getIsDelete()) {
            throw new CodeException(ResultCode.Codes.ERROR, "用户不存在");
        }
        BeanUtils.copyProperties(sysUserInfo, respVO);
        return respVO;
    }

    @Override
    public Object list(SysUserListReqVO vo) {
        List<String> orgCodes = new ArrayList<>();
        if(vo.getOrganizeCode() != null){
            for(String organizeCode:vo.getOrganizeCode()){
                if (Strings.isNotBlank(organizeCode)) {
                    List<String> orgCodeList = sysOrganizeService.getOrgCodeList(organizeCode);
                    orgCodes.addAll(orgCodeList);
                }
            }
        }
        QueryWrapper<BiUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(Strings.isNotBlank(vo.getSearch()), "u.search_key", vo.getSearch())
                .in(!CollectionUtils.isEmpty(orgCodes), "u.organize_code", orgCodes)
                .eq(vo.getRoleId() != null, "ur.role_id", vo.getRoleId())
                .eq(Strings.isNotBlank(vo.getDutyName()), "u.duty_name", vo.getDutyName())
                .eq(vo.getPoiDempAdminName() != null, "u.poi_demp_admin_name", vo.getPoiDempAdminName())
                .eq("u.is_delete", false)
                .groupBy("u.id")
                .orderByDesc("u.create_time");
        Page<SysUserListBO> page = biSysUserDao.findPage(new Page<>(vo.getPage(), vo.getSize()), queryWrapper);
        if (page.getTotal() == 0) {
            return page;
        }

        Page<SysUserListRespVO> result = new Page<>();
        result.setTotal(page.getTotal());
        List<SysUserListRespVO> list = new ArrayList<>();
        List<SysUserListBO> records = page.getRecords();
        Map<String, String> deptMap = getDeptMap(records);
        Map<Long, String> roleMap = getRoleMap(records);
        for (SysUserListBO bo : records) {
            SysUserListRespVO user = new SysUserListRespVO();
            BeanUtils.copyProperties(bo, user);
            // 部门
            user.setDept(deptMap.get(bo.getOrganizeCode()));
            // 直属领导
            user.setLeaderName(bo.getPoiDempAdminName());
            // 角色
            if (Strings.isNotBlank(bo.getRoleIds())) {
                List<String> roleList = Arrays.asList(bo.getRoleIds().split(","));
                List<String> roleNames = new ArrayList<>();
                for (String r : roleList) {
                    if (Strings.isBlank(r) || roleMap.get(Long.valueOf(r)) == null) {
                        continue;
                    }
                    roleNames.add(roleMap.get(Long.valueOf(r)));
                }
                user.setRole(Joiner.on("，").join(roleNames));
                // 使用Stream API进行转换
                List<Long> longList = roleList.stream()
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                user.setRoleIds(longList);
            }
            list.add(user);
        }
        result.setRecords(list);
        return result;
    }

    /**
     * 获取部门map
     *
     * @param records
     * @return
     */
    private Map<String, String> getDeptMap(List<SysUserListBO> records) {
        List<String> organizeCodes = records.stream()
                .map(SysUserListBO::getOrganizeCode)
                .distinct()
                .collect(Collectors.toList());
        List<SysOrganize> list = sysOrganizeService.getByOrgCodes(organizeCodes);
        return list.stream()
                .collect(Collectors.toMap(SysOrganize::getOrganizeCode, SysOrganize::getOrganizeName));
    }

    /**
     * 获取角色map
     *
     * @param records
     * @return
     */
    private Map<Long, String> getRoleMap(List<SysUserListBO> records) {
        List<String> roleIds = records.stream()
                .filter(s -> Strings.isNotBlank(s.getRoleIds()))
                .map(SysUserListBO::getRoleIds)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(roleIds)) {
            return new HashMap<>();
        }
        List<Long> roleIdList = new ArrayList<>();
        for (String roleId : roleIds) {
            List<Long> list = Arrays.asList(roleId.split(",")).stream().map(l -> Long.valueOf(l)).collect(Collectors.toList());
            roleIdList.addAll(list);
        }
        if (CollectionUtils.isEmpty(roleIdList)) {
            return new HashMap<>();
        }
        roleIdList = roleIdList.stream().distinct().collect(Collectors.toList());
        List<SysRole> roles = sysRoleService.getByIds(roleIdList);
        return roles.stream()
                .collect(Collectors.toMap(SysRole::getId, SysRole::getName));
    }

//    /**
//     * 获取用户map
//     *
//     * @param records
//     * @return
//     */
//    private Map<Long, String> getUserMap(List<SysUserListBO> records) {
//        List<Long> userids = records.stream()
//                .map(SysUserListBO::getPoiDempAdmin)
//                .distinct()
//                .collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(userids)) {
//            return new HashMap<>();
//        }
//        List<BiUser> users = getByUserids(userids);
//        return users.stream()
//                .collect(Collectors.toMap(BiUser::getUserid, BiUser::getUsername, (v1, v2) -> v2));
//    }

    @Override
    public List<BiUser> getByUserids(List<Long> userids) {
        LambdaQueryWrapper<BiUser> queryWrapper = new QueryWrapper<BiUser>()
                .lambda().in(BiUser::getUserId, userids)
                .eq(BiUser::getIsDelete, false);
        return biSysUserDao.selectList(queryWrapper);
    }


    @Override
    public Object dropDutyNameList(BaseListReqVO vo) {
        QueryWrapper<BiUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(BiUser::getDutyName)
                .isNotNull(BiUser::getDutyName)
                .ne(BiUser::getDutyName, "")
                .groupBy(BiUser::getDutyName);
        return biSysUserDao.selectPage(new Page<>(vo.getPage(), vo.getSize()), queryWrapper);
    }

    @Override
    public Object dropLeaders(BaseListReqVO vo) {
        QueryWrapper<BiUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(BiUser::getPoiDempAdminName)
                .isNotNull(BiUser::getPoiDempAdminName)
                .ne(BiUser::getPoiDempAdminName, "")
                .groupBy(BiUser::getPoiDempAdminName);
        Page<BiUser> biUserPage = biSysUserDao.selectPage(new Page<>(vo.getPage(), vo.getSize()), queryWrapper);
        return biUserPage;
    }

    @Override
    public SysUserDetailRespVO detail(Long id) {
        SysUserDetailRespVO result = new SysUserDetailRespVO();
        BiUser sysUser = biSysUserDao.selectById(id);
        BeanUtils.copyProperties(sysUser, result);
        if (sysUser == null || sysUser.getIsDelete()) {
            throw new CodeException(ResultCode.Codes.ERROR, "用户不存在");
        }
        // 角色
        result.setRoleIds(sysUserRolesService.getByUserIds(id));

        // 直属领导
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object edit(List<SysUserEditReqVO> vos) {
        for (SysUserEditReqVO vo : vos) {
            Long id = vo.getId();
            Date date = new Date();
            // 新增用户
            List<BiUser> sysUsers = getByJobNumber(vo.getJobNumber());
            if (id == null) {
                if (!CollectionUtils.isEmpty(sysUsers)) {
                    BiUser sysUser = sysUsers.get(0);
                    if (sysUser.getSourceType() == SourceTypeEnum.SSU_SYS.getCode()) {
                        throw new CodeException(ResultCode.Codes.ERROR, "系统已存在相同工号，无法重复保存");
                    }
                    if (sysUser.getSourceType() == SourceTypeEnum.BEI_SENG.getCode()) {
                        throw new CodeException(ResultCode.Codes.ERROR, "北森已存在相同工号，无法重复保存");
                    }
                }
                BiUser sysUser = new BiUser();
                BeanUtils.copyProperties(vo, sysUser);
                sysUser.setUpdateTime(date);
                sysUser.setSearchKey(ToolUtil.join(",", vo.getUsername(), vo.getMobile(), vo.getEmail(), vo.getJobNumber()));
                // 不填如果为空
                if (Strings.isNotBlank(sysUser.getPassword())) {
                    //密码解密
                    String decryptPwd = Sm2Util.decrypt(privateKey, vo.getPassword());
                    // 校验密码
                    if (!decryptPwd.matches("^(?![0-9]+$)(?!a-zA-Z]+$)(?![0-9a-zA-Z]+$)(?![0-9\\\\W]+$)(?![a-zA-Z\\\\W]+$)[0-9A-Za-z!@#$%^&*\\\\W][^\f\n\r\t\\v]{7,17}$")) {
                        throw new CodeException(ResultCode.Codes.ERROR,
                                "密码不符合要求——新密码要求为8-16位英文（大+小写）+数字+符号组合");
                    }
                    String salt = ToolUtil.getUUId();
                    sysUser.setSalt(salt);
                    sysUser.setPassword(CredentialsMatcher.generatePassword(salt, decryptPwd));
                } else {
                    throw new CodeException(ResultCode.Codes.ERROR, "初始密码不能为空");
                }

                sysUser.setEmail(vo.getEmail());
                sysUser.setCreateTime(new Date());
                sysUser.setUpdateTime(new Date());
                sysUser.setSourceType(SourceTypeEnum.SSU_SYS.getCode());
                //初始化密码重置日期：当天
                sysUser.setResetDay(DateTimeUtil.today().toString());
                try {
                    biSysUserDao.insert(sysUser);
                } catch (DuplicateKeyException e) {
                    log.error("error={}", e.getMessage());
                    throw new CodeException(ResultCode.Codes.ERROR, DuplicateKeyEnum.getNameByCode(e.getMessage()) + "已存在，无法重复保存");
                }
                id = sysUser.getId();
            } else {
                if (!CollectionUtils.isEmpty(sysUsers)) {
                    for (BiUser sysUser : sysUsers) {
                        if (!sysUser.getId().equals(vo.getId())) {
                            if (Objects.equals(sysUser.getJobNumber(), vo.getJobNumber())) {
                                if (sysUser.getSourceType() == SourceTypeEnum.SSU_SYS.getCode()) {
                                    throw new CodeException(ResultCode.Codes.ERROR, "系统已存在相同工号，无法重复保存");
                                }
                                if (sysUser.getSourceType() == SourceTypeEnum.BEI_SENG.getCode()) {
                                    throw new CodeException(ResultCode.Codes.ERROR, "北森已存在相同工号，无法重复保存");
                                }
                            }
                        }
                    }
                }
                // 编辑用户
                UpdateWrapper<BiUser> updateWrapper = new UpdateWrapper<>();
                if (vo.getSourceType() == SourceTypeEnum.SSU_SYS.getCode()) {
                    updateWrapper.lambda().set(BiUser::getUsername, vo.getUsername())
                            .set(BiUser::getEmail, vo.getEmail())
                            .set(BiUser::getSearchKey, ToolUtil.join(",", vo.getUsername(), vo.getMobile(), vo.getEmail(), vo.getJobNumber()))
                            .set(BiUser::getUpdateTime, new Date())
                            .set(BiUser::getJobNumber, vo.getJobNumber())
                            .set(BiUser::getDutyName, vo.getDutyName())
                            .set(BiUser::getOrganizeCode, vo.getOrganizeCode())
                            .eq(BiUser::getId, id);
                    try {
                        biSysUserDao.update(null, updateWrapper);
                    } catch (DuplicateKeyException e) {
                        throw new CodeException(ResultCode.Codes.ERROR, DuplicateKeyEnum.getNameByCode(e.getMessage()) + "已存在，无法重复保存");
                    }
                }
            }
            // 编辑角色
            List<Long> userIds = new ArrayList<>();
            userIds.add(id);
            sysUserRolesService.deleteByUserIds(userIds);
            if (!CollectionUtils.isEmpty(vo.getRoleIds())) {
                List<SysUserRoles> sysUserRoles = new ArrayList<>();
                for (Long roleId : vo.getRoleIds()) {
                    SysUserRoles userRoles = new SysUserRoles();
                    userRoles.setUserId(id);
                    userRoles.setRoleId(roleId);
                    sysUserRoles.add(userRoles);
                }
                sysUserRolesService.batchInsert(sysUserRoles);
            }
        }
        return null;
    }

    @Override
    public List<BiUser> getByJobNumber(String jobNumber) {
        LambdaQueryWrapper<BiUser> queryWrapper = new QueryWrapper<BiUser>()
                .lambda().eq(BiUser::getJobNumber, jobNumber)
                .eq(BiUser::getIsDelete, false);
        return biSysUserDao.selectList(queryWrapper);
    }

    @Override
    public BiUser checkByJobNumber(String jobNumber) {
        LambdaQueryWrapper<BiUser> queryWrapper = new QueryWrapper<BiUser>()
                .lambda().eq(BiUser::getJobNumber, jobNumber)
                .eq(BiUser::getIsDelete, false);
        return biSysUserDao.selectOne(queryWrapper);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object syncUser() {
        try {
            //定时任务记录
            SysTaskResult sysTaskResult = new SysTaskResult();
            sysTaskResult.setName("同步主数据平台用户信息");
            sysTaskResult.setCreateTime(new Date());
            sysTaskResultService.insert(sysTaskResult);

            //获取系统id
            Integer systemId = sysMdmService.getSystemId();
            if (null != systemId) {
                //数据总条数
                Integer totalDataNum = 0;
                //新增数据条数
                Integer newDataNum = 0;
                //更新数据条数
                Integer updateDataNum = 0;

                Date curDate = new Date();
                //获取当前系统所有用户除系统管理员
                QueryWrapper<BiUser> userQuery = new QueryWrapper<>();
                userQuery.lambda().isNotNull(BiUser::getUserId);
                List<BiUser> currentUserList = biSysUserDao.selectList(userQuery);
                Map<Long, BiUser> prepareDeleteUserMap = currentUserList.stream().collect(Collectors.toMap(BiUser::getUserId, Function.identity()));

                for (int i = 1; i < this.page; i++) {
                    List<BiUser> sysUsers = sysMdmService.syncUser(systemId, i);
                    if (CollectionUtil.isNotEmpty(sysUsers)) {
                        for (BiUser mdmUser : sysUsers) {
                            // 同步用户从当前用户中剔除 剩下的就是待删除的用户
                            prepareDeleteUserMap.remove(mdmUser.getId());

                            totalDataNum++;
                            QueryWrapper<BiUser> sysUserQueryWrapper = new QueryWrapper<>();
                            sysUserQueryWrapper.lambda().select(BiUser::getId).eq(BiUser::getUserId, mdmUser.getId()).last("limit 1");
                            BiUser curUser = biSysUserDao.selectOne(sysUserQueryWrapper);
                            if (ObjectUtil.isNotEmpty(curUser)) {
                                //更新用户
                                UpdateWrapper<BiUser> updateWrapper = new UpdateWrapper<>();
                                updateWrapper.lambda().eq(BiUser::getId, curUser.getId())
                                        .set(BiUser::getMobile, mdmUser.getMobile())
                                        .set(BiUser::getJobNumber, mdmUser.getJobNumber())
                                        .set(BiUser::getUsername, mdmUser.getUsername())
                                        .set(BiUser::getUserAccount, mdmUser.getUserAccount())
                                        .set(BiUser::getEmail, mdmUser.getEmail())
                                        .set(BiUser::getDutyName, mdmUser.getDutyName())
                                        .set(BiUser::getPoiDempAdminName, mdmUser.getPoiDempAdminName())
                                        .set(BiUser::getOrganizeCode, mdmUser.getOrganizeCode())
                                        .set(BiUser::getIsDelete, false)
                                        .set(BiUser::getUpdateTime, curDate)
                                        .set(BiUser::getSourceType, mdmUser.getSourceType())
                                        .set(BiUser::getResetDay, mdmUser.getResetDay())
                                        .set(BiUser::getSearchKey, mdmUser.getUsername() + "," + mdmUser.getJobNumber() + "," + mdmUser.getEmail());
                                biSysUserDao.update(null, updateWrapper);
                                updateDataNum++;
                            } else {
                                //新增用户
                                BiUser sysUser = new BiUser();
                                sysUser.setCreateTime(curDate);
                                sysUser.setUpdateTime(curDate);
                                sysUser.setUserId(mdmUser.getId());
                                sysUser.setJobNumber(mdmUser.getJobNumber());
                                sysUser.setMobile(mdmUser.getMobile());
                                sysUser.setUsername(mdmUser.getUsername());
                                sysUser.setUserAccount(mdmUser.getUserAccount());
                                sysUser.setEmail(mdmUser.getEmail());
                                sysUser.setDutyName(mdmUser.getDutyName());
                                sysUser.setPoiDempAdminName(mdmUser.getPoiDempAdminName());
                                sysUser.setOrganizeCode(mdmUser.getOrganizeCode());
                                sysUser.setSourceType(mdmUser.getSourceType());
                                sysUser.setResetDay(mdmUser.getResetDay());
                                sysUser.setSearchKey(mdmUser.getUsername() + "," + mdmUser.getJobNumber() + "," + mdmUser.getEmail());
                                biSysUserDao.insert(sysUser);
                                newDataNum++;
                            }
                        }
                    } else {
                        log.info("同步主数据平台用户信息当前页无数据返回：" + i);
                        break;
                    }
                }
                //删除用户
                if (CollectionUtil.isNotEmpty(prepareDeleteUserMap)) {
                    List<Long> ids = new ArrayList<>(prepareDeleteUserMap.keySet());
                    UpdateWrapper<BiUser> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda().in(BiUser::getUserId, ids).set(BiUser::getIsDelete, true);
                    biSysUserDao.update(null, updateWrapper);
                }
                sysTaskResult.setResult("数据总条数:" + totalDataNum + ";更新数据条数:" + updateDataNum + ";新增数据条数:" + newDataNum);
            } else {
                sysTaskResult.setResult("获取系统id异常！");
            }
            sysTaskResult.setUpdateTime(new Date());
            sysTaskResultService.updateById(sysTaskResult);
        } catch (Exception e) {
            log.error("同步主数据平台用户信息信息异常 error={}", e.getMessage());
        }
        return true;
    }
}

