package com.r2.bi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.r2.bi.entity.bi.SysOrganize;
import com.r2.bi.vo.sysorg.SysOrgListReqVO;
import com.r2.bi.vo.sysorg.SysOrgTreeNodeRespVO;


import java.util.List;

/**parentCode
 * 组织部门(SysOrganize)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-02 10:41:31
 */
public interface SysOrganizeService extends IService<SysOrganize> {

    /**
     * 返回指定组织下的所有组织代码
     *
     * @param orgCode 指定组织代码
     * @return 下拉树结构列表
     */
    List<String> getOrgCodeList(String orgCode);


    /**
     * 查询组织管理数据,返回树形结构需要的实体类
     *
     * @param org 组织信息
     * @return 组织信息集合
     */
    List<SysOrgTreeNodeRespVO> selectTreeOrgList(SysOrgListReqVO org);

    /**
     * 构建前端所需要树结构
     *
     * @param orgs 组织列表
     * @return 树结构列表
     */
    List<SysOrgTreeNodeRespVO> buildOrgTree(List<SysOrgTreeNodeRespVO> orgs);


    /**
     * 跟据
     * @param orgCodes
     * @return
     */
    List<SysOrganize> getByOrgCodes(List<String> orgCodes);


    /**
     * 查询组织管理数据
     *
     * @param org 组织信息
     * @return 组织信息集合
     */
    List<SysOrganize> selectOrgList(SysOrgListReqVO org);

}
