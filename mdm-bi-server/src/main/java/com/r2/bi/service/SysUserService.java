package com.r2.bi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.r2.bi.entity.bi.BiUser;
import com.r2.bi.vo.BaseListReqVO;
import com.r2.bi.vo.sysUser.*;


import java.util.List;

/**
 * 人员(SysUser)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-21 10:04:10
 */
public interface SysUserService extends IService<BiUser> {


    /**
     *
     * @param userAccount
     */
    BiUser getByUserAccount(String userAccount);


    /**
     * 跟据工号查询
     * @param jobNumber
     * @return
     */
    BiUser getOneByJobNumber(String jobNumber);



    /**
     * 获取系统用户
     * @return
     */
    SysUserInfoRespVO getUserInfo();


    /**
     * 获取系统用户详情
     * @return
     */
    SysUserDetailRespVO getUserInfoDetail();


    /**
     * 列表
     * @param vo
     * @return
     */
    Object list(SysUserListReqVO vo);


    /**
     * 根据userid查询
     * @param userids
     * @return
     */
    List<BiUser> getByUserids(List<Long> userids);


    /**
     * 职位列表
     * @param vo
     * @return
     */
    Object dropDutyNameList(BaseListReqVO vo);

    /**
     * 下拉直属领导列表
     * @return
     */
    Object dropLeaders(BaseListReqVO vo);


    /**
     * 详情
     * @param id
     * @return
     */
    SysUserDetailRespVO detail(Long id);


    /**
     * 新增/编辑
     * @param vo
     * @return
     */
    Object edit(List<SysUserEditReqVO> vo);

    /**
     * 跟据工号查询
     * @param jobNumber
     * @return
     */
    List<BiUser> getByJobNumber(String jobNumber);


    /**
     * 根据工号查看当前用户是否存在
     * @param jobNumber
     * @return
     */
    BiUser checkByJobNumber(String jobNumber);




    /**
     * 用户同步
     * @param
     * @return
     */
    Object syncUser();
}

