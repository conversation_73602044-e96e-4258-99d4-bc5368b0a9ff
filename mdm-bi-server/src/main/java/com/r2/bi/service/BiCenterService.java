package com.r2.bi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.r2.bi.vo.BiCenterRepVO;
import com.r2.bi.vo.BiCenterRespVO;
import com.r2.bi.vo.bi.*;
import com.r2.bi.vo.bi.center.AreaListReqVO;
import com.r2.bi.vo.bi.center.BiStartSpeedVO;
import com.r2.bi.vo.bi.center.InfoStartSpeedDetailVO;
import com.r2.bi.vo.bi.center.SysUserAuthorityReqVO;
import com.r2.bi.vo.bi.centermember.BiCenterMemberReqVO;
import com.r2.bi.vo.bi.centermember.BiCenterMemberRespVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <AUTHOR>
 */
public interface BiCenterService {


    /**
     * 权限状态更新
     * @param vo
     * @return
     */
    Object updateStatus(SysUserAuthorityReqVO vo);

    /**
     * 中心数据统计
     * @param
     * @return
     */
    Object centerCountList(AreaListReqVO areaListVO);


    /**
     * 获取区域、省市、中心数据
     * @param
     * @return
     */
    Object getAggregatedDataAsArray();

    /**
     * 中心列表
     * @return
     */
    Page<BiCenterRespVO> ssuCenterList(BiCenterRepVO repVO);

    /**
     * 中心列表详情
     * @return
     */
    BiCenterDetailsRespVO centerDetails(BiCenterDetailsRepVO repVO);

    /**
     * 根据中心和时间段返回日历列表
     *
     * @param listReqVO
     * @return
     */
    List<BiCalendarRespVO> infoCalendarByScope(BiCalendarReqVO listReqVO);

    /**
     * 单个中心完整性报告
     *
     * @param req
     * @return
     */
    BiRelateReportRespVO centerIntegrity(BiRelateReportReqVO req);

    /**
     * 单个中心完整性报告导出
     *
     * @param req
     * @return
     */
    Object exportToExcel(BiRelateReportReqVO req, HttpServletResponse response);

    /**
     * 启动速度
     * @return
     */
    Object startSpeed(BiStartSpeedVO speedVO);

    /**
     * 启动速度详情
     * @param detailVO
     * @return
     */
    Page<CustomTableDataWithDetailsReqVO> startSpeedDetail(InfoStartSpeedDetailVO detailVO);


    /**
     * 分页查询
     * @return 查询结果
     */
    Page<BiCenterMemberRespVO> queryByPage(BiCenterMemberReqVO reqVO);



}
