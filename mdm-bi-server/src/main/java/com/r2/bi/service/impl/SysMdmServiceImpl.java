package com.r2.bi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.r2.bi.entity.bi.BiUser;
import com.r2.bi.service.SysMdmService;
import com.r2.bi.util.TokenGeneratorUtil;
import com.r2.bi.vo.login.AuthUpdatePasswordReqVO;
import com.r2.framework.exception.CodeException;
import com.r2.framework.util.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class SysMdmServiceImpl implements SysMdmService {

    /**
     * 主数据平台 请求地址
     */
    @Value("${mdm.url}")
    private String mdmUrl;

    /**
     * 主数据平台地址
     */
    @Value("${mdm.ssoUrl}")
    private String ssoUrl;

    /**
     * 主数据平台 请求地址
     */
    @Value("${mdm.tokenSalt}")
    private String salt;

    /**
     * 获取系统id api
     */
    private String systemIdApi = "/thirdParty/systemInfo";

    /**
     * 获取系统用户 api
     */
    private String syncUserApi = "/thirdParty/systemUser";

    /**
     * 系统用户登录 api
     */
    private String mdmLogin = "/auth/thirdSystemAuth";

    /**
     * 用户登录-[返回详细错误码及提示信息]
     */
    private String mdmLogin2 = "/auth/thirdPartyLogin";

    /**
     * 修改密码
     */
    private String mdmUpdatePassword = "/thirdParty/updatePassword";

    /**
     * 发送修改密码验证码
     */
    private String sendUpdatePasswordVerifyCode = "/thirdParty/verifyCode";

    private String generateToken = "/token/generateToken";

    /**
     * 系统名称
     */
    private String systemName = "bi";

    private String generateTokenSalt = "k-jf7}z8su5d9RIJ";

    @Override
    public Integer getSystemId() {
        Integer systemId = null;
        JSONObject dataObj = getRequest(systemIdApi, "systemName", this.systemName);
        systemId = Integer.parseInt(dataObj.get("id").toString());
        return systemId;
    }
//
//    @Override
//    public List<SysUser> syncUser(Integer systemId, Integer page) {
//        List<SysUser> sysUsers = new ArrayList<>();
//        String key = "";
//        try {
//            //生成key
//            JSONObject userData = new JSONObject();
//            userData.put("systemId", systemId);
//            key = TokenGeneratorUtil.encrypt(userData.toString());
//        } catch (Exception e) {
//            log.info("参数异常:{}", e.getMessage());
//            throw new CodeException(ResultCode.Codes.ERROR, "参数异常");
//        }
//
//        if (StrUtil.isNotEmpty(key)) {
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("key", key);
//            jsonObject.put("page", page);
//
//            JSONObject dataObj = postRequest(syncUserApi, jsonObject);
//            Object records = dataObj.get("records");
//            sysUsers = JSONObject.parseArray(records.toString(), SysUser.class);
//        }
//        return sysUsers;
//    }

    @Override
    public JSONObject login(String jobNumber, String password) {
        try {
            //获取系统id
            Integer systemId = getSystemId();
            //生成key
            JSONObject userData = new JSONObject();
            userData.put("jobNumber", jobNumber);
            userData.put("password", password);
            userData.put("systemId", systemId);
            String encodeStr = TokenGeneratorUtil.encrypt(userData.toString());

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("key", encodeStr);
            JSONObject dataObj = postRequest(mdmLogin2, jsonObject);
            return dataObj;

        } catch (Exception e) {
            log.info("参数异常:{}", e.getMessage());
            throw new CodeException(ResultCode.Codes.ERROR, e.getMessage());
        }
    }

    @Override
    public List<BiUser> syncUser(Integer systemId, Integer page) {
        List<BiUser> sysUsers = new ArrayList<>();
        String key = "";
        try {
            //生成key
            JSONObject userData = new JSONObject();
            userData.put("systemId", systemId);
            key = TokenGeneratorUtil.encrypt(userData.toString());
        } catch (Exception e) {
            log.info("参数异常:{}", e.getMessage());
            throw new CodeException(ResultCode.Codes.ERROR, "参数异常");
        }

        if (StrUtil.isNotEmpty(key)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("key", key);
            jsonObject.put("page", page);

            JSONObject dataObj = postRequest(syncUserApi, jsonObject);
            Object records = dataObj.get("records");
            sysUsers = JSONObject.parseArray(records.toString(), BiUser.class);
        }
        return sysUsers;
    }



    @Override
    public JSONObject updatePassword(AuthUpdatePasswordReqVO authUpdatePasswordReqVO) {
        try {
            //获取系统id
            Integer systemId = getSystemId();
            //生成key
            JSONObject userData = new JSONObject();
            userData.put("systemId", systemId);
            userData.put("jobNumber", authUpdatePasswordReqVO.getJobNumber());
            userData.put("code", authUpdatePasswordReqVO.getCode());
            userData.put("newPassword", authUpdatePasswordReqVO.getNewPassword());
            String encodeStr = TokenGeneratorUtil.encrypt(userData.toString());

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("key", encodeStr);

            JSONObject result = postRequest(mdmUpdatePassword,jsonObject);
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * get请求
     * @param api
     * @param paramName
     * @param paramValue
     * @return
     */
    private JSONObject getRequest(String api, String paramName, String paramValue) {
        JSONObject dataObj = new JSONObject();
        RestTemplate restTemplate = new RestTemplate();
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(mdmUrl + api)
                .queryParam(paramName, paramValue);
        String finalUrl = builder.toUriString();
        log.info("请求连接：" + finalUrl);
        ResponseEntity<String> responseEntity = restTemplate.getForEntity(finalUrl, String.class);
        log.info(finalUrl + " 返回json:" + JSON.toJSONString(responseEntity));
        if (ObjectUtil.isNotEmpty(responseEntity)) {
            //返回值解析
            String body = responseEntity.getBody();
            if (StrUtil.isNotEmpty(body)) {
                JSONObject jsonObject = JSON.parseObject(body);
                String code = jsonObject.get("code").toString();
                if ("10000".equals(code)) {
                    dataObj = JSON.parseObject(jsonObject.get("data").toString());
                } else {
                    throw new CodeException(ResultCode.Codes.ERROR, jsonObject.get("message").toString());
                }
            }
        }
        return dataObj;
    }

    /**
     * post请求
     * @param api
     * @return
     */
    private JSONObject postRequest(String api, JSONObject jsonObject) {
        JSONObject dataObj = new JSONObject();
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<JSONObject> jsonObjectHttpEntity = new HttpEntity<>(jsonObject, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(mdmUrl + api, jsonObjectHttpEntity, String.class);
        log.info(mdmUrl + api + " 返回json:" + JSON.toJSONString(responseEntity));
        if (ObjectUtil.isNotEmpty(responseEntity)) {
            //返回值解析
            String body = responseEntity.getBody();
            if (StrUtil.isNotEmpty(body)) {
                JSONObject jsonObj = JSON.parseObject(body);
                String code = jsonObj.get("code").toString();
                if ("10000".equals(code)) {
                    dataObj = JSON.parseObject(jsonObj.get("data").toString());
                } else {
                    throw new CodeException(ResultCode.Codes.ERROR, jsonObj.get("message").toString());
                }
            }
        }
        return dataObj;
    }

    /**
     * post请求
     * @param api
     * @return
     */
    private JSONObject ssoPostRequest(String api, JSONObject jsonObject) {
        JSONObject dataObj = new JSONObject();
        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<JSONObject> jsonObjectHttpEntity = new HttpEntity<>(jsonObject, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(ssoUrl + api, jsonObjectHttpEntity, String.class);
        log.info(ssoUrl + api + " 返回json:" + JSON.toJSONString(responseEntity));
        if (ObjectUtil.isNotEmpty(responseEntity)) {
            //返回值解析
            String body = responseEntity.getBody();
            if (StrUtil.isNotEmpty(body)) {
                JSONObject jsonObj = JSON.parseObject(body);
                String code = jsonObj.get("code").toString();
                if ("10000".equals(code)) {
                    dataObj = JSON.parseObject(jsonObj.get("data").toString());
                } else {
                    throw new CodeException(ResultCode.Codes.ERROR, jsonObj.get("message").toString());
                }
            }
        }
        return dataObj;
    }
}
