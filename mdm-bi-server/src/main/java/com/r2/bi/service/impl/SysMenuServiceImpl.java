package com.r2.bi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.r2.bi.dao.bi.TSysMenuDao;
import com.r2.bi.entity.bi.BiUser;
import com.r2.bi.entity.bi.SysMenu;
import com.r2.bi.service.SysMenuService;
import com.r2.bi.vo.sysmenu.SysMenuCreateReqVO;
import com.r2.bi.vo.sysmenu.SysMenuListReqVO;
import com.r2.bi.vo.sysmenu.SysMenuListRespVO;
import com.r2.bi.vo.sysmenu.SysMenuUpdateReqVO;
import com.r2.framework.exception.CodeException;
import com.r2.framework.util.ResultCode;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统菜单(SysMenu)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-21 10:04:10
 */
@SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
@Service
public class SysMenuServiceImpl implements SysMenuService {
    private static final Logger log = LoggerFactory.getLogger(SysMenuServiceImpl.class);
    @Resource
    private TSysMenuDao TSysMenuDao;

    @Override
    public List<String> getPermissionsByUserId(Long userId) {
        List<String> permissions = TSysMenuDao.getPermissionsByUserId(userId);
        // 写入相关联的权限
        if (permissions.contains("center:export")) {
            permissions.add("exportApproval:insert");
        }
        permissions.add("user:info");
        permissions.add("dataField:list");
        return permissions.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public Page<SysMenuListRespVO> queryByPage(SysMenuListReqVO reqVO) {
        Page<SysMenuListRespVO> page = new Page<>();
        //查询条件
        String querySql = "";
        //分页
        String limitSql = "";
        //模块名称
        if (StrUtil.isNotEmpty(reqVO.getTitle())) {
            querySql += " and sm.title like '%" + reqVO.getTitle() + "%' ";
        }
        //pid
        if (null != reqVO.getPid()) {
            querySql += " and sm.pid = " + reqVO.getPid() + " ";
        }
        //状态
        if (null != reqVO.getStatus()) {
            querySql += " and sm.status = " + reqVO.getStatus() + " ";
        }
        //菜单类型
        if (null != reqVO.getType()) {
            querySql += " and sm.type = " + reqVO.getType() + " ";
            //菜单/按钮 不进行分页
            if (reqVO.getType() == 1) {
                //分页
                limitSql = " limit " + reqVO.getOffset() + "," + reqVO.getSize() + " ";
                //列表数量
                Integer totalCount = TSysMenuDao.totalCount(querySql);
                page.setTotal(totalCount);
                page.setCurrent(reqVO.getPage());
                page.setSize(reqVO.getSize());
            }
        }
        //列表数据
        List<SysMenuListRespVO> sysMenuList = TSysMenuDao.getMenuList(querySql, limitSql);
        //递归获取子节点数据
        if (CollectionUtil.isNotEmpty(sysMenuList)) {
            loopList(sysMenuList, reqVO);
        }
        page.setRecords(sysMenuList);

        return page;
    }

    @Override
    public Boolean insert(SysMenuCreateReqVO reqVO) {
        SysMenu sysMenu = new SysMenu();
        sysMenu.setPid(Long.parseLong(reqVO.getPid().toString()));
        sysMenu.setTitle(reqVO.getTitle());
        sysMenu.setName(reqVO.getName());
        sysMenu.setType(reqVO.getType());
        sysMenu.setMenuSort(reqVO.getMenuSort());
        sysMenu.setRoute(reqVO.getRoute());
        sysMenu.setStatus(reqVO.getStatus());
        sysMenu.setCreateTime(LocalDateTime.now());
        sysMenu.setUpdateTime(LocalDateTime.now());
        //创建人 更新人
        BiUser sysUser = (BiUser) SecurityUtils.getSubject().getPrincipal();
        if (ObjectUtil.isNotEmpty(sysUser)) {
            sysMenu.setCreateBy(sysUser.getUsername());
            sysMenu.setUpdateBy(sysUser.getUsername());
        }
        TSysMenuDao.insert(sysMenu);
        return true;
    }

    @Override
    public Boolean update(SysMenuUpdateReqVO reqVO) {
        //当前数据是否存在
        SysMenu sysMenu = TSysMenuDao.selectById(reqVO.getId());
        if (ObjectUtil.isEmpty(sysMenu)) {
            throw new CodeException(ResultCode.Codes.ERROR, "数据不存在");
        }

        //更新
        UpdateWrapper<SysMenu> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SysMenu::getId, reqVO.getId());

        if (null != reqVO.getPid()) {
            updateWrapper.lambda().set(SysMenu::getPid, reqVO.getPid());
        }
        if (StrUtil.isNotEmpty(reqVO.getTitle())) {
            updateWrapper.lambda().set(SysMenu::getTitle, reqVO.getTitle());
        }
        if (StrUtil.isNotEmpty(reqVO.getName())) {
            updateWrapper.lambda().set(SysMenu::getName, reqVO.getName());
        }
        if (null != reqVO.getType()) {
            updateWrapper.lambda().set(SysMenu::getType, reqVO.getType());
        }
        if (null != reqVO.getMenuSort()) {
            updateWrapper.lambda().set(SysMenu::getMenuSort, reqVO.getMenuSort());
        }
        if (StrUtil.isNotBlank(reqVO.getRoute())) {
            updateWrapper.lambda().set(SysMenu::getRoute, reqVO.getRoute());
        }
        if (null != reqVO.getStatus()) {
            updateWrapper.lambda().set(SysMenu::getStatus, reqVO.getStatus());
        }
        updateWrapper.lambda().set(SysMenu::getUpdateTime, new Date());
        //更新人
        BiUser sysUser = (BiUser) SecurityUtils.getSubject().getPrincipal();
        if (ObjectUtil.isNotEmpty(sysUser)) {
            updateWrapper.lambda().set(SysMenu::getUpdateBy, sysUser.getUsername());
        }
        TSysMenuDao.update(null, updateWrapper);

        return true;
    }

    @Override
    public Boolean disable(Integer id, Integer status) {
        //当前数据是否存在
        SysMenu sysMenu = TSysMenuDao.selectById(id);
        if (ObjectUtil.isEmpty(sysMenu)) {
            throw new CodeException(ResultCode.Codes.ERROR, "数据不存在");
        }
        //当前节点及子节点停用
        String updateBy = "未知";
        BiUser sysUser = (BiUser) SecurityUtils.getSubject().getPrincipal();
        if (ObjectUtil.isNotEmpty(sysUser)) {
            updateBy = sysUser.getUsername();
        }
        TSysMenuDao.disable(id, status, updateBy, LocalDateTime.now());
        return true;
    }

    @Override
    public List<SysMenu>  getAllPermissions() {
        QueryWrapper<SysMenu> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysMenu :: getIsDelete, false);
        return TSysMenuDao.selectList(queryWrapper);
    }

    @Override
    public String currentUserMenuList() {
        String menuIds = "";
        BiUser sysUser = (BiUser) SecurityUtils.getSubject().getPrincipal();
        if (ObjectUtil.isNotEmpty(sysUser)) {
            //获取用户菜单权限
            List<SysMenuListRespVO> userMenuList = TSysMenuDao.getUserMenuList(sysUser.getId());
            if (CollectionUtil.isNotEmpty(userMenuList)) {
                List<Integer> menuIdsArr = userMenuList.stream().map(item -> item.getId()).collect(Collectors.toList());
                menuIds = StrUtil.join(",", menuIdsArr);
            }
        }
        return menuIds;
    }

    @Override
    public void checkUserMenuList() {
        List<SysMenuListRespVO> sysMenuListRespVOS = TSysMenuDao.selectByGroupHaving();
        if (CollectionUtil.isNotEmpty(sysMenuListRespVOS)) {
            String updateSql = "";
            for (SysMenuListRespVO sysMenu : sysMenuListRespVOS) {
                updateSql += "update t_sys_menu set permission = 'noneAuth',route = '"+ sysMenu.getRoute() + RandomUtil.randomString(4) +"' where id = " + sysMenu.getId() + ";>>>";
            }
            log.info(updateSql);
        }
    }

    /**
     * 递归获取子节点数据
     * @param sysMenuList
     */
    private void loopList(List<SysMenuListRespVO> sysMenuList, SysMenuListReqVO reqVO) {
        for (SysMenuListRespVO sysMenuListRespVO : sysMenuList) {
            //查询条件
            String loopQuerySql = "";
            loopQuerySql += " and sm.pid = " + sysMenuListRespVO.getId() + " ";
            if (null != reqVO.getStatus()) {
                loopQuerySql += " and sm.status = " + reqVO.getStatus() + " ";
            }
            List<SysMenuListRespVO> loopMenuList = TSysMenuDao.getMenuList(loopQuerySql, "");
            if (CollectionUtil.isNotEmpty(loopMenuList)) {
                loopList(loopMenuList, reqVO);
                sysMenuListRespVO.setChildren(loopMenuList);
            }
        }
    }
}

