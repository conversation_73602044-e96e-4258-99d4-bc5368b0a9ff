package com.r2.bi.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.r2.bi.entity.bi.SysMenu;
import com.r2.bi.vo.sysmenu.SysMenuCreateReqVO;
import com.r2.bi.vo.sysmenu.SysMenuListReqVO;
import com.r2.bi.vo.sysmenu.SysMenuListRespVO;
import com.r2.bi.vo.sysmenu.SysMenuUpdateReqVO;


import java.util.List;

/**
 * 系统菜单(SysMenu)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-21 10:04:10
 */
public interface SysMenuService {

    /**
     * 根据用户id获取用户权限
     * @param userId
     * @return
     */
    List<String> getPermissionsByUserId(Long userId);

    /**
     * 菜单列表
     *
     * @param reqVO 筛选条件
     * @return 查询结果
     */
    Page<SysMenuListRespVO> queryByPage(SysMenuListReqVO reqVO);

    /**
     * 新增
     *
     * @param reqVO
     * @return
     */
    Boolean insert(SysMenuCreateReqVO reqVO);

    /**
     * 更新
     *
     * @param reqVO
     * @return
     */
    Boolean update(SysMenuUpdateReqVO reqVO);

    /**
     * 停用/启用
     * @param id
     * @return
     */
    Boolean disable(Integer id, Integer status);

    /**
     * 获取所有的权限
     * @return
     */
    List<SysMenu> getAllPermissions();

    /**
     * 当前用户菜单节点列表
     * @return
     */
    String currentUserMenuList();

    /**
     * t_sys_menu route去重，权限放开
     */
    void checkUserMenuList();
}

