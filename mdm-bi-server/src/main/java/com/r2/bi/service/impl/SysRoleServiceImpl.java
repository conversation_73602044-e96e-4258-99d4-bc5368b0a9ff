package com.r2.bi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.r2.bi.dao.bi.SysRoleDao;
import com.r2.bi.dao.bi.SysRolesMenusDao;
import com.r2.bi.dao.bi.SysUserRolesDao;
import com.r2.bi.entity.bi.BiUser;
import com.r2.bi.entity.bi.SysRole;
import com.r2.bi.entity.bi.SysRolesMenus;
import com.r2.bi.entity.bi.SysUserRoles;
import com.r2.bi.service.SysRoleService;
import com.r2.bi.util.ToolUtil;
import com.r2.bi.vo.sysrole.*;
import com.r2.framework.exception.CodeException;
import com.r2.framework.util.ResultCode;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色表(SysRole)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-21 09:55:47
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleDao, SysRole> implements SysRoleService {


    @Resource
    private SysRoleDao sysRoleDao;


    @Resource
    private SysRolesMenusDao sysRolesMenusDao;

    @Resource
    private SysUserRolesDao sysUserRolesDao;
    @Override
    public List<SysRole> getByIds(List<Long> roleIds) {
        return sysRoleDao.selectBatchIds(roleIds);
    }

    @Override
    public Page<SysRoleSimpleListRespVO> simpleQueryByPage(SysRoleListReqVO reqVO) {
        Page<SysRoleSimpleListRespVO> page = new Page<>();

        //查询条件
        String querySql = "";

        //角色名称
        if (StrUtil.isNotEmpty(reqVO.getName())) {
            querySql += " and sr.name like \"%" + reqVO.getName() + "%\" ";
        }
        //分页
        String limitSql = " limit " + reqVO.getOffset() + "," + reqVO.getSize() + " ";
        //列表数量
        Integer totalCount = sysRoleDao.totalCount(querySql);
        page.setTotal(totalCount);
        page.setCurrent(reqVO.getPage());
        page.setSize(reqVO.getSize());
        //列表数据
        List<SysRoleSimpleListRespVO> dataFieldList = sysRoleDao.simpleListByCond(querySql, limitSql);
        page.setRecords(dataFieldList);

        return page;
    }

    @Override
    public Page<SysRoleListRespVO> queryByPage(SysRoleListReqVO reqVO) {
        Page<SysRoleListRespVO> page = new Page<>();

        //查询条件
        String querySql = "";

        //角色名称
        if (StrUtil.isNotEmpty(reqVO.getName())) {
            querySql += " and sr.name like \"%" + reqVO.getName() + "%\" ";
        }
        //角色状态
        if (null != reqVO.getStatus()) {
            querySql += " and sr.status = " + reqVO.getStatus() + " ";
        }
        //分页
        String limitSql = " limit " + reqVO.getOffset() + "," + reqVO.getSize() + " ";
        //列表数量
        Integer totalCount = sysRoleDao.totalCount(querySql);
        page.setTotal(totalCount);
        page.setCurrent(reqVO.getPage());
        page.setSize(reqVO.getSize());
        //列表数据
        List<SysRoleListRespVO> dataFieldList = sysRoleDao.listByCond(querySql, limitSql);
        if (CollectionUtil.isNotEmpty(dataFieldList)) {
            for (SysRoleListRespVO sysRoleListRespVO : dataFieldList) {
                //获取权限数据
                UpdateWrapper<SysRolesMenus> queryWrapper = new UpdateWrapper<>();
                queryWrapper.lambda().eq(SysRolesMenus::getRoleId, sysRoleListRespVO.getId());
                List<SysRolesMenus> sysRolesMenus = sysRolesMenusDao.selectList(queryWrapper);
                if (CollectionUtil.isNotEmpty(sysRolesMenus)) {
                    List<Long> menuIds = sysRolesMenus.stream().map(item -> item.getMenuId()).collect(Collectors.toList());
                    sysRoleListRespVO.setAuthList(menuIds);
                } else {
                    sysRoleListRespVO.setAuthList(new ArrayList<>());
                }
            }
        }
        page.setRecords(dataFieldList);

        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(SysRoleCreateReqVO reqVO) {
        //编码校验/名称校验
        checkData(reqVO.getCode(), reqVO.getName(), 0);

        //新增
        SysRole sysRole = new SysRole();
        sysRole.setCode(reqVO.getCode());
        sysRole.setName(reqVO.getName());
        sysRole.setDataScope(reqVO.getDataScope());
        sysRole.setDescription(reqVO.getDescription());
        sysRole.setCreateTime(LocalDateTime.now());
        sysRole.setUpdateTime(LocalDateTime.now());
        BiUser sysUser = (BiUser) SecurityUtils.getSubject().getPrincipal();
        if (ObjectUtil.isNotEmpty(sysUser)) {
            sysRole.setCreateBy(sysUser.getUsername());
            sysRole.setUpdateBy(sysUser.getUsername());
        }
        sysRoleDao.insert(sysRole);

        //角色权限菜单映射
        if (CollectionUtil.isNotEmpty(reqVO.getAuthList())) {
            for (Integer menuId : reqVO.getAuthList()) {
                SysRolesMenus sysRolesMenus = new SysRolesMenus();
                sysRolesMenus.setRoleId(sysRole.getId());
                sysRolesMenus.setMenuId(menuId.longValue());
                sysRolesMenusDao.insert(sysRolesMenus);
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(SysRoleUpdateReqVO reqVO) {
        //编码校验/名称校验
        checkData(reqVO.getCode(), reqVO.getName(), reqVO.getId());

        //更新
        UpdateWrapper<SysRole> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SysRole::getId, reqVO.getId());

        if (StrUtil.isNotEmpty(reqVO.getCode())) {
            updateWrapper.lambda().set(SysRole::getCode, reqVO.getCode());
        }
        if (StrUtil.isNotEmpty(reqVO.getName())) {
            updateWrapper.lambda().set(SysRole::getName, reqVO.getName());
        }
        if (StrUtil.isNotEmpty(reqVO.getDataScope())) {
            updateWrapper.lambda().set(SysRole::getDataScope, reqVO.getDataScope());
        }
        if (StrUtil.isNotEmpty(reqVO.getDescription())) {
            updateWrapper.lambda().set(SysRole::getDescription, reqVO.getDescription());
        }
        updateWrapper.lambda().set(SysRole::getUpdateTime, new Date());
        //更新人
        BiUser sysUser = (BiUser) SecurityUtils.getSubject().getPrincipal();
        if (ObjectUtil.isNotEmpty(sysUser)) {
            updateWrapper.lambda().set(SysRole::getUpdateBy, sysUser.getUsername());
        }
        sysRoleDao.update(null, updateWrapper);

        //角色权限
        if (CollectionUtil.isNotEmpty(reqVO.getAuthList())) {
            //删除现有权限
            UpdateWrapper<SysRolesMenus> deleteWrapper = new UpdateWrapper<>();
            deleteWrapper.lambda().eq(SysRolesMenus::getRoleId, reqVO.getId().longValue());
            sysRolesMenusDao.delete(deleteWrapper);
            //新增权限
            for (Integer menuId : reqVO.getAuthList()) {
                SysRolesMenus sysRolesMenus = new SysRolesMenus();
                sysRolesMenus.setRoleId(reqVO.getId().longValue());
                sysRolesMenus.setMenuId(menuId.longValue());
                sysRolesMenusDao.insert(sysRolesMenus);
            }
        }
        return true;
    }

    @Override
    public Boolean disable(Integer id, Integer status) {
        //更新
        UpdateWrapper<SysRole> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SysRole::getId, id);
        updateWrapper.lambda().set(SysRole::getStatus, status);
        //更新人
        BiUser sysUser = (BiUser) SecurityUtils.getSubject().getPrincipal();
        if (ObjectUtil.isNotEmpty(sysUser)) {
            updateWrapper.lambda().set(SysRole::getUpdateBy, sysUser.getUsername());
        }
        sysRoleDao.update(null, updateWrapper);
        return true;
    }

    @Override
    public Boolean delete(Integer id) {
        //校验角色是否被引用
        QueryWrapper<SysUserRoles> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysUserRoles::getRoleId, id);
        Integer rolesCount = sysUserRolesDao.selectCount(queryWrapper);
        if (rolesCount > 0) {
            throw new CodeException(ResultCode.Codes.ERROR, "已被引用的角色不可以删除");
        }

        UpdateWrapper<SysRole> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(SysRole::getId, id);
        updateWrapper.lambda().set(SysRole::getIsDelete, true);
        updateWrapper.lambda().set(SysRole::getDeleteFlag, ToolUtil.getUUId());
        //更新人
        BiUser sysUser = (BiUser) SecurityUtils.getSubject().getPrincipal();
        if (ObjectUtil.isNotEmpty(sysUser)) {
            updateWrapper.lambda().set(SysRole::getUpdateBy, sysUser.getUsername());
        }
        sysRoleDao.update(null, updateWrapper);
        return true;
    }
    /**
     * 数据校验
     * @param
     */
    private void checkData(String code, String name, Integer id) {
        //编码校验
        UpdateWrapper<SysRole> checkCodeWrapper = new UpdateWrapper<>();
        checkCodeWrapper.lambda().eq(SysRole::getCode, code);
        checkCodeWrapper.lambda().eq(SysRole::getIsDelete, false);
        checkCodeWrapper.lambda().eq(SysRole::getDeleteFlag, "");
        if (null != id && id > 0) {
            checkCodeWrapper.lambda().ne(SysRole::getId, id);
        }

        Integer checkCodeNum = sysRoleDao.selectCount(checkCodeWrapper);
        if (checkCodeNum > 0) {
            throw new CodeException(ResultCode.Codes.ERROR, "该角色编码已存在, 请重新输入");
        }
        //名称校验
        UpdateWrapper<SysRole> checkNameWrapper = new UpdateWrapper<>();
        checkNameWrapper.lambda().eq(SysRole::getName, name);
        checkNameWrapper.lambda().eq(SysRole::getIsDelete, false);
        checkNameWrapper.lambda().eq(SysRole::getDeleteFlag, "");
        if (null != id && id > 0) {
            checkNameWrapper.lambda().ne(SysRole::getId, id);
        }

        Integer checkNameNum = sysRoleDao.selectCount(checkNameWrapper);
        if (checkNameNum > 0) {
            throw new CodeException(ResultCode.Codes.ERROR, "该角色名称已存在, 请重新输入");
        }
    }
}

