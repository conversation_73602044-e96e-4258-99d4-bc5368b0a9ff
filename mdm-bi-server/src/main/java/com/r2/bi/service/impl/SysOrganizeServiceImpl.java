package com.r2.bi.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.r2.bi.dao.bi.SysOrganizeDao;
import com.r2.bi.entity.bi.SysOrganize;
import com.r2.bi.service.SysOrganizeService;
import com.r2.bi.util.StringUtils;
import com.r2.bi.vo.common.TreeSelectVO;
import com.r2.bi.vo.sysorg.SysOrgListReqVO;
import com.r2.bi.vo.sysorg.SysOrgTreeNodeRespVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组织部门(SysOrganize)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-02 10:41:31
 */
@Service
public class SysOrganizeServiceImpl extends ServiceImpl<SysOrganizeDao, SysOrganize> implements SysOrganizeService {
    @Resource
    private SysOrganizeDao sysOrganizeDao;

    @Override
    public List<String> getOrgCodeList(String orgCode) {
        List<String> list = new ArrayList<>();
        SysOrgListReqVO reqVO = new SysOrgListReqVO();
        reqVO.setStatus(1);
        List<SysOrgTreeNodeRespVO> orgs = selectTreeOrgList(reqVO);
        List<TreeSelectVO> resp = buildOrgTree(orgs).stream().map(TreeSelectVO::new).collect(Collectors.toList());
        List<TreeSelectVO> treeSelectList = new ArrayList<>();
        recursionTreeFn(resp, treeSelectList,  orgCode);
        recursionCodeFnTo(treeSelectList, list);
        return list;
    }


    /**
     * 查询组织管理数据,返回树形结构需要的实体类
     *
     * @param org 组织信息
     * @return 组织信息集合
     */
    @Override
    public List<SysOrgTreeNodeRespVO> selectTreeOrgList(SysOrgListReqVO org)
    {
        return sysOrganizeDao.selectTreeOrgList(org);
    }

    /**
     * 构建前端需要的树结构
     *
     * @param orgs 组织列表
     * @return 树结构列表
     */
    @Override
    public List<SysOrgTreeNodeRespVO> buildOrgTree(List<SysOrgTreeNodeRespVO> orgs) {
        List<SysOrgTreeNodeRespVO> returnList = new ArrayList<SysOrgTreeNodeRespVO>();
        List<String> tempList = new ArrayList<String>();
        for (SysOrgTreeNodeRespVO org : orgs) {
            tempList.add(org.getOrganizeCode());
        }
        for (Iterator<SysOrgTreeNodeRespVO> iterator = orgs.iterator(); iterator.hasNext();) {
            SysOrgTreeNodeRespVO org = (SysOrgTreeNodeRespVO) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(org.getParentCode())) {
                recursionFn(orgs, org);
                returnList.add(org);
            }
        }
        if (returnList.isEmpty()) {
            returnList = orgs;
        }
        return returnList;
    }
    @Override
    public List<SysOrganize> getByOrgCodes(List<String> orgCodes) {
        QueryWrapper<SysOrganize> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(SysOrganize::getOrganizeCode, orgCodes)
                .eq(SysOrganize::getIsDelete, false);
        return sysOrganizeDao.selectList(queryWrapper);

    }
    /**
     * 查询组织管理数据
     *
     * @param org 组织信息
     * @return 组织信息集合
     */
    @Override
    public List<SysOrganize> selectOrgList(SysOrgListReqVO org)
    {
        return sysOrganizeDao.selectOrgList(org);
    }

        /**
         * 递归列表
         */
    private void recursionFn(List<SysOrgTreeNodeRespVO> list, SysOrgTreeNodeRespVO t) {
        // 得到子节点列表
        List<SysOrgTreeNodeRespVO> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysOrgTreeNodeRespVO tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }
    /**
     * 递归选择组织树
     */
    private void recursionTreeFn(List<TreeSelectVO> list, List<TreeSelectVO> treeSelectList, String parentCode) {
        for (TreeSelectVO treeVO: list) {
            if (treeVO.getKey().equals(parentCode)) {
                treeSelectList.add(treeVO);
                return;
            }
            if (!treeVO.getChildren().isEmpty()) {
                recursionTreeFn(treeVO.getChildren(), treeSelectList, parentCode);
            }
        }
    }


    /**
     * 递归获取所有code
     */
    private void recursionCodeFnTo(List<TreeSelectVO> list, List<String> strList) {
        for (TreeSelectVO treeVO: list) {
            strList.add(treeVO.getKey());
        }
    }

    /**
     * 递归获取所有code
     */
    private void recursionCodeFn(List<TreeSelectVO> list, List<String> strList) {
        for (TreeSelectVO treeVO: list) {
            strList.add(treeVO.getKey());
            if (!treeVO.getChildren().isEmpty()) {
                recursionCodeFn(treeVO.getChildren(), strList);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysOrgTreeNodeRespVO> getChildList(List<SysOrgTreeNodeRespVO> list, SysOrgTreeNodeRespVO t) {
        List<SysOrgTreeNodeRespVO> tlist = new ArrayList<SysOrgTreeNodeRespVO>();
        Iterator<SysOrgTreeNodeRespVO> it = list.iterator();
        while (it.hasNext()) {
            SysOrgTreeNodeRespVO n = (SysOrgTreeNodeRespVO) it.next();
            if (StringUtils.isNotNull(n.getParentCode()) && n.getParentCode().equals(t.getOrganizeCode())) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysOrgTreeNodeRespVO> list, SysOrgTreeNodeRespVO t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }
}
