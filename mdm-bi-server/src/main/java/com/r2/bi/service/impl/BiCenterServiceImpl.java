package com.r2.bi.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.r2.bi.bo.BiStartSpeedResponseDTO;
import com.r2.bi.bo.ContractCountDTO;
import com.r2.bi.config.shiro.NoPwdToken;
import com.r2.bi.dao.bi.BiSysUserDao;
import com.r2.bi.dao.cs.*;
import com.r2.bi.dao.dc.AssociationTableMapper;
import com.r2.bi.dao.dc.CenterDao;
import com.r2.bi.dao.dc.CustomTableDataMapper;
import com.r2.bi.dao.dc.SysAccessSystemDao;
import com.r2.bi.entity.bi.BiUser;
import com.r2.bi.entity.cs.*;
import com.r2.bi.entity.dc.AssociationTable;
import com.r2.bi.entity.dc.CustomTableData;
import com.r2.bi.entity.dc.SysAccessSystem;
import com.r2.bi.enums.AuthLoginEnum;
import com.r2.bi.service.BiCenterService;
import com.r2.bi.service.SysMdmService;
import com.r2.bi.service.SysUserService;
import com.r2.bi.util.EncryptUtil;
import com.r2.bi.util.Sm2Util;
import com.r2.bi.util.StringUtils;
import com.r2.bi.vo.BiCenterRepVO;
import com.r2.bi.vo.BiCenterRespVO;
import com.r2.bi.vo.BiStartSpeedResponseVO;
import com.r2.bi.vo.bi.*;
import com.r2.bi.vo.bi.center.*;
import com.r2.bi.vo.bi.centermember.BiCenterMemberReqVO;
import com.r2.bi.vo.bi.centermember.BiCenterMemberRespVO;
import com.r2.bi.vo.centermeeting.CalendarDetailRespVO;
import com.r2.bi.vo.centermeeting.CenterMeetingQueryStrRespVO;
import com.r2.bi.vo.centermeeting.OverviewVO;
import com.r2.framework.exception.CodeException;
import com.r2.framework.util.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
@Service(value = "infoCenterService")
public class BiCenterServiceImpl extends ServiceImpl<BiCenterDao, Center> implements BiCenterService {

    @Resource
    private BiSysUserDao biSysUserDao;

    @Resource
    private BiCenterDao biCenterDao;

    @Resource
    private CustomTableDataMapper customTableDataMapper;

    @Resource
    private InitSmoDataAreaRelateMapper initSmoDataAreaRelateMapper;

    @Resource
    private DataAreaRelateReportDao dataAreaRelateReportDao;

    @Resource
    private BiCenterMemberDao biCenterMemberDao;

    @Resource
    private CenterDao centerDao;

    @Resource
    private AssociationTableMapper associationTableMapper;

    @Resource
    private TCenterStartUpMapper tCenterStartUpMapper;

    @Resource
    private TCenterEthicsMapper tCenterEthicsMapper;

    @Resource
    private CenterProjectApprovalDao centerProjectApprovalDao;

    @Resource
    private CenterMeetingDao centerMeetingDao;

    @Resource
    private SysAccessSystemDao sysAccessSystemDao;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private CenterMemberContactTypeRoleDao centerMemberContactTypeRoleDao;

    @Resource
    private SysMdmService sysMdmService;

    /**
     * sm2密码解密秘钥
     */
    @Value("${password.privateKey}")
    private String privateKey;


    @Override
    public Object updateStatus(SysUserAuthorityReqVO vo) {
        UpdateWrapper<BiUser> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(BiUser::getDataPermission, vo.getDataPermission())
                .set(BiUser::getUpdateTime, new Date())
                .eq(BiUser::getId, vo.getId());
        return biSysUserDao.update(null, updateWrapper);
    }

    @Override
    public Object centerCountList(AreaListReqVO areaListVO) {
        return getAllSummaryVO(areaListVO);
    }

    public SummaryVO getAllSummaryVO(AreaListReqVO areaListVO) {
        // 查询未删除的中心
        QueryWrapper<Center> centerQueryWrapper = new QueryWrapper<>();
        centerQueryWrapper.lambda().eq(Center::getIsDelete, 0);
        if (StringUtils.isNotBlank(areaListVO.getProvince())) {
            centerQueryWrapper.lambda().eq(Center::getProvince, areaListVO.getProvince());
        }
        if (StringUtils.isNotBlank(areaListVO.getCity())) {
            centerQueryWrapper.lambda().eq(Center::getCity, areaListVO.getCity());
        }

        List<Center> centers = biCenterDao.selectList(centerQueryWrapper);

        // 查询 CustomTableData
        QueryWrapper<CustomTableData> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CustomTableData::getStatus, 0);
        List<CustomTableData> customTableData = customTableDataMapper.selectList(queryWrapper);

        // 构建一个中心名称到启动用时的映射
        Map<String, List<Integer>> centerToStartupTimes = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");

        for (CustomTableData customTableDatum : customTableData) {
            String content = customTableDatum.getContent();
            Map<String, Map<String, Object>> transformedData = JSON.parseObject(content, new TypeReference<Map<String, Map<String, Object>>>() {
            });
            Map<String, String> keyValueMap = transformedData.entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> String.valueOf(entry.getValue().get("value"))));

            String centerName = keyValueMap.get("中心名称");
            String startupTimeStr = keyValueMap.get("实际启动日期");
            String endUpTimeStr = keyValueMap.get("立项递交时间");

            if (centerName != null && startupTimeStr != null && endUpTimeStr != null) {
                try {
                    LocalDate startupDate = LocalDate.parse(startupTimeStr, formatter);
                    LocalDate endDate = LocalDate.parse(endUpTimeStr, formatter);

                    long daysBetween = ChronoUnit.DAYS.between(endDate, startupDate);
                    if (daysBetween >= 0) {
                        centerToStartupTimes.computeIfAbsent(centerName, k -> new ArrayList<>()).add((int) daysBetween);
                    }
                } catch (Exception e) {
                    // 忽略无法解析的启动用时
                }
            }
        }

        // 根据省份进行分组
        Map<String, List<Center>> centersGroupedByProvince = centers.stream()
                .collect(Collectors.groupingBy(Center::getProvince));

        // 查询 InitSmoDataAreaRelate 数据
        QueryWrapper<InitSmoDataAreaRelate> initSmoDataAreaRelateQueryWrapper = new QueryWrapper<>();
        List<InitSmoDataAreaRelate> initSmoDataAreaRelates = initSmoDataAreaRelateMapper.selectList(initSmoDataAreaRelateQueryWrapper);

        // 根据区域名进行分组，并确保每个区域对应的省份是唯一的集合
        Map<String, Set<String>> groupedByAreaWithUniqueProvinces = initSmoDataAreaRelates.stream()
                .collect(Collectors.groupingBy(
                        InitSmoDataAreaRelate::getAreaName,
                        Collectors.mapping(InitSmoDataAreaRelate::getProvince, Collectors.toSet())
                ));

        // 如果前端提供了区域，则只保留与该区域相关的省份
        if (StringUtils.isNotBlank(areaListVO.getZoneName())) {
            groupedByAreaWithUniqueProvinces = groupedByAreaWithUniqueProvinces.entrySet().stream()
                    .filter(entry -> entry.getKey().equals(areaListVO.getZoneName()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        // 查询未删除且项目独立的 CenterMember
        List<CenterMember> centerMembers = biCenterMemberDao.selectList(new QueryWrapper<CenterMember>()
                .eq("proj_alone", "是")
                .eq("is_delete", 0));

        // 查询所有的 DataAreaRelateReport
        QueryWrapper<DataAreaRelateReport> reportQueryWrapper = new QueryWrapper<>();
        List<DataAreaRelateReport> dataAreaRelateReports = dataAreaRelateReportDao.selectList(reportQueryWrapper);

        // 初始化 SummaryVO
        SummaryVO summaryVO = new SummaryVO();
        List<AreaRespVO> areas = new ArrayList<>();

        // 外层统计数据
        int totalCenters = 0;
        int totalSmoNumber = 0;
        int totalPiNumber = 0; // 总的 piNumber
        int totalUpdateNumber = 0;
        int totalUpdateNum = 0;
        double totalIntegritySum = 0.0;
        int areaCount = 0;

        // 遍历按区域分组的数据
        for (Map.Entry<String, Set<String>> areaEntry : groupedByAreaWithUniqueProvinces.entrySet()) {
            String areaName = areaEntry.getKey();
            Set<String> provinces = areaEntry.getValue();

            // 创建 AreaRespVO 对象
            AreaRespVO areaResp = new AreaRespVO();
            areaResp.setAreaName(areaName);
            List<AreaRespVO.ProvinceVO> provincesVO = new ArrayList<>();

            int areaCenterNum = 0;
            int areaSmoNumber = 0;
            int areaUpdateNumber = 0;
            int areaUpdateNum = 0;
            int areaPiNumber = 0;
            double areaIntegritySum = 0.0;
            int areaReportCount = 0;

            // 从 centers 中获取属于该区域的中心名称
            Set<String> centerNamesInArea = centers.stream()
                    .filter(center -> provinces.contains(center.getProvince()))
                    .map(Center::getCenterName)
                    .collect(Collectors.toSet());

            // 计算启动用时统计信息
            List<Integer> areaStartupTimes = centerNamesInArea.stream()
                    .filter(centerToStartupTimes::containsKey) // 过滤出有启动用时记录的中心
                    .flatMap(centerName -> centerToStartupTimes.get(centerName).stream())
                    .collect(Collectors.toList());

            if (!areaStartupTimes.isEmpty()) {
                int minStartupTime = areaStartupTimes.stream()
                        .filter(time -> time > 0) // 排除启动用时为0的情况
                        .min(Integer::compare)
                        .orElse(0);
                double avgStartupTime = areaStartupTimes.stream()
                        .mapToInt(Integer::intValue)
                        .average()
                        .orElse(0.0);
                areaResp.setFastestTime(String.valueOf(minStartupTime));
                areaResp.setAverageTime(String.valueOf((int) Math.ceil(avgStartupTime)));
            } else {
                areaResp.setFastestTime("0");
                areaResp.setAverageTime("0");
            }

            for (String prov : provinces) {
                if (StringUtils.isNotBlank(areaListVO.getProvince()) && !prov.equals(areaListVO.getProvince())) {
                    continue; // 如果前端指定了省份并且当前省份不匹配，则跳过
                }

                List<Center> centersInProvince = centersGroupedByProvince.getOrDefault(prov, Collections.emptyList());

                // 创建 ProvinceVO 对象
                AreaRespVO.ProvinceVO provinceVO = new AreaRespVO.ProvinceVO();
                provinceVO.setProvince(prov);
                provinceVO.setCenterNum(centersInProvince.size());

                // 统计有 SMO 的中心数量
                long smoCentersCount = centersInProvince.stream()
                        .filter(center -> "是".equals(center.getHaveSmo()))
                        .count();
                provinceVO.setSmoNumber((int) smoCentersCount);

                // 统计当月被更新的中心数量
                LocalDate now = LocalDate.now();
                Date startOfMonth = Date.from(now.withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
                Date endOfMonth = Date.from(now.withDayOfMonth(now.lengthOfMonth()).atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());

                // 统计当月被更新的中心数量
                long updatedCentersCount = centersInProvince.stream()
                        .filter(center -> center.getDatemodified() != null &&
                                !center.getDatemodified().before(startOfMonth) &&
                                !center.getDatemodified().after(endOfMonth))
                        .count();

                provinceVO.setUpdateNumber((int) updatedCentersCount);

                long updatedCentersNum = centersInProvince.stream()
                        .filter(center -> center.getUpdator() != null && !center.getUpdator().isEmpty())
                        .count();
                //覆盖中心数
                provinceVO.setCenterCoverNum((int) updatedCentersNum);
                // 计算覆盖率
                if (!centersInProvince.isEmpty()) {
                    double coverage = (double) updatedCentersNum / centersInProvince.size() * 100;
                    provinceVO.setCoverageRate(String.format("%.2f%%", coverage));
                }

                // 获取该省份所有中心的 ID
                List<Integer> provinceCenterIds = centersInProvince.stream()
                        .map(Center::getId)
                        .collect(Collectors.toList());

                // 统计 piNumber
                List<CenterMember> piMembersInProvince = centerMembers.stream()
                        .filter(member -> provinceCenterIds.contains(member.getCenterId().intValue()))
                        .collect(Collectors.toList());
                int piNumberInProvince = piMembersInProvince.size();
                provinceVO.setPiNumber(piNumberInProvince);

                // 计算该省份的信息完成度
                List<DataAreaRelateReport> reportsForProvince = dataAreaRelateReports.stream()
                        .filter(report -> centersInProvince.stream()
                                .map(Center::getCenterName)
                                .collect(Collectors.toList())
                                .contains(report.getCenterName()))
                        .collect(Collectors.toList());

                if (!reportsForProvince.isEmpty()) {
                    double integritySum = reportsForProvince.stream()
                            .mapToDouble(report -> {
                                String integrityStr = report.getCenterIntegrityPlus();
                                try {
                                    return Double.parseDouble(integrityStr.replace("%", "").trim());
                                } catch (NumberFormatException e) {
                                    return 0.0;
                                }
                            }).sum();
                    double averageIntegrity = integritySum / reportsForProvince.size();
                    provinceVO.setInformationCompletion(String.format("%.2f%%", averageIntegrity));
                    areaIntegritySum += integritySum;
                    areaReportCount += reportsForProvince.size();
                } else {
                    provinceVO.setInformationCompletion("0.00%");
                }

                // 计算省份的最快启动时长和平均启动时长
                List<Integer> provinceStartupTimes = centersInProvince.stream()
                        .filter(center -> centerToStartupTimes.containsKey(center.getCenterName()))
                        .flatMap(center -> centerToStartupTimes.getOrDefault(center.getCenterName(), Collections.emptyList()).stream())
                        .collect(Collectors.toList());
                if (!provinceStartupTimes.isEmpty()) {
                    int minStartupTime = provinceStartupTimes.stream()
                            .filter(time -> time > 0) // 排除启动用时为0的情况
                            .min(Integer::compare)
                            .orElse(0);
                    double avgStartupTime = provinceStartupTimes.stream()
                            .mapToInt(Integer::intValue)
                            .average()
                            .orElse(0.0);
                    provinceVO.setFastestTime(String.valueOf(minStartupTime));
                    provinceVO.setAverageTime(String.valueOf((int) Math.ceil(avgStartupTime)));
                } else {
                    provinceVO.setFastestTime("0");
                    provinceVO.setAverageTime("0");
                }
                // 过滤掉直辖市作为省份的情况
                Set<String> municipalities = new HashSet<>(Arrays.asList("北京市", "天津市", "上海市", "重庆市"));
                Map<String, List<Center>> centersGroupedByCity = centersInProvince.stream()
                        .filter(center -> center.getCity() != null && !center.getCity().trim().isEmpty())
                        .filter(center -> !municipalities.contains(center.getProvince()))
                        .collect(Collectors.groupingBy(Center::getCity));

                // 创建城市列表
                List<AreaRespVO.ProvinceVO.CityVO> cities = new ArrayList<>();
                for (Map.Entry<String, List<Center>> cityEntry : centersGroupedByCity.entrySet()) {
                    String cityName = cityEntry.getKey();
                    if (StringUtils.isNotBlank(areaListVO.getCity()) && !cityName.equals(areaListVO.getCity())) {
                        continue; // 如果前端指定了城市并且当前城市不匹配，则跳过
                    }

                    List<Center> centersInCity = cityEntry.getValue();

                    // 创建 CityVO 对象
                    AreaRespVO.ProvinceVO.CityVO cityVO = new AreaRespVO.ProvinceVO.CityVO();
                    cityVO.setCity(cityName);
                    cityVO.setCenterNum(centersInCity.size());

                    // 统计有 SMO 的中心数量
                    long smoCentersCountInCity = centersInCity.stream()
                            .filter(center -> "是".equals(center.getHaveSmo()))
                            .count();
                    cityVO.setSmoNumber((int) smoCentersCountInCity);

                    // 统计当月被更新的中心数量
                    long updatedCentersCountInCity = centersInCity.stream()
                            .filter(center -> center.getDatemodified() != null &&
                                    !center.getDatemodified().before(startOfMonth) &&
                                    !center.getDatemodified().after(endOfMonth))
                            .count();
                    // 设置城市VO中的更新数量
                    cityVO.setUpdateNumber((int) updatedCentersCountInCity);

                    long updatedCentersCityNum = centersInCity.stream()
                            .filter(center -> center.getUpdator() != null)
                            .count();

                    cityVO.setCenterCoverNum((int) updatedCentersCityNum);
                    // 计算覆盖率
                    if (!centersInCity.isEmpty()) {
                        double coverage = (double) updatedCentersCityNum / centersInCity.size() * 100;
                        cityVO.setCoverageRate(String.format("%.2f%%", coverage));
                    }

                    // 获取该城市所有中心的 ID
                    List<Integer> centerIdsInCity = centersInCity.stream()
                            .map(Center::getId)
                            .collect(Collectors.toList());

                    // 统计 piNumber
                    List<CenterMember> piMembersInCity = centerMembers.stream()
                            .filter(member -> centerIdsInCity.contains(member.getCenterId().intValue()))
                            .collect(Collectors.toList());
                    int piNumberInCity = piMembersInCity.size();
                    cityVO.setPiNumber(piNumberInCity);

                    // 计算该城市的信息完成度
                    List<DataAreaRelateReport> reportsForCity = dataAreaRelateReports.stream()
                            .filter(report -> centersInCity.stream()
                                    .map(Center::getCenterName)
                                    .collect(Collectors.toList())
                                    .contains(report.getCenterName()))
                            .collect(Collectors.toList());

                    if (!reportsForCity.isEmpty()) {
                        double integritySum = reportsForCity.stream()
                                .mapToDouble(report -> {
                                    String integrityStr = report.getCenterIntegrityPlus();
                                    try {
                                        return Double.parseDouble(integrityStr.replace("%", "").trim());
                                    } catch (NumberFormatException e) {
                                        return 0.0;
                                    }
                                }).sum();
                        double averageIntegrity = integritySum / reportsForCity.size();
                        cityVO.setInformationCompletion(String.format("%.2f%%", averageIntegrity));
                    } else {
                        cityVO.setInformationCompletion("0.00%");
                    }

                    // 计算城市的最快启动时长和平均启动时长
                    List<Integer> cityStartupTimes = centersInCity.stream()
                            .filter(center -> centerToStartupTimes.containsKey(center.getCenterName()))
                            .flatMap(center -> centerToStartupTimes.getOrDefault(center.getCenterName(), Collections.emptyList()).stream())
                            .collect(Collectors.toList());
                    if (!cityStartupTimes.isEmpty()) {
                        int minStartupTime = cityStartupTimes.stream()
                                .filter(time -> time > 0) // 排除启动用时为0的情况
                                .min(Integer::compare)
                                .orElse(0);
                        double avgStartupTime = cityStartupTimes.stream()
                                .mapToInt(Integer::intValue)
                                .average()
                                .orElse(0.0);
                        cityVO.setFastestTime(String.valueOf(minStartupTime));
                        cityVO.setAverageTime(String.valueOf((int) Math.ceil(avgStartupTime)));
                    } else {
                        cityVO.setFastestTime("0");
                        cityVO.setAverageTime("0");
                    }

                    // 添加到城市列表
                    cities.add(cityVO);
                }

                // 设置城市列表到 ProvinceVO
                provinceVO.setCities(cities);

                // 添加到省份列表
                provincesVO.add(provinceVO);

                // 累加区域级统计数据
                areaCenterNum += centersInProvince.size();
                areaSmoNumber += smoCentersCount;
                areaUpdateNumber += updatedCentersCount;
                areaUpdateNum += updatedCentersNum;
                areaPiNumber += piNumberInProvince; // 累加 piNumber
            }

            // 计算区域的信息完成度
            if (areaReportCount > 0) {
                double areaAverageIntegrity = areaIntegritySum / areaReportCount;
                areaResp.setInformationCompletion(String.format("%.2f%%", areaAverageIntegrity));
                totalIntegritySum += areaIntegritySum;
                areaCount += areaReportCount;
            } else {
                areaResp.setInformationCompletion("0.00%");
            }

            // 设置区域级统计数据
            areaResp.setCenterNum(areaCenterNum);
            areaResp.setSmoNumber(areaSmoNumber);
            areaResp.setUpdateNumber(areaUpdateNumber);
            areaResp.setUpdateNum(areaUpdateNum);
            areaResp.setCenterCoverNum(areaUpdateNum);
            areaResp.setPiNumber(areaPiNumber);

            // 计算区域覆盖率
            if (areaCenterNum > 0) {
                double areaCoverage = (double) areaUpdateNum / areaCenterNum * 100;
                areaResp.setCoverageRate(String.format("%.2f%%", areaCoverage));
            } else {
                areaResp.setCoverageRate("0.00%");
            }

            // 设置省份列表到 AreaRespVO
            areaResp.setProvinces(provincesVO);

            // 添加到 areas 列表
            areas.add(areaResp);

            // 累加外层统计数据
            totalCenters += areaCenterNum;
            totalSmoNumber += areaSmoNumber;
            totalUpdateNumber += areaUpdateNumber;
            totalUpdateNum += areaUpdateNum;
            totalPiNumber += areaPiNumber; // 累加 piNumber
        }

        // 计算总体覆盖率
        if (totalCenters > 0) {
            double overallCoverage = (double) totalUpdateNum / totalCenters * 100;
            summaryVO.setCoverageRate(String.format("%.2f%%", overallCoverage));
        } else {
            summaryVO.setCoverageRate("0.00%");
        }

        // 计算总体的信息完成度
        if (areaCount > 0) {
            double overallIntegrity = totalIntegritySum / areaCount;
            summaryVO.setInformationCompletion(String.format("%.2f%%", overallIntegrity));
        } else {
            summaryVO.setInformationCompletion("0.00%");
        }

        // 设置外层统计数据
        summaryVO.setTotalCenters(totalCenters);
        summaryVO.setTotalSmoNumber(totalSmoNumber);
        summaryVO.setTotalPiNumber(totalPiNumber);
        summaryVO.setTotalUpdateNumber(totalUpdateNumber);
        summaryVO.setTotalCenterCoverNum(totalUpdateNum);

        // 计算总体的最快启动时长和平均启动时长
        List<Integer> allAreaFastestTimes = areas.stream()
                .map(AreaRespVO::getFastestTime)
                .map(Integer::valueOf)
                .filter(time -> time > 0) // 过滤掉无效值（小于等于 0 的时间）
                .collect(Collectors.toList());

        List<Integer> allAreaAverageTimes = areas.stream()
                .map(AreaRespVO::getAverageTime)
                .map(Integer::valueOf)
                .collect(Collectors.toList());

        // 计算最快启动时长
        if (!allAreaFastestTimes.isEmpty()) {
            int overallFastestTime = allAreaFastestTimes.stream()
                    .min(Integer::compare)
                    .orElse(0); // 获取最小值
            summaryVO.setFastestTime(String.valueOf(overallFastestTime));
        } else {
            summaryVO.setFastestTime("0");
        }

        // 计算平均启动时长
        if (!allAreaAverageTimes.isEmpty()) {
            double overallAverageTime = allAreaAverageTimes.stream()
                    .mapToInt(Integer::intValue)
                    .average()
                    .orElse(0.0); // 计算平均值
            summaryVO.setAverageTime(String.valueOf((int) Math.ceil(overallAverageTime)));
        } else {
            summaryVO.setAverageTime("0");
        }

        // 设置 areas 到 SummaryVO
        summaryVO.setRegions(areas);

        return summaryVO;
    }

    @Override
    public List<RegionListVO.RegionVO> getAggregatedDataAsArray() {

        QueryWrapper<Center> objectQueryWrapper1 = new QueryWrapper<>();
        objectQueryWrapper1.lambda().eq(Center::getIsDelete, 0);
        List<Center> centers = biCenterDao.selectList(objectQueryWrapper1);

        // 根据省份进行分组
        Map<String, List<Center>> centersGroupedByProvince = centers.stream()
                .collect(Collectors.groupingBy(Center::getProvince));

        QueryWrapper<InitSmoDataAreaRelate> objectQueryWrapper = new QueryWrapper<>();
        List<InitSmoDataAreaRelate> initSmoDataAreaRelates =
                initSmoDataAreaRelateMapper.selectList(objectQueryWrapper);

        // 使用 Stream API 根据省份进行分组
        Map<String, Set<String>> groupedByProvinceWithUniqueAreas = initSmoDataAreaRelates.stream()
                .collect(Collectors.groupingBy(
                        InitSmoDataAreaRelate::getAreaName,
                        Collectors.mapping(InitSmoDataAreaRelate::getProvince, Collectors.toSet())
                ));

        // 定义直辖市集合
        Set<String> municipalities = new HashSet<>(Arrays.asList("北京市", "天津市", "上海市", "重庆市"));

        // 构建结果
        List<RegionListVO.RegionVO> regions = new ArrayList<>();

        for (Map.Entry<String, Set<String>> entry : groupedByProvinceWithUniqueAreas.entrySet()) {
            String areaName = entry.getKey();
            RegionListVO.RegionVO regionVO = new RegionListVO.RegionVO();
            regionVO.setRegionName(areaName);
            List<RegionListVO.DistrictVO> districtVOs = new ArrayList<>();

            for (String province : entry.getValue()) {
                RegionListVO.DistrictVO districtVO = new RegionListVO.DistrictVO();
                districtVO.setDistrictName(province);


                if (municipalities.contains(province)) {
                    // 直接添加直辖市下的所有中心
                    List<Center> municipalityCenters = centersGroupedByProvince.get(province);
                    if (municipalityCenters != null && !municipalityCenters.isEmpty()) {
                        districtVO.setCenters(municipalityCenters);
                    }
                } else {  // 非直辖市
                    List<RegionListVO.CityVO> cityVOs = new ArrayList<>();
                    List<Center> filteredCenters = centersGroupedByProvince.get(province).stream()
                            .filter(center -> !municipalities.contains(center.getCity()))
                            .collect(Collectors.toList());
                    for (Center center : filteredCenters) {
                        String cityName = center.getCity();
                        if (StringUtils.isEmpty(cityName)) {
                            continue;
                        }
                        RegionListVO.CityVO existingCityVO = cityVOs.stream()
                                .filter(city -> city.getCityName().equals(cityName))
                                .findFirst()
                                .orElseGet(() -> {
                                    RegionListVO.CityVO newCityVO = new RegionListVO.CityVO();
                                    newCityVO.setCityName(cityName);
                                    newCityVO.setCenters(new ArrayList<>());
                                    cityVOs.add(newCityVO);
                                    return newCityVO;
                                });

                        existingCityVO.getCenters().add(center);
                    }

                    districtVO.setCities(cityVOs);
                }

                districtVOs.add(districtVO);
            }

            regionVO.setDistricts(districtVOs);
            regions.add(regionVO);
        }

        return regions;
    }

    @Override
    public Page<BiCenterRespVO> ssuCenterList(BiCenterRepVO repVO) {
        // 构建查询条件
        QueryWrapper<com.r2.bi.entity.dc.Center> centerQueryWrapper = buildCenterQueryWrapper(repVO);
        // 分页查询中心数据
        List<com.r2.bi.entity.dc.Center> centers = centerDao.selectList(centerQueryWrapper);
        // 查询总裁办中心名称数据
        QueryWrapper<AssociationTable> tableQueryWrapper = tableCenterQueryWrapper(repVO);
        List<AssociationTable> associationTables = associationTableMapper.selectList(tableQueryWrapper);
        // 查询中心启动数据和伦理数据
        Map<Long, TCenterStartUp> centerStartUpMap = getCenterStartUpMap();
        Map<Long, TCenterEthics> centerEthicsMap = getCenterEthicsMap();
        // 查询中心列表
        Map<Integer, Center> centerMap = getCenterMap();
        // 查询自定义表格数据
        Map<String, List<Map<String, Object>>> resultMap = getCustomTableDataMap();
        // 转换为响应对象
        List<BiCenterRespVO> infoCenterRespVOS = convertToInfoCenterRespVOS(centers, centerMap, centerStartUpMap, centerEthicsMap, resultMap, associationTables);
        // 根据前端传递的参数进行过滤
        infoCenterRespVOS = filterInfoCenterRespVOS(infoCenterRespVOS, repVO);
        // 设置分页信息
        int currentPage = (int) repVO.getPage();
        int pageSize = (int) repVO.getSize();
        Page<BiCenterRespVO> resultPage = new Page<>(currentPage, pageSize);
        // 计算分页起始索引和结束索引
        int fromIndex = (currentPage - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, infoCenterRespVOS.size());
        // 设置分页记录
        if (fromIndex < toIndex) {
            resultPage.setRecords(infoCenterRespVOS.subList(fromIndex, toIndex));
        } else {
            resultPage.setRecords(new ArrayList<>()); // 空列表
        }
        // 设置总记录数
        resultPage.setTotal(infoCenterRespVOS.size());
        resultPage.setCurrent(currentPage);
        return resultPage;
    }


    @Override
    public BiCenterDetailsRespVO centerDetails(BiCenterDetailsRepVO repVO) {
        // 构建查询条件

        QueryWrapper<Center> centerQueryWrapper = new QueryWrapper<>();
        centerQueryWrapper.lambda().eq(Center::getIsDelete, 0)
                .eq(Center::getId, repVO.getCenterId().intValue());
        Center center = biCenterDao.selectOne(centerQueryWrapper);

        BiCenterDetailsRespVO biCenterDetailsRespVO = new BiCenterDetailsRespVO();
        if (center == null) {
            log.error("中心不存在,centerId:" + repVO.getCenterId());
            return biCenterDetailsRespVO;
        }

        biCenterDetailsRespVO.setCenterName(center.getCenterName());
        Date dateModified = center.getDatemodified();
        LocalDateTime localDateTime = dateModified.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateModified = localDateTime.format(formatter);
        biCenterDetailsRespVO.setDateModified(formattedDateModified);
        return biCenterDetailsRespVO;
    }

    @Override
    public List<BiCalendarRespVO> infoCalendarByScope(BiCalendarReqVO listReqVO) {
        String startDate = listReqVO.getStartDate();
        if (startDate.length() == 7) {
            startDate = findFirstDayOfMonth(startDate);
        }
        List<BiCalendarRespVO> calendarList = new ArrayList<>();
        // 查询未删除的中心
        QueryWrapper<Center> centerQueryWrapper = new QueryWrapper<>();
        centerQueryWrapper.eq("is_delete", 0);
        centerQueryWrapper.eq("id", listReqVO.getId());
        Center centers = biCenterDao.selectOne(centerQueryWrapper);
        if (centers == null) {
            log.error("中心不存在会议日期,centerId:" + listReqVO.getId());
            return calendarList;
        }

        try {
            // 查询指定年月内的会议
            List<CenterMeetingQueryStrRespVO> meetings = centerMeetingDao.queryByDateScope(startDate);

            // 过滤掉不在 t_center 下的会议
            meetings = meetings.stream()
                    .filter(meeting -> centers.getId().equals(meeting.getCenterId().intValue()))
                    .collect(Collectors.toList());
            // 获取年月
            String yearMonth = listReqVO.getStartDate();
            String firstDayOfMonth = findFirstDayOfMonth(yearMonth);
            String lastDayOfMonth = findLastDayOfMonth(yearMonth);

            // 计算每个会议的具体时间(放在cycle里)
            increaseCycleToMeetings(meetings, firstDayOfMonth, lastDayOfMonth);

            // 遍历当前月所有天数
            List<String> everyDay = findEveryDay(firstDayOfMonth, lastDayOfMonth);
            for (String day : everyDay) {
                // 创建每天所有会议返回实例
                BiCalendarRespVO listCalendarResp = new BiCalendarRespVO();
                List<CalendarDetailRespVO> calendarDetails = new ArrayList<>();
                Map<String, Integer> meetingTypeCount = new HashMap<>();

                // 遍历会议,根据条件新增至返回实例(去重)
                for (CenterMeetingQueryStrRespVO meeting : meetings) {
                    if (meeting.getCycle() == null || !meeting.getCycle().contains(day)) {
                        continue;
                    }

                    // 检查该天的会议列表中是否已存在相同的会议
                    CalendarDetailRespVO calendarDetailRespVO = new CalendarDetailRespVO();
                    calendarDetailRespVO.setCenterId(meeting.getCenterId());
                    calendarDetailRespVO.setCenterName(meeting.getCenterName());
                    calendarDetailRespVO.setMeetingId(meeting.getId());
                    calendarDetailRespVO.setMeetingType(meeting.getType());

                    if (!calendarDetails.contains(calendarDetailRespVO)) {
                        calendarDetails.add(calendarDetailRespVO);

                        // 计数放入 map (根据会议类型)
                        String meetingTypeKey = replaceMeetingType(meeting.getType());
                        meetingTypeCount.merge(meetingTypeKey, 1, Integer::sum);
                    }
                }

                if (calendarDetails.isEmpty()) {
                    continue;
                }

                listCalendarResp.setDate(day);
                listCalendarResp.setCalendarList(calendarDetails);
                listCalendarResp.setOverview(meetingTypeCount.entrySet().stream()
                        .map(entry -> new OverviewVO(entry.getKey(), entry.getValue()))
                        .collect(Collectors.toList()));

                // 添加到结果列表
                calendarList.add(listCalendarResp);
            }
        } catch (Exception e) {
            log.error("日历查询异常", e);
            throw new CodeException(ResultCode.Codes.ERROR, "日历查询异常!");
        }
        return calendarList;
    }


    @Override
    public BiRelateReportRespVO centerIntegrity(BiRelateReportReqVO req) {
        // 查询未删除的中心
        BiRelateReportRespVO biRelateReportRespVO = new BiRelateReportRespVO();
        QueryWrapper<Center> centerQueryWrapper = new QueryWrapper<>();
        centerQueryWrapper.eq("is_delete", 0);
        centerQueryWrapper.eq("id", Integer.valueOf(req.getId()));
        Center center = biCenterDao.selectOne(centerQueryWrapper);
        if (center == null) {
            log.info("该中心不存在");
            return biRelateReportRespVO;
        }
        QueryWrapper<InitSmoDataAreaRelate> initSmoDataAreaRelateQueryWrapper = new QueryWrapper<>();
        initSmoDataAreaRelateQueryWrapper.lambda()
                .eq(InitSmoDataAreaRelate::getProvince, center.getProvince());
        List<InitSmoDataAreaRelate> initSmoDataAreaRelates =
                initSmoDataAreaRelateMapper.selectList(initSmoDataAreaRelateQueryWrapper);
        InitSmoDataAreaRelate initSmoDataAreaRelate = initSmoDataAreaRelates.get(0);
        QueryWrapper<DataAreaRelateReport> dataAreaRelateReportQueryWrapper = new QueryWrapper<>();
        dataAreaRelateReportQueryWrapper.eq("center_name", center.getCenterName());
        dataAreaRelateReportQueryWrapper.eq("center_id", Long.parseLong(req.getId()));
        DataAreaRelateReport dataAreaRelateReport = dataAreaRelateReportDao.selectOne(dataAreaRelateReportQueryWrapper);

        if (dataAreaRelateReport == null) {
            log.info("该中心没有SMO更新人,不参与统计完成度");
            return biRelateReportRespVO;
        }
        biRelateReportRespVO.setCenterName(center.getCenterName());
        biRelateReportRespVO.setAreaName(initSmoDataAreaRelate.getAreaName());
        biRelateReportRespVO.setProvinceCity(center.getProvince() + "/" + center.getCity());
        biRelateReportRespVO.setUpdator(dataAreaRelateReport.getUpdator());
        biRelateReportRespVO.setSmoUpdatorLoginNum(dataAreaRelateReport.getSmoUpdatorLoginNum());
        // 定义输入和输出的日期格式
        DateTimeFormatter inputFormatter = new DateTimeFormatterBuilder()
                .appendPattern("yyyy-M-d H:m:s.S")
                .parseLenient()
                .toFormatter(Locale.getDefault());
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        try {
            String smoUpdatorLoginTime = dataAreaRelateReport.getSmoUpdatorLoginTime();
            String centerUpdateTime = dataAreaRelateReport.getCenterUpdateTime();
            if (smoUpdatorLoginTime != null && !smoUpdatorLoginTime.isEmpty()) {
                // 解析字符串为LocalDateTime
                LocalDateTime dateModified = LocalDateTime.parse(smoUpdatorLoginTime, inputFormatter);
                // 重新格式化为所需的格式
                String formattedDateModified = dateModified.format(outputFormatter);
                // 设置修改日期
                biRelateReportRespVO.setSmoUpdatorLoginTime(formattedDateModified);
            } else {
                biRelateReportRespVO.setSmoUpdatorLoginTime(null);
            }
            if (centerUpdateTime != null && !centerUpdateTime.isEmpty()) {
                // 解析字符串为LocalDateTime
                LocalDateTime updateTime = LocalDateTime.parse(centerUpdateTime, inputFormatter);
                // 重新格式化为所需的格式
                String formatCenterUpdateTime = updateTime.format(outputFormatter);
                // 设置修改日期
                biRelateReportRespVO.setCenterUpdateTime(formatCenterUpdateTime);
            } else {
                biRelateReportRespVO.setCenterUpdateTime(null);
            }
        } catch (Exception e) {
            log.error("日期转换异常", e);
            biRelateReportRespVO.setSmoUpdatorLoginTime(null);
            biRelateReportRespVO.setCenterUpdateTime(null);
        }
        biRelateReportRespVO.setCenterIntegrityPlus(dataAreaRelateReport.getCenterIntegrityPlus());
        biRelateReportRespVO.setCenterInvalidDataPlus(dataAreaRelateReport.getCenterInvalidDataPlus());

        biRelateReportRespVO.setDeptSize(dataAreaRelateReport.getDeptSize());
        biRelateReportRespVO.setDeptCenterIntegrity(dataAreaRelateReport.getDeptCenterIntegrity());
        biRelateReportRespVO.setInvalidDeptIntegrity(dataAreaRelateReport.getInvalidDeptIntegrity());
        biRelateReportRespVO.setMemberSize(dataAreaRelateReport.getMemberSize());
        biRelateReportRespVO.setMemberCenterIntegrity(dataAreaRelateReport.getMemberCenterIntegrity());
        biRelateReportRespVO.setInvalidMemberIntegrity(dataAreaRelateReport.getInvalidMemberIntegrity());
        return biRelateReportRespVO;
    }


    @Override
    public Object exportToExcel(BiRelateReportReqVO req, HttpServletResponse response) {
        BiRelateReportRespVO reportRespVO = centerIntegrity(req);
        try (OutputStream outputStream = response.getOutputStream()) {
            // 设置响应头
            response.reset();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("中心完整性报告", StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            try (Workbook workbook = new XSSFWorkbook()) {
                Sheet sheet = workbook.createSheet("中心完整性报告");
                // 创建标题行
                Row headerRow = sheet.createRow(0);
                String[] headers = {"中心名称", "区域名称", "省/市", "更新人", "SMO更新人登录次数", "SMO更新人登录时间",
                        "中心字段填写完整度", "中心无效数据占比", "中心最新更新时间", "中心科室数量", "中心科室数据完整度", "中心科室无效数据占比", "中心人员数量", "中心人员数据完整度", "中心人员无效数据占比"};
                for (int i = 0; i < headers.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    cell.setCellValue(headers[i]);
                }
                if (reportRespVO != null && reportRespVO.getUpdator() != null) {
                    // 创建数据行
                    Row dataRow = sheet.createRow(1);
                    dataRow.createCell(0).setCellValue(reportRespVO.getCenterName());
                    dataRow.createCell(1).setCellValue(reportRespVO.getAreaName());
                    dataRow.createCell(2).setCellValue(reportRespVO.getProvinceCity());
                    dataRow.createCell(3).setCellValue(reportRespVO.getUpdator());
                    dataRow.createCell(4).setCellValue(reportRespVO.getSmoUpdatorLoginNum());
                    dataRow.createCell(5).setCellValue(reportRespVO.getSmoUpdatorLoginTime());
                    dataRow.createCell(6).setCellValue(reportRespVO.getCenterIntegrityPlus());
                    dataRow.createCell(7).setCellValue(reportRespVO.getCenterInvalidDataPlus());
                    dataRow.createCell(8).setCellValue(reportRespVO.getCenterUpdateTime());
                    dataRow.createCell(9).setCellValue(reportRespVO.getDeptSize());
                    dataRow.createCell(10).setCellValue(reportRespVO.getDeptCenterIntegrity());
                    dataRow.createCell(11).setCellValue(reportRespVO.getInvalidDeptIntegrity());
                    dataRow.createCell(12).setCellValue(reportRespVO.getMemberSize());
                    dataRow.createCell(13).setCellValue(reportRespVO.getMemberCenterIntegrity());
                    dataRow.createCell(14).setCellValue(reportRespVO.getInvalidMemberIntegrity());
                }
                // 写入工作簿到输出流
                workbook.write(outputStream);
            }
        } catch (IOException e) {
            throw new CodeException(ResultCode.Codes.ERROR, "导出Excel失败!" + e.getMessage());
        }
        return null;
    }

    @Override
    public Object startSpeed(BiStartSpeedVO speedVO) {
        LambdaQueryWrapper<com.r2.bi.entity.dc.Center> queryWrapper = Wrappers.lambdaQuery(com.r2.bi.entity.dc.Center.class)
                .select(com.r2.bi.entity.dc.Center::getId, com.r2.bi.entity.dc.Center::getCenterName)
                .eq(com.r2.bi.entity.dc.Center::getIsDelete, false)
                .eq(com.r2.bi.entity.dc.Center::getId, speedVO.getCenterId().intValue());
        com.r2.bi.entity.dc.Center center = centerDao.selectOne(queryWrapper);
        if (center == null) {
            throw new CodeException(ResultCode.Codes.ERROR, "中心医院不存在");
        }
        QueryWrapper<SysAccessSystem> systemQueryWrapper = new QueryWrapper<>();
        systemQueryWrapper.lambda()
                .eq(SysAccessSystem::getIsDelete, 0)
                .eq(SysAccessSystem::getType, 2)
                .isNotNull(SysAccessSystem::getTabulatioName);
        List<SysAccessSystem> sysAccessSystems = sysAccessSystemDao.selectList(systemQueryWrapper);
        LambdaQueryWrapper<AssociationTable> tableLambdaQueryWrapper = new LambdaQueryWrapper<>();
        for (SysAccessSystem sysAccessSystem : sysAccessSystems) {
            tableLambdaQueryWrapper.eq(AssociationTable::getSystemSource, sysAccessSystem.getTabulatioName());
        }
        tableLambdaQueryWrapper.eq(AssociationTable::getType, 1);
        tableLambdaQueryWrapper.eq(AssociationTable::getMdmCenterName, center.getCenterName());
        List<AssociationTable> tableList = associationTableMapper.selectList(tableLambdaQueryWrapper);
        Set<String> centerNames = new HashSet<>();
        centerNames.add(center.getCenterName());
        if (!tableList.isEmpty()) {
            for (AssociationTable associationTable : tableList) {
                centerNames.add(associationTable.getThirdPartyCenterName());
            }
        }

        // 查询所有 CustomTableData
        LambdaQueryWrapper<CustomTableData> queryWrapper1 = Wrappers.lambdaQuery(CustomTableData.class);
        queryWrapper1.eq(CustomTableData::getStatus, "0");
        List<CustomTableData> customTableData = customTableDataMapper.selectList(queryWrapper1);
        /**
         * 优先统计2年内数据、没有2年内统计2年前的数据
         * 根据CustomTableData类的content字段里面有项目年份和是否参与统计这二个字段来判断是否剔除
         **/
        customTableData = filterCustomTableData(customTableData);
        /**
         * 根据入参speedType类型筛选数据：分类：默认为1：全部，2：立项-伦理批件，3：立项-合同签署，4：立项-实际启动
         * 立项-伦理批件：当speedType为2时，数据范围取【伦理批件获取日期】不为空的项目计算；
         * 立项-合同签署：当speedType为3时，数据范围取【主协议签署完成】不为空的项目计算；
         * 立项-实际启动：当speedType为4时，数据范围取【实际启动时间】不为空的项目计算；
         **/
        ObjectMapper objectMapper = new ObjectMapper();
        customTableData = customTableData.stream().filter(record -> {
            try {
                // 将 data 字段解析为 JsonNode
                JsonNode jsonData = objectMapper.readTree(record.getContent());

                switch (speedVO.getSpeedType().intValue()) {
                    case 1:
                        return true; // 全部显示
                    case 2:
                        return getFirstValidFieldValue(jsonData, "伦理批件获取日期") != null;
                    case 3:
                        return getFirstValidFieldValue(jsonData, "协议签署完成日期") != null;
                    case 4:
                        return getFirstValidFieldValue(jsonData, "实际启动日期") != null;
                    default:
                        return false;
                }
            } catch (Exception e) {
                // 处理解析错误
                e.printStackTrace();
                return false;
            }
        }).collect(Collectors.toList());

        SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy/MM/dd");

        List<Map<String, Object>> result = new ArrayList<>();
        // 定义需要提取的标准键
        Set<String> keysToExtract = new HashSet<>(Arrays.asList(
                "立项递交时间",
                "立项完成",
                "伦理递交日期",
                "伦理批件获取日期",
                "协议递交日期",
                "协议签署完成日期",
                "遗传办承诺书实际递交日期",
                "遗传办承诺书实际签署完成日期",
                "实际启动日期"
        ));

        // 用于存储所有天数的总和
        Map<String, Integer> totalDaysMap = new HashMap<>();
        Map<String, Integer> totalDiffDaysMap = new HashMap<>();
        // 定义额外的映射来追踪每个键的有效条目数量
        Map<String, Integer> daysCountMap = new HashMap<>();
        Map<String, Integer> diffDaysCountMap = new HashMap<>();
        // 用于存储每个时间段的起始和结束日期
        Map<String, List<Date>> sivStartDates = new HashMap<>();
        Map<String, List<Date>> projectEndDates = new HashMap<>();
        Map<String, List<Date>> ethicsStartDates = new HashMap<>();
        Map<String, List<Date>> contractEndDates = new HashMap<>();
        Map<String, List<Date>> hgrStartDates = new HashMap<>();

        // 遍历所有数据项
        for (CustomTableData customTableDatum : customTableData) {
            // 获取 content 字段并转换为 Map
            String content = customTableDatum.getContent();
            Map<String, Map<String, Object>> transformedData = JSON.parseObject(content, new TypeReference<Map<String, Map<String, Object>>>() {
            });

            // 将嵌套 Map 扁平化，提取出 value 部分，并确保所有值都是 String 类型
            Map<String, String> keyValueMap = transformedData.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> String.valueOf(entry.getValue().get("value"))
                    ));

            // 检查中心名称是否符合要求
            if (StringUtils.isNotBlank(center.getCenterName())) {
                String centerName = keyValueMap.get("中心名称");
                if (centerName == null || !centerNames.contains(centerName)) {
                    continue;
                }
            } else {
                throw new CodeException(ResultCode.Codes.ERROR, "中心医院不匹配");
            }

            // 创建一个临时映射来存储当前记录的有效键值对
            Map<String, String> filteredKeyValueMap = new HashMap<>();

            // 提取需要的键，无论其值是否为空
            for (Map.Entry<String, String> entry : keyValueMap.entrySet()) {
                String key = entry.getKey();
                String mappedKey = FIELD_MAPPINGS.getOrDefault(key, key);
                if (keysToExtract.contains(mappedKey) && entry.getValue() != null && !entry.getValue().trim().isEmpty()) {
                    filteredKeyValueMap.put(mappedKey, entry.getValue());
                }
            }

            // 计算各个时间段的天数、偏移量和差异天数
            Map<String, Integer> daysMap = new HashMap<>();
            Map<String, Integer> diffDaysMap = new HashMap<>();

            // 立项递交至 SIV
            customDaysAndOffset(daysMap, diffDaysMap, "sivCount",
                    filteredKeyValueMap.get("立项递交时间"),
                    filteredKeyValueMap.get("实际启动日期"),
                    filteredKeyValueMap.get("立项递交时间"),// 上一个时间段的开始日期
                    filteredKeyValueMap.get("立项递交时间"),// 下一个一个时间段的开始日期
                    DATE_FORMAT, true);

            // 立项递交至立项完成
            customDaysAndOffset(daysMap, diffDaysMap, "projectCount",
                    filteredKeyValueMap.get("立项递交时间"),
                    filteredKeyValueMap.get("立项完成"),
                    filteredKeyValueMap.get("立项完成"),  // 上一个时间段的开始日期
                    filteredKeyValueMap.get("伦理递交日期"), // 下一个一个时间段的开始日期
                    DATE_FORMAT, true);

            // 伦理递交至伦理完成
            customDaysAndOffset(daysMap, diffDaysMap, "ethicsCount",
                    filteredKeyValueMap.get("伦理递交日期"),
                    filteredKeyValueMap.get("伦理批件获取日期"),
                    filteredKeyValueMap.get("伦理批件获取日期"),// 上一个时间段的开始日期
                    filteredKeyValueMap.get("协议递交日期"), // 下一个一个时间段的开始日期
                    DATE_FORMAT, true);

            // 合同递交至合同签署
            customDaysAndOffset(daysMap, diffDaysMap, "contractCount",
                    filteredKeyValueMap.get("协议递交日期"),
                    filteredKeyValueMap.get("协议签署完成日期"),
                    filteredKeyValueMap.get("伦理批件获取日期"),// 上一个时间段的开始日期
                    filteredKeyValueMap.get("遗传办承诺书实际递交日期"), //下一个一个时间段的开始日期
                    DATE_FORMAT, true);

            // 分中心 HGR 备案递交至盖章完成
            customDaysAndOffset(daysMap, diffDaysMap, "hgrCount",
                    filteredKeyValueMap.get("遗传办承诺书实际递交日期"),
                    filteredKeyValueMap.get("遗传办承诺书实际签署完成日期"),
                    filteredKeyValueMap.get("伦理批件获取日期"),// 上一个时间段的开始日期
                    filteredKeyValueMap.get("遗传办承诺书实际递交日期"), // 下一个一个时间段的开始日期
                    DATE_FORMAT, false);

            // 收集各个时间段的起始和结束日期
            collectDates(filteredKeyValueMap, sivStartDates, projectEndDates, ethicsStartDates, contractEndDates, hgrStartDates, DATE_FORMAT);

            // 将当前记录的天数、偏移量和差异天数累加到总和中
            for (Map.Entry<String, Integer> entry : daysMap.entrySet()) {
                String key = entry.getKey();
                Integer value = entry.getValue();
                if (value != null && value != -1) { // 确保只在值存在并且不为-1时才进行累加
                    totalDaysMap.merge(key, value, Integer::sum);
                    daysCountMap.merge(key, 1, Integer::sum); // 每个key对应的有效条目数量+1
                }
            }

            for (Map.Entry<String, Integer> entry : diffDaysMap.entrySet()) {
                String key = entry.getKey();
                Integer value = entry.getValue();
                if (value != null && value != -1) {
                    totalDiffDaysMap.merge(key, value, Integer::sum);
                    diffDaysCountMap.merge(key, 1, Integer::sum);
                }
            }
            Map<String, Object> resultEntry = new HashMap<>();
            resultEntry.putAll(daysMap);
            resultEntry.putAll(diffDaysMap);

            result.add(resultEntry);
        }
        // 计算平均值
        Map<String, Integer> averageDaysMap = new HashMap<>();
        Map<String, Integer> averageDiffDaysMap = new HashMap<>();

        // 计算平均值时使用对应的 countMap 来获取有效条目的数量
        for (Map.Entry<String, Integer> entry : totalDaysMap.entrySet()) {
            double average = (double) entry.getValue() / daysCountMap.getOrDefault(entry.getKey(), 1); // 防止除以零
            int roundedAverage = (int) Math.round(average);
            averageDaysMap.put(entry.getKey(), roundedAverage);
        }
        for (Map.Entry<String, Integer> entry : totalDiffDaysMap.entrySet()) {
            double average = (double) entry.getValue() / diffDaysCountMap.getOrDefault(entry.getKey(), 1);
            int roundedAverage = (int) Math.round(average);
            averageDiffDaysMap.put(entry.getKey(), roundedAverage);
        }

        BiStartSpeedResponseVO response = new BiStartSpeedResponseVO();
        response.setContractCount(new BiStartSpeedResponseVO.ContractCount());
        response.setEthicsCount(new BiStartSpeedResponseVO.ContractCount());
        response.setHgrCount(new BiStartSpeedResponseVO.ContractCount());
        response.setSivCount(new BiStartSpeedResponseVO.ContractCount());
        response.setProjectCount(new BiStartSpeedResponseVO.ContractCount());

        response.getSivCount().setDays(averageDaysMap.getOrDefault("sivCount", 0));
        response.getSivCount().setDiffDays(averageDiffDaysMap.getOrDefault("sivCount", 0));

        response.getProjectCount().setDays(averageDaysMap.getOrDefault("projectCount", 0));
        response.getProjectCount().setDiffDays(averageDiffDaysMap.getOrDefault("projectCount", 0));

        response.getEthicsCount().setDays(averageDaysMap.getOrDefault("ethicsCount", 0));
        response.getEthicsCount().setDiffDays(averageDiffDaysMap.getOrDefault("ethicsCount", 0));

        response.getContractCount().setDays(averageDaysMap.getOrDefault("contractCount", 0));
        response.getContractCount().setDiffDays(averageDiffDaysMap.getOrDefault("contractCount", 0));

        response.getHgrCount().setDays(averageDaysMap.getOrDefault("hgrCount", 0));
        response.getHgrCount().setDiffDays(0);
        // 重新计算偏移量
        response.getSivCount().setOffset(0);
        BiStartSpeedResponseVO.ContractCount projectCount = response.getProjectCount();
        projectCount.setOffset(0);
        BiStartSpeedResponseVO.ContractCount ethicsCount = response.getEthicsCount();
        ethicsCount.setOffset(projectCount.getDays() + projectCount.getDiffDays());
        BiStartSpeedResponseVO.ContractCount contractCount = response.getContractCount();
        contractCount.setOffset(ethicsCount.getDays() + ethicsCount.getOffset() + ethicsCount.getDiffDays());
        BiStartSpeedResponseVO.ContractCount hgrCount = response.getHgrCount();
        hgrCount.setOffset(ethicsCount.getDays() + ethicsCount.getOffset() + contractCount.getDiffDays());

        TCenterStartUp tCenterStartUp = tCenterStartUpMapper.selectOne(Wrappers.<TCenterStartUp>lambdaQuery()
                .eq(TCenterStartUp::getCenterId, speedVO.getCenterId()).eq(TCenterStartUp::getIsDelete, 0));
        TCenterEthics tCenterEthics = tCenterEthicsMapper.selectOne(Wrappers.<TCenterEthics>lambdaQuery()
                .eq(TCenterEthics::getCenterId, speedVO.getCenterId()).eq(TCenterEthics::getIsDelete, 0));

        CenterProjectApproval centerProjectApproval = centerProjectApprovalDao.selectOne(Wrappers.<CenterProjectApproval>lambdaQuery()
                .eq(CenterProjectApproval::getCenterId, speedVO.getCenterId())
                .eq(CenterProjectApproval::getIsDelete, 0));

        if (tCenterStartUp != null && tCenterStartUp.getCenterRqmt() != null && tCenterStartUp.getCenterRqmt().contains("开通免费检查账号或者免费检查单")) {
            response.setKickoffRqmt(true);
        } else {
            response.setKickoffRqmt(false);
        }
        Map<String, String> approvalMapping = new HashMap<>();
        approvalMapping.put("需要", "否");
        approvalMapping.put("不需要", "是");
        approvalMapping.put("视情况而定", "进一步沟通");

        Map<String, String> auditTypeMapping = new HashMap<>();
        auditTypeMapping.put("上会", "上会");
        auditTypeMapping.put("快审", "快审");

        HashMap<String, String> leaderFileHashMap = new HashMap<>();
        leaderFileHashMap.put("是", "否");
        leaderFileHashMap.put("否", "是");

        HashMap<String, String> communicationLetterHashMap = new HashMap<>();
        communicationLetterHashMap.put("是", "否");
        communicationLetterHashMap.put("否", "是");

        if (tCenterEthics != null) {
            response.setEthicalPre(
                    tCenterEthics.getEthicalPre() != null && tCenterEthics.getEthicalPre().contains("是")
            );
            response.setLeaderApproval(mapValue(tCenterEthics.getLeaderApproval(), approvalMapping, "NA"));
            response.setEthicsAuditType(mapValue(tCenterEthics.getEthicsAuditType(), auditTypeMapping, "NA"));
        } else {
            response.setEthicalPre(false);
            response.setLeaderApproval("NA");
            response.setEthicsAuditType("NA");
        }

        if (centerProjectApproval != null) {
            response.setIsGroupLeaderFile(mapValue(centerProjectApproval.getIsGroupLeaderFile(), leaderFileHashMap, "NA"));
            response.setCommunicationLetter(mapValue(centerProjectApproval.getCommunicationLetter(), communicationLetterHashMap, "NA"));
        } else {
            response.setIsGroupLeaderFile("NA");
            response.setCommunicationLetter("NA");
        }
        response.setCenterId(speedVO.getCenterId());

        BiStartSpeedResponseDTO finalResponse = convertToDTO(response);

        return finalResponse;
    }

    public BiStartSpeedResponseDTO convertToDTO(BiStartSpeedResponseVO response) {
        BiStartSpeedResponseDTO dto = new BiStartSpeedResponseDTO();

        dto.setSivCount(new ContractCountDTO(
                response.getSivCount().getDays(),
                response.getSivCount().getOffset()
        ));

        dto.setProjectCount(new ContractCountDTO(
                response.getProjectCount().getDays(),
                response.getProjectCount().getOffset()
        ));

        dto.setEthicsCount(new ContractCountDTO(
                response.getEthicsCount().getDays(),
                response.getEthicsCount().getOffset()
        ));

        dto.setContractCount(new ContractCountDTO(
                response.getContractCount().getDays(),
                response.getContractCount().getOffset()
        ));

        dto.setHgrCount(new ContractCountDTO(
                response.getHgrCount().getDays(),
                response.getHgrCount().getOffset()
        ));

        dto.setKickoffRqmt(response.getKickoffRqmt());
        dto.setEthicalPre(response.getEthicalPre());
        dto.setLeaderApproval(response.getLeaderApproval());
        dto.setEthicsAuditType(response.getEthicsAuditType());
        dto.setIsGroupLeaderFile(response.getIsGroupLeaderFile());
        dto.setCommunicationLetter(response.getCommunicationLetter());
        dto.setCenterId(response.getCenterId());

        return dto;
    }

    public List<CustomTableData> filterCustomTableData(List<CustomTableData> customTableData) {
        // 按医院分组
        Map<String, List<CustomTableData>> groupedByHospital = new HashMap<>();

        int currentYear = LocalDate.now().getYear();

        // 第一步：先按医院名分组
        for (CustomTableData data : customTableData) {
            try {
                JSONObject contentJson = JSON.parseObject(data.getContent());

                if (!contentJson.containsKey("项目年份") ||
                        !contentJson.containsKey("是否参与统计") ||
                        !contentJson.containsKey("中心名称")) {
                    log.debug("数据不包含必要字段，跳过");
                    continue;
                }

                String includeValue = extractValueFromField(contentJson.get("是否参与统计"));
                if (!"是".equals(includeValue)) {
                    log.debug("isInclude 为 false，跳过");
                    continue;
                }

                String hospitalName = extractValueFromField(contentJson.get("中心名称"));
                if (hospitalName == null || hospitalName.isEmpty()) {
                    log.warn("缺少中心名称字段，跳过");
                    continue;
                }

                String yearValueStr = extractValueFromField(contentJson.get("项目年份"));
                int year = 0;
                if (yearValueStr != null && !yearValueStr.trim().isEmpty()) {
                    try {
                        year = Integer.parseInt(yearValueStr.trim());
                    } catch (NumberFormatException e) {
                        log.warn("项目年份格式错误，跳过此条数据，ID: " + data.getId());
                        continue;
                    }
                }

                boolean isRecent = year >= currentYear - 2;

                // 添加到对应的医院列表中
                groupedByHospital.computeIfAbsent(hospitalName, k -> new ArrayList<>()).add(data);

            } catch (Exception e) {
                log.error("解析数据失败，ID: " + data.getId(), e);
            }
        }

        // 第二步：筛选结果
        List<CustomTableData> result = new ArrayList<>();

        for (Map.Entry<String, List<CustomTableData>> entry : groupedByHospital.entrySet()) {
            String hospitalName = entry.getKey();
            List<CustomTableData> dataList = entry.getValue();

            // 检查是否有任意一个是“近两年”的数据
            boolean hasRecent = dataList.stream().anyMatch(data -> {
                try {
                    JSONObject contentJson = JSON.parseObject(data.getContent());
                    String yearValueStr = extractValueFromField(contentJson.get("项目年份"));
                    int year = Integer.parseInt(yearValueStr.trim());
                    return year >= currentYear - 2;
                } catch (Exception e) {
                    log.warn("解析数据失败，跳过此条数据", e);
                    return false;
                }
            });

            if (hasRecent) {
                // 取出所有近两年的数据
                result.addAll(dataList.stream()
                        .filter(data -> {
                            try {
                                JSONObject contentJson = JSON.parseObject(data.getContent());
                                String yearValueStr = extractValueFromField(contentJson.get("项目年份"));
                                int year = Integer.parseInt(yearValueStr.trim());
                                return year >= currentYear - 2;
                            } catch (Exception e) {
                                log.warn("解析数据失败，跳过此条数据", e);
                                return false;
                            }
                        })
                        .collect(Collectors.toList()));
                log.debug("医院 {} 有近两年数据，共 {} 条", hospitalName, result.size());
            } else {
                // 否则取全部两年前的数据
                result.addAll(dataList);
                log.debug("医院 {} 没有近两年数据，使用两年前数据，共 {} 条", hospitalName, result.size());
            }
        }

        return result;
    }


    private static String extractValueFromField(Object fieldObj) {
        if (fieldObj == null) {
            return null;
        }
        if (fieldObj instanceof JSONObject) {
            JSONObject json = (JSONObject) fieldObj;
            return json.getString("value");
        } else if (fieldObj instanceof String) {
            try {
                JSONObject json = JSON.parseObject((String) fieldObj);
                return json.getString("value");
            } catch (Exception e) {
                // 如果不是JSON字符串，直接当作普通字符串使用
                return (String) fieldObj;
            }
        }
        return null;
    }

    private String mapValue(String input, Map<String, String> mapping, String defaultValue) {
        if (input == null) {
            return defaultValue;
        }
        for (Map.Entry<String, String> entry : mapping.entrySet()) {
            if (input.equals(entry.getKey())) {
                return entry.getValue();
            }
        }
        return defaultValue;
    }

    private String getFirstValidFieldValue(JsonNode jsonData, String mainField) {
        List<String> candidateKeys = new ArrayList<>();
        // 先加主字段，优先匹配
        candidateKeys.add(mainField);
        // 再加上所有别名字段
        candidateKeys.addAll(getAliasFields(mainField));

        for (String key : candidateKeys) {
            if (jsonData.has(key)) {
                JsonNode valueNode = jsonData.get(key).get("value");
                if (valueNode != null && !valueNode.asText().trim().isEmpty()) {
                    return valueNode.asText();
                }
            }
        }

        return null;
    }

    private List<String> getAliasFields(String mainField) {
        return FIELD_MAPPINGS.entrySet().stream()
                .filter(entry -> entry.getValue().equals(mainField))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    @Override
    public Page<CustomTableDataWithDetailsReqVO> startSpeedDetail(InfoStartSpeedDetailVO detailVO) {
        LambdaQueryWrapper<Center> queryWrapper = Wrappers.lambdaQuery(Center.class)
                .select(Center::getId, Center::getCenterName)
                .eq(Center::getIsDelete, false)
                .eq(Center::getId, detailVO.getCenterId().intValue());

        Center center = biCenterDao.selectOne(queryWrapper);

        QueryWrapper<SysAccessSystem> systemQueryWrapper = new QueryWrapper<>();
        systemQueryWrapper.lambda()
                .eq(SysAccessSystem::getIsDelete, 0)
                .eq(SysAccessSystem::getType, 2)
                .isNotNull(SysAccessSystem::getTabulatioName);
        List<SysAccessSystem> sysAccessSystems = sysAccessSystemDao.selectList(systemQueryWrapper);
        LambdaQueryWrapper<AssociationTable> tableLambdaQueryWrapper = new LambdaQueryWrapper<>();
        for (SysAccessSystem sysAccessSystem : sysAccessSystems) {
            tableLambdaQueryWrapper.eq(AssociationTable::getSystemSource, sysAccessSystem.getTabulatioName());
        }
        tableLambdaQueryWrapper
                .eq(AssociationTable::getType, 1)
                .eq(AssociationTable::getMdmCenterName, center.getCenterName());
        List<AssociationTable> tableList = associationTableMapper.selectList(tableLambdaQueryWrapper);
        Set<String> centerNames = new HashSet<>();
        centerNames.add(center.getCenterName());
        if (!tableList.isEmpty()) {
            for (AssociationTable associationTable : tableList) {
                centerNames.add(associationTable.getThirdPartyCenterName());
            }
        }

        LambdaQueryWrapper<CustomTableData> queryWrapper1 = Wrappers.lambdaQuery(CustomTableData.class);
        queryWrapper1.eq(CustomTableData::getStatus, 0);
        List<CustomTableData> customTableData = customTableDataMapper.selectList(queryWrapper1);
        /**
         * 优先统计2年内数据、没有2年内统计2年前的数据
         * 根据CustomTableData类的content字段里面有项目年份和是否参与统计这二个字段来判断是否剔除
         **/
        customTableData = filterCustomTableData(customTableData);

        // 过滤数据，根据 detailVO.getSpeedType() 的值决定是否显示
        ObjectMapper objectMapper = new ObjectMapper();
        customTableData = customTableData.stream().filter(record -> {
            try {
                // 将 data 字段解析为 JsonNode
                JsonNode jsonData = objectMapper.readTree(record.getContent());

                switch (detailVO.getSpeedType().intValue()) {
                    case 1:
                        return true; // 全部显示
                    case 2:
                        return getFirstValidFieldValue(jsonData, "伦理批件获取日期") != null;
                    case 3:
                        return getFirstValidFieldValue(jsonData, "协议签署完成日期") != null;
                    case 4:
                        return getFirstValidFieldValue(jsonData, "实际启动日期") != null;
                    default:
                        return false;
                }
            } catch (Exception e) {
                // 处理解析错误
                e.printStackTrace();
                return false;
            }
        }).collect(Collectors.toList());
        SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy/MM/dd");

        List<CustomTableDataWithDetailsReqVO> result = new ArrayList<>();

        Set<String> keysToExtract = new HashSet<>(Arrays.asList(
                "立项递交时间",
                "立项完成",
                "伦理递交日期",
                "伦理批件获取日期",
                "协议递交日期",
                "协议签署完成日期",
                "遗传办承诺书实际递交日期",
                "遗传办承诺书实际签署完成日期",
                "实际启动日期"
        ));

        Map<String, Integer> totalDaysMap = new HashMap<>();
        int tableCount = 0;

        for (CustomTableData customTableDatum : customTableData) {
            String content = customTableDatum.getContent();
            Map<String, Map<String, Object>> transformedData = JSON.parseObject(content, new TypeReference<Map<String, Map<String, Object>>>() {
            });

            Map<String, String> keyValueMap = transformedData.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> String.valueOf(entry.getValue().get("value"))
                    ));

            if (StringUtils.isNotBlank(center.getCenterName())) {
                String centerName = keyValueMap.get("中心名称");
                if (centerName == null || !centerNames.contains(centerName)) {
                    continue;
                }
            } else {
                throw new CodeException(ResultCode.Codes.ERROR, "中心医院不匹配");
            }

            String objectName = keyValueMap.get("项目名称");
            Map<String, String> originalKeyValueMap = new HashMap<>();

            for (Map.Entry<String, String> entry : keyValueMap.entrySet()) {
                String key = entry.getKey();
                String mappedKey = FIELD_MAPPINGS.getOrDefault(key, key);
                if (keysToExtract.contains(mappedKey)) {
                    originalKeyValueMap.put(mappedKey, entry.getValue());
                }
            }

            Map<String, String> sivCount = new HashMap<>();
            Map<String, String> projectCount = new HashMap<>();
            Map<String, String> contractCount = new HashMap<>();
            Map<String, String> ethicsCount = new HashMap<>();
            Map<String, String> hgrCount = new HashMap<>();

            String countType = detailVO.getCountType();


            if ("sivCount".equals(countType)) {
                customDaysDetail(sivCount,
                        originalKeyValueMap.get("立项递交时间"),
                        originalKeyValueMap.get("实际启动日期"),
                        DATE_FORMAT, originalKeyValueMap, new String[]{"立项递交时间", "实际启动日期"});
            } else if ("projectCount".equals(countType)) {
                customDaysDetail(projectCount,
                        originalKeyValueMap.get("立项递交时间"),
                        originalKeyValueMap.get("立项完成"),
                        DATE_FORMAT, originalKeyValueMap, new String[]{"立项递交时间", "立项完成"});
            } else if ("ethicsCount".equals(countType)) {
                customDaysDetail(ethicsCount,
                        originalKeyValueMap.get("伦理递交日期"),
                        originalKeyValueMap.get("伦理批件获取日期"),
                        DATE_FORMAT, originalKeyValueMap, new String[]{"伦理递交日期", "伦理批件获取日期"});
            } else if ("contractCount".equals(countType)) {
                customDaysDetail(contractCount,
                        originalKeyValueMap.get("协议递交日期"),
                        originalKeyValueMap.get("协议签署完成日期"),
                        DATE_FORMAT, originalKeyValueMap, new String[]{"协议递交日期", "协议签署完成日期"});
            } else if ("hgrCount".equals(countType)) {
                customDaysDetail(hgrCount,
                        originalKeyValueMap.get("遗传办承诺书实际递交日期"),
                        originalKeyValueMap.get("遗传办承诺书实际签署完成日期"),
                        DATE_FORMAT, originalKeyValueMap, new String[]{"遗传办承诺书实际递交日期", "遗传办承诺书实际签署完成日期"});
            }

            Map<String, String> daysMap = sivCount;
            if ("projectCount".equals(countType)) {
                daysMap = projectCount;
            } else if ("ethicsCount".equals(countType)) {
                daysMap = ethicsCount;
            } else if ("contractCount".equals(countType)) {
                daysMap = contractCount;
            } else if ("hgrCount".equals(countType)) {
                daysMap = hgrCount;
            }

            String day = daysMap.get("days");

            for (Map.Entry<String, String> entry : daysMap.entrySet()) {
                if (entry.getKey().equals(day)) {
                    int days = Integer.parseInt(entry.getValue());
                    totalDaysMap.merge(countType, days, Integer::sum);
                }
            }
            tableCount++;

            CustomTableDataWithDetailsReqVO detailsVO = new CustomTableDataWithDetailsReqVO();
            // 掩码处理，隐藏项目名称部分字符
            String maskedName = StringUtils.maskFromSecondChar(objectName);
            detailsVO.setTableName(maskedName);

            detailsVO.setDays(daysMap.get("days"));
            detailsVO.setProjectSubmissionTime(daysMap.get("立项递交时间"));
            detailsVO.setProjectCompletion(daysMap.get("立项完成"));
            detailsVO.setEthicsSubmissionDate(daysMap.get("伦理递交日期"));
            detailsVO.setEthicsApprovalDate(daysMap.get("伦理批件获取日期"));
            detailsVO.setContractSsubmissionDate(daysMap.get("协议递交日期"));
            detailsVO.setContractSigningCompletionDate(daysMap.get("协议签署完成日期"));
            detailsVO.setGeneticCommitmentActualSubmissionDate(daysMap.get("遗传办承诺书实际递交日期"));
            detailsVO.setGeneticCommitmentActualSigningCompletionDate(daysMap.get("遗传办承诺书实际签署完成日期"));
            detailsVO.setActualStartDate(daysMap.get("实际启动日期"));

            result.add(detailsVO);
        }

        Page<CustomTableDataWithDetailsReqVO> page = new Page<>();
        page.setCurrent(detailVO.getPage());
        page.setSize(detailVO.getSize());
        page.setTotal(tableCount);
        page.setRecords(result);

        return page;
    }

    private void customDaysDetail(Map<String, String> daysMap, String startDays, String endDays, SimpleDateFormat dateFormat, Map<String, String> originalKeyValueMap, String[] requiredKeys) {
        if (StringUtils.isNotBlank(startDays) && StringUtils.isNotBlank(endDays)) {
            try {
                Date startDate = dateFormat.parse(startDays);
                Date endDate = dateFormat.parse(endDays);
                long differenceInMilliseconds = endDate.getTime() - startDate.getTime();
                long differenceInDays = TimeUnit.DAYS.convert(differenceInMilliseconds, TimeUnit.MILLISECONDS);

                daysMap.put("days", String.valueOf(differenceInDays));

            } catch (ParseException e) {
                throw new CodeException(ResultCode.Codes.ERROR, "日期解析错误");
            }
        }

        // 添加必要的键值对
        for (String requiredKey : requiredKeys) {
            for (Map.Entry<String, String> entry : originalKeyValueMap.entrySet()) {
                String mappedKey = FIELD_MAPPINGS.getOrDefault(entry.getKey(), entry.getKey());
                if (mappedKey.equals(requiredKey)) {
                    daysMap.put(mappedKey, entry.getValue());
                    break; // 找到后跳出内层循环
                }
            }
        }
    }

    @Override
    public Page<BiCenterMemberRespVO> queryByPage(BiCenterMemberReqVO reqVO) {
        Page<BiCenterMemberRespVO> page = new Page<>();
        if (reqVO.getUserAccount() != null) {
            BiUser user = sysUserService.getByUserAccount(reqVO.getUserAccount());
            if (user == null) {
                throw new CodeException(ResultCode.Codes.ERROR, "用户不存在");
            }
            //密码解密
            String decryptPwd = Sm2Util.decrypt(privateKey, reqVO.getPassword());
            Subject subject = SecurityUtils.getSubject();

            //调用主数据平台接口
            JSONObject loginRes = sysMdmService.login(reqVO.getUserAccount(), decryptPwd);
            Integer code = loginRes.getInteger("errorCode");
            if (!AuthLoginEnum.SUCCESS.getErrorCode().equals(code)) {
                // 构建一个空的分页对象并设置错误信息
                page.setTotal(0L);
                page.setRecords(Collections.emptyList());
                throw new CodeException(ResultCode.Codes.ERROR, "密码错误,请重新输入");
            }
            subject.login(new NoPwdToken(reqVO.getUserAccount()));
        }
        if (reqVO.getCenterId() == null || reqVO.getContactTypeNotNull() == null) {
            return page;
        }
        //列表数量
        Integer totalCount = biCenterMemberDao.totalCount(reqVO);
        page.setTotal(totalCount);
        //列表数据
        Integer start = (reqVO.getPage() - 1) * reqVO.getSize();
        List<BiCenterMemberRespVO> centerMemberRespVOS = biCenterMemberDao.listByCond(reqVO, start, reqVO.getSize());
        if (CollectionUtil.isNotEmpty(centerMemberRespVOS)) {
            //任职信息/角色 数据
            List<Long> memberIds = centerMemberRespVOS.stream().map(item -> item.getId()).collect(Collectors.toList());
            QueryWrapper<CenterMemberContactTypeRole> centerMemberContactTypeRoleDaoQueryWrapper = new QueryWrapper<>();
            centerMemberContactTypeRoleDaoQueryWrapper.lambda().in(CenterMemberContactTypeRole::getMemberId, memberIds)
                    .isNotNull(CenterMemberContactTypeRole::getRole);
            List<CenterMemberContactTypeRole> centerMemberContactTypeRoles = centerMemberContactTypeRoleDao.selectList(centerMemberContactTypeRoleDaoQueryWrapper);

            //数据拼装
            for (BiCenterMemberRespVO centerMemberRespVO : centerMemberRespVOS) {
                //部门数据 赋值 20230901
                centerMemberRespVO.setContactName(centerMemberRespVO.getContactType());
                //任职信息 角色 数据拼装
                if (CollectionUtil.isNotEmpty(centerMemberContactTypeRoles)) {
                    for (CenterMemberContactTypeRole centerMemberContactTypeRole : centerMemberContactTypeRoles) {
                        if (centerMemberRespVO.getId().equals(centerMemberContactTypeRole.getMemberId()) && centerMemberRespVO.getCenterId().equals(centerMemberContactTypeRole.getCenterId())) {
                            if (StrUtil.isNotEmpty(centerMemberRespVO.getContactType())) {
                                centerMemberRespVO.setContactType(centerMemberRespVO.getContactType() + "," + centerMemberContactTypeRole.getContactType());
                            } else {
                                centerMemberRespVO.setContactType(centerMemberContactTypeRole.getContactType());
                            }
                            if (StrUtil.isNotEmpty(centerMemberRespVO.getRole())) {
                                centerMemberRespVO.setRole(centerMemberRespVO.getRole() + "," + StrUtil.replace(centerMemberContactTypeRole.getRole(), ";", ","));
                            } else {
                                centerMemberRespVO.setRole(StrUtil.replace(centerMemberContactTypeRole.getRole(), ";", ","));
                            }
                        }
                    }
                }
                //加密/解密util
                EncryptUtil util = EncryptUtil.getInstance();
                //姓名解密
                if (StrUtil.isNotEmpty(centerMemberRespVO.getName())) {
                    centerMemberRespVO.setName(util.decodeStr(centerMemberRespVO.getName()));
                }
                //手机号解密
                if (StrUtil.isNotEmpty(centerMemberRespVO.getTel())) {
                    centerMemberRespVO.setTel(util.decodeStr(centerMemberRespVO.getTel()));
                }
                if (reqVO.getId() == null || !reqVO.getId().equals(centerMemberRespVO.getId())) {
                    //手机号脱敏
                    centerMemberRespVO.setTel(maskPhoneNumbers(centerMemberRespVO.getTel()));
                }
            }
        }
        centerMemberRespVOS = centerMemberRespVOS.stream()
                .filter(member -> member.getContactName() != null)
                .collect(Collectors.toList());

        page.setRecords(centerMemberRespVOS);
        return page;
    }

    private String maskPhoneNumbers(String phoneNumbers) {
        if (phoneNumbers == null) {
            return null;
        }

        // 拆分电话号码
        String[] numbers = phoneNumbers.split("，");

        // 处理每个电话号码
        for (int i = 0; i < numbers.length; i++) {
            numbers[i] = maskPhoneNumber(numbers[i].trim());
        }

        // 重新组合电话号码
        return String.join("，", numbers);
    }

    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null) {
            return null;
        }

        // 判断是否为手机号
        if (phoneNumber.matches("^1\\d{10}$")) {
            return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(7);
        }

        // 判断是否为座机号
        if (phoneNumber.matches("^\\d{3,4}-\\d{7,8}$")) {
            String[] parts = phoneNumber.split("-");
            String areaCode = parts[0];
            String number = parts[1];
            int middleLength = number.length() / 2;
            return areaCode + "-" + number.substring(0, middleLength) + "****" + number.substring(middleLength + 4);
        }

        // 其他类型的电话号码不做处理
        return phoneNumber;
    }

    //取每个的起始日期
    private void collectDates(Map<String, String> keyValueMap, Map<String, List<Date>> sivStartDates, Map<String, List<Date>> projectEndDates, Map<String, List<Date>> ethicsStartDates, Map<String, List<Date>> contractEndDates, Map<String, List<Date>> hgrStartDates, SimpleDateFormat dateFormat) {
        try {
            Date sivStartDate = parseDate(keyValueMap.get("立项递交时间"), dateFormat);
            Date projectEndDate = parseDate(keyValueMap.get("立项完成"), dateFormat);
            Date ethicsStartDate = parseDate(keyValueMap.get("伦理递交日期"), dateFormat);
            Date contractEndDate = parseDate(keyValueMap.get("协议签署完成日期"), dateFormat);
            Date hgrStartDate = parseDate(keyValueMap.get("遗传办承诺书实际递交日期"), dateFormat);

            if (sivStartDate != null) {
                sivStartDates.computeIfAbsent("sivStartDates", k -> new ArrayList<>()).add(sivStartDate);
            }
            if (projectEndDate != null) {
                projectEndDates.computeIfAbsent("projectEndDates", k -> new ArrayList<>()).add(projectEndDate);
            }
            if (ethicsStartDate != null) {
                ethicsStartDates.computeIfAbsent("ethicsStartDates", k -> new ArrayList<>()).add(ethicsStartDate);
            }
            if (contractEndDate != null) {
                contractEndDates.computeIfAbsent("contractEndDates", k -> new ArrayList<>()).add(contractEndDate);
            }
            if (hgrStartDate != null) {
                hgrStartDates.computeIfAbsent("hgrStartDates", k -> new ArrayList<>()).add(hgrStartDate);
            }
        } catch (ParseException e) {
            throw new CodeException(ResultCode.Codes.ERROR, "操作失败");
        }
    }


    private static final Map<String, String> FIELD_MAPPINGS = initializeFieldMappings();

    private static Map<String, String> initializeFieldMappings() {
        // 初始化FIELD_MAPPINGS
        Map<String, String> mappings = new HashMap<>();

        // 立项相关的字段
        putAll(mappings, "立项递交时间",
                "立项递交时间",
                "立项递交实际日期",
                "立项递交日期");

        // 立项完成相关的字段
        putAll(mappings, "立项完成",
                "立项完成",
                "立项完成时间",
                "立项完成完成时间",
                "立项完成实际完成时间");

        // 伦理批件获取相关字段
        putAll(mappings, "伦理批件获取日期",
                "伦理批件获取日期",
                "伦理批件获取实际日期");

        // 伦理递交相关字段
        putAll(mappings, "伦理递交日期",
                "伦理递交日期",
                "首次伦理实际递交日期");

        // 协议签署与递交相关字段
        putAll(mappings, "协议签署完成日期",
                "协议签署完成日期",
                "协议实际签署完成日期");
        putAll(mappings, "协议实际首次递交审核日期",
                "协议实际首次递交审核日期");
        putAll(mappings, "协议递交日期",
                "协议递交日期");

        // 遗传办承诺书相关字段
        putAll(mappings, "遗传办承诺书实际签署完成日期",
                "遗传办承诺书实际签署完成日期");
        putAll(mappings, "遗传办承诺书实际递交日期",
                "遗传办承诺书实际递交日期");

        // 实际启动与交接相关字段
        putAll(mappings, "实际启动日期",
                "实际启动日期",
                "实际交接时间");

        return Collections.unmodifiableMap(mappings);
    }

    private static void putAll(Map<String, String> map, String value, String... keys) {
        for (String key : keys) {
            map.put(key, value);
        }
    }

    // 天数和偏移量
    private void customDaysAndOffset(Map<String, Integer> daysMap, Map<String, Integer> diffDaysMap, String key,
                                     String startDateStr, String endDateStr, String prevEndDateStr, String finishEndDateStr, SimpleDateFormat dateFormat, boolean calculateDiffDays) {
        int days = -1;
        int diffDays = -1;
        if (StringUtils.isNotBlank(startDateStr) && StringUtils.isNotBlank(endDateStr)) {
            days = calculateDays(startDateStr, endDateStr, dateFormat);

        }

        if (StringUtils.isNotBlank(prevEndDateStr) && StringUtils.isNotBlank(finishEndDateStr)) {
            diffDays = calculateDays(prevEndDateStr, finishEndDateStr, dateFormat);
        }

        daysMap.put(key, days);
        diffDaysMap.put(key, diffDays);
    }

    // 计算两个日期之间的天数
    private int calculateDays(String startDateStr, String endDateStr, SimpleDateFormat dateFormat) {
        try {
            Date startDate = parseDate(startDateStr, dateFormat);
            Date endDate = parseDate(endDateStr, dateFormat);
            if (startDate != null && endDate != null) {
                long diff = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
                return (int) diff;
            }
        } catch (ParseException e) {
            e.printStackTrace();
            throw new CodeException(ResultCode.Codes.ERROR, "计算两个日期之间的天数,日期格式错误");
        }
        return -1;
    }

    private Date parseDate(String dateStr, SimpleDateFormat dateFormat) throws ParseException {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        return dateFormat.parse(dateStr);
    }

    // 替换会议类型
    private String replaceMeetingType(String meetingType) {
        if ("立项会".equals(meetingType)) {
            return "Approval";
        } else if ("伦理会".equals(meetingType)) {
            return "Ethics";
        }
        return meetingType;
    }

    //找出指定日期范围内的每一天
    private List<String> findEveryDay(String startDate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<String> days = new ArrayList<>();
        try {
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);
            Calendar cal = Calendar.getInstance();
            cal.setTime(start);
            while (!cal.getTime().after(end)) {
                days.add(sdf.format(cal.getTime()));
                cal.add(Calendar.DATE, 1);
            }
        } catch (ParseException e) {
            log.error("日期解析异常", e);
            throw new CodeException(ResultCode.Codes.ERROR, "日期解析异常!");
        }
        return days;
    }

    private String findFirstDayOfMonth(String yearMonth) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(sdf.parse(yearMonth));
            cal.set(Calendar.DAY_OF_MONTH, 1);
            return sdf.format(cal.getTime()) + "-01";
        } catch (ParseException e) {
            log.error("日期解析异常", e);
            throw new CodeException(ResultCode.Codes.ERROR, "日期解析异常!");
        }
    }

    // 查找某个月的最后一天
    private String findLastDayOfMonth(String yearMonth) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(sdf.parse(yearMonth));
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            return sdf.format(cal.getTime()) + "-" + cal.get(Calendar.DAY_OF_MONTH);
        } catch (ParseException e) {
            log.error("日期解析异常", e);
            throw new CodeException(ResultCode.Codes.ERROR, "日期解析异常!");
        }
    }

    /**
     * 会议中添加具体开会日期(cycle)
     *
     * @param meetings         会议
     * @param formatDatePrefix 格式化日期前缀
     * @param endDate          截止日期
     */
    private void increaseCycleToMeetings(List<CenterMeetingQueryStrRespVO> meetings, String formatDatePrefix, String endDate) {
        for (CenterMeetingQueryStrRespVO meeting : meetings) {
            String finalDate = endDate;
            try {
                if (StringUtils.isNotEmpty(meeting.getEndDate()) && !"Invalid date".equals(meeting.getEndDate())) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date dEnd = sdf.parse(endDate);
                    Date meetingEnd = sdf.parse(meeting.getEndDate());
                    finalDate = dEnd.after(meetingEnd) ? meeting.getEndDate() : endDate;
                }
            } catch (Exception e) {
                log.error("会议截止日期格式错误", e);
                continue;
            }
            // 指定日期
            if ("指定日期".equals(meeting.getRepeatType())) {
                String cycle = splitExpireDateStr(meeting.getRepeatDetails(), finalDate);
                meeting.setCycle(cycle);
            } else if (StringUtils.isEmpty(meeting.getRepeatWeek())) {
                // 指定周期(按月)
                insertCycleByMonth(meeting, formatDatePrefix, finalDate);
            } else {
                // 指定周期(按周)
                insertCycleByWeek(meeting, formatDatePrefix, finalDate);
            }
        }
    }

    /**
     * 生成固定周期内的所有会议(按月)
     *
     * @param meeting          会议
     * @param formatDatePrefix 格式化日期前缀
     * @param endDate          截止日期
     */
    private void insertCycleByMonth(CenterMeetingQueryStrRespVO meeting, String formatDatePrefix, String endDate) {
        if (StringUtils.isEmpty(meeting.getRepeatDetails())) {
            return;
        }
        // 提取 formatDatePrefix 中的年月部分
        String yearMonth = formatDatePrefix.substring(0, 8); // 提取 "yyyy-MM-"
        // 分割重复详情
        String details = meeting.getRepeatDetails().replace("号", "");
        String[] days = details.split(",");
        StringBuilder cycleBuilder = new StringBuilder();
        // 拼接插入cycle
        for (String dayStr : days) {
            int day = Integer.parseInt(dayStr.trim());
            // 格式化日期，确保 day 小于 10 时前面补零
            String formattedDay = (day < 10) ? "0" + day : String.valueOf(day);
            String fullDate = yearMonth + formattedDay;
            // 添加到 cycleBuilder
            if (cycleBuilder.length() > 0) {
                cycleBuilder.append(",");
            }
            cycleBuilder.append(fullDate);
        }
        // 设置 meeting 的 cycle 属性
        meeting.setCycle(splitExpireDateStr(cycleBuilder.toString(), endDate));
    }

    /**
     * 生成固定周期内的所有会议(按周)
     *
     * @param meeting          会议
     * @param formatDatePrefix 格式化日期前缀
     * @param endDate          截止日期
     */
    private void insertCycleByWeek(CenterMeetingQueryStrRespVO meeting, String formatDatePrefix, String endDate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String[] parts = formatDatePrefix.split("-");
            Integer year = Integer.parseInt(parts[0]);
            Integer month = Integer.parseInt(parts[1]);

            // 获取月份的第一天和最后一天
            Calendar cal = Calendar.getInstance();
            cal.set(year, month - 1, 1); // 设置为本月第一天
            int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);

            // 获取月份的第一天是周几（1=周一, 2=周二, ..., 7=周日）
            int startDayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
            int adjustedStartDayOfWeek = (startDayOfWeek == Calendar.SUNDAY) ? 7 : startDayOfWeek - 1;

            String cycle = "";
            for (String week : meeting.getRepeatWeek().split(",")) {
                Integer count = formatWeek(week); // 输入“第二周”应返回1
                for (String weekday : meeting.getRepeatDetails().split(",")) { // 输入“周二”应返回2
                    Integer dayCount = formatWeekDay(weekday);
                    if (dayCount == null) {
                        continue;
                    }

                    // 计算符合条件的日期
                    if (count == 0) { // 第一周
                        // 第一周从月份的第一天开始，持续到该周的周日
                        int firstWeekEndDay = Math.min(lastDay, 7 - adjustedStartDayOfWeek + 1); // 第一周的最后一天
                        if (dayCount >= adjustedStartDayOfWeek && dayCount <= 7) { // 确保当前星期在第一周内
                            int day = dayCount - adjustedStartDayOfWeek + 1; // 计算具体的日期
                            if (day > 0 && day <= firstWeekEndDay) {
                                // 构建日期字符串
                                String currentStrDate = parts[0] + "-" + parts[1] + "-" + (day > 9 ? day : "0" + day);
                                Date currentDate = sdf.parse(currentStrDate);

                                // 只插入最后一天前的日期数据
                                if (currentDate.before(sdf.parse(endDate))) {
                                    cycle += currentStrDate + ",";
                                }
                            }
                        }
                    } else { // 非第一周
                        // 计算第一周的第一个周一的偏移量
                        int offsetFromFirstMonday = (adjustedStartDayOfWeek - 1) % 7;
                        int dayOffset = count * 7 + dayCount - offsetFromFirstMonday;
                        int day = dayOffset; // 计算具体的日期

                        // 如果计算的日期超出了月份的最后一天，则跳过
                        if (day > lastDay || day < 1) { // 确保日期有效
                            continue;
                        }

                        // 构建日期字符串
                        String currentStrDate = parts[0] + "-" + parts[1] + "-" + (day > 9 ? day : "0" + day);
                        Date currentDate = sdf.parse(currentStrDate);

                        // 只插入最后一天前的日期数据
                        if (currentDate.before(sdf.parse(endDate))) {
                            cycle += currentStrDate + ",";
                        }
                    }
                }
            }

            // 移除最后一个逗号
            if (cycle.endsWith(",")) {
                cycle = cycle.substring(0, cycle.length() - 1);
            }

            meeting.setCycle(splitExpireDateStr(cycle, endDate));
        } catch (ParseException e) {
            log.error("固定周期日历生成异常", e);
            throw new CodeException(ResultCode.Codes.ERROR, "固定周期日历生成异常!");
        }
    }

    private Integer formatWeek(String week) {
        return "第一周".equals(week) ? 0 : "第二周".equals(week) ? 1 : "第三周".equals(week) ? 2 : "第四周".equals(week) ? 3 : 4;
    }

    private Integer formatWeekDay(String weekDay) {
        Map day = new HashMap();
        day.put("周一", 1);
        day.put("周二", 2);
        day.put("周三", 3);
        day.put("周四", 4);
        day.put("周五", 5);
        day.put("周六", 6);
        day.put("周日", 7);
        return (Integer) day.get(weekDay);
    }

    /**
     * 过滤日期范围外的日历数据
     *
     * @param dateStr
     * @param expire
     * @return
     */
    private String splitExpireDateStr(String dateStr, String expire) {
        try {
            if (StringUtils.isEmpty(dateStr)) {
                return "";
            }
            String finalDateStr = "";
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date dEnd = sdf.parse(expire);
            for (String date : dateStr.split(",")) {
                Date dFinal = sdf.parse(date);
                if (dFinal.after(dEnd)) {
                    continue;
                }
                finalDateStr += date + ",";
            }
            return StringUtils.isEmpty(finalDateStr) ? "" : finalDateStr.substring(0, finalDateStr.length() - 1);
        } catch (Exception e) {
            log.error("ee", e);
            throw new CodeException(ResultCode.Codes.ERROR, "数据异常!");
        }
    }

    private QueryWrapper<com.r2.bi.entity.dc.Center> buildCenterQueryWrapper(BiCenterRepVO repVO) {
        QueryWrapper<com.r2.bi.entity.dc.Center> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(com.r2.bi.entity.dc.Center::getIsCenter, 1)
                .eq(com.r2.bi.entity.dc.Center::getIsDelete, 0);

        if (StringUtils.isNotEmpty(repVO.getName())) {
            queryWrapper.lambda()
                    .like(com.r2.bi.entity.dc.Center::getCenterName, repVO.getName())
                    .or()
                    .like(com.r2.bi.entity.dc.Center::getAlias, repVO.getName());
        }

        return queryWrapper;
    }

    private QueryWrapper<AssociationTable> tableCenterQueryWrapper(BiCenterRepVO repVO) {
        QueryWrapper<AssociationTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(AssociationTable::getIsDelete, 0);

        if (StringUtils.isNotEmpty(repVO.getName())) {
            queryWrapper.lambda()
                    .like(AssociationTable::getThirdPartyCenterName, repVO.getName())
                    .or()
                    .like(AssociationTable::getMdmCenterName, repVO.getName());
        }

        return queryWrapper;
    }

    private Map<Long, TCenterStartUp> getCenterStartUpMap() {
        QueryWrapper<TCenterStartUp> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TCenterStartUp::getIsDelete, 0);
        List<TCenterStartUp> tCenterStartUps = tCenterStartUpMapper.selectList(queryWrapper);
        return tCenterStartUps.stream()
                .collect(Collectors.toMap(TCenterStartUp::getCenterId, t -> t));
    }

    private Map<Long, TCenterEthics> getCenterEthicsMap() {
        QueryWrapper<TCenterEthics> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TCenterEthics::getIsDelete, 0);
        List<TCenterEthics> tCenterEthics = tCenterEthicsMapper.selectList(queryWrapper);
        return tCenterEthics.stream()
                .collect(Collectors.toMap(TCenterEthics::getCenterId, t -> t));
    }

    private Map<Integer, Center> getCenterMap() {
        QueryWrapper<Center> centerQueryWrapper = new QueryWrapper<>();
        centerQueryWrapper.lambda().eq(Center::getIsDelete, 0);
        List<Center> centers = biCenterDao.selectList(centerQueryWrapper);
        return centers.stream()
                .collect(Collectors.toMap(Center::getId, t -> t));
    }


    private List<BiCenterRespVO> filterInfoCenterRespVOS(List<BiCenterRespVO> infoCenterRespVOS, BiCenterRepVO repVO) {
        if (infoCenterRespVOS == null || repVO == null) {
            return null;
        }
        List<BiCenterRespVO> filteredList = infoCenterRespVOS.stream()
                .filter(info -> matchesCriteria(repVO.getProvince(), info.getProvince()))
                .filter(info -> matchesCriteria(repVO.getCity(), info.getCity()))
                .filter(info -> matchesCriteria(repVO.getCenterRqmt(), info.getCenterRqmt()))
                .filter(info -> matchesCriteria(repVO.getEthicalPre(), info.getEthicalPre()))
                .collect(Collectors.toList());


        // 获取排序字段
        String field = repVO.getField();
        // 获取排序顺序
        String order = repVO.getOrder();
        // 检查 field 是否非空，并且不为空字符串
        if (field != null && !field.trim().isEmpty()) {
            Comparator<BiCenterRespVO> comparator = null;
            // 按 speed 字段排序
            if ("speed".equalsIgnoreCase(field)) {
                comparator = Comparator.comparing(BiCenterRespVO::getSpeed);
            }
            // 如果匹配到有效的排序字段，则继续处理排序顺序
            if (comparator != null) {
                // 如果是降序
                if ("descend".equalsIgnoreCase(order)) {
                    comparator = comparator.reversed();
                }
                // 对过滤后的列表进行排序
                filteredList.sort(comparator);
            }
        }
        return filteredList;
    }


    /**
     * 检查两个字符串是否匹配。
     * 如果过滤条件为空或空字符串，则认为匹配；
     * 否则，只有当两个非空字符串相等时才匹配。
     *
     * @param filterValue 过滤条件值
     * @param actualValue 实际值
     * @return 如果匹配返回true，否则返回false
     */
    private boolean matchesCriteria(String filterValue, String actualValue) {
        return filterValue == null || filterValue.isEmpty() || Objects.equals(filterValue, actualValue);
    }

    private Map<String, List<Map<String, Object>>> getCustomTableDataMap() {
        QueryWrapper<CustomTableData> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CustomTableData::getStatus, 0);
        List<CustomTableData> customTableData = customTableDataMapper.selectList(queryWrapper);
        /**
         * 优先统计2年内数据、没有2年内统计2年前的数据
         * 根据CustomTableData类的content字段里面有项目年份和是否参与统计这二个字段来判断是否剔除
         **/
        customTableData = filterCustomTableData(customTableData);
        Map<String, List<Map<String, Object>>> resultMap = new HashMap<>();
        ObjectMapper objectMapper = new ObjectMapper();
        if (customTableData != null && !customTableData.isEmpty()) {
            for (CustomTableData data : customTableData) {
                String content = data.getContent();
                // 跳过空内容
                if (StringUtils.isBlank(content)) {
                    continue;
                }
                try {
                    JsonNode jsonNode = objectMapper.readTree(content);

                    // 获取中心名称
                    JsonNode centerNameNode = jsonNode.get("中心名称");
                    // 跳过缺少中心名称的数据
                    if (centerNameNode == null || centerNameNode.get("value") == null) {
                        continue;
                    }
                    String centerName = centerNameNode.get("value").asText();
                    // 创建或获取该中心名称对应的记录列表
                    List<Map<String, Object>> recordList = resultMap.computeIfAbsent(centerName, k -> new ArrayList<>());

                    // 创建一个新的记录
                    Map<String, Object> record = new HashMap<>();

                    // 遍历所有字段
                    Iterator<Map.Entry<String, JsonNode>> fields = jsonNode.fields();
                    while (fields.hasNext()) {
                        Map.Entry<String, JsonNode> field = fields.next();
                        String key = field.getKey();
                        JsonNode valueNode = field.getValue();

                        int type = valueNode.get("type").asInt();
                        JsonNode valueField = valueNode.get("value");

                        Object value;
                        switch (type) {
                            // 文本类型
                            case 3:
                                value = valueField.asText();
                                break;
                            // 日期类型
                            case 2:
                                String dateStr = valueField.asText();
                                if (StringUtils.isBlank(dateStr)) {
                                    value = null;
                                } else {
                                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
                                    value = LocalDate.parse(dateStr, formatter);
                                }
                                break;
                            // 数值类型
                            case 1:
                                // 假设数值是double类型，也可以是int等其他类型
                                value = valueField.asDouble();
                                break;
                            default:
                                // 默认处理为文本类型
                                value = valueField.asText();
                        }
                        // 添加键值对到记录中
                        record.put(key, value);
                    }
                    recordList.add(record);
                } catch (Exception e) {
                    log.error("解析自定义表数据出错", e);
                    e.printStackTrace();
                }
            }
        }

        return resultMap;
    }

    private List<BiCenterRespVO> convertToInfoCenterRespVOS(List<com.r2.bi.entity.dc.Center> centers,
                                                            Map<Integer, Center> centerMap,
                                                            Map<Long, TCenterStartUp> centerStartUpMap,
                                                            Map<Long, TCenterEthics> centerEthicsMap,
                                                            Map<String, List<Map<String, Object>>> resultMap,
                                                            List<AssociationTable> associationTables) {

        List<BiCenterRespVO> infoCenterRespVOS = new ArrayList<>();

        for (com.r2.bi.entity.dc.Center ssuCenter : centers) {
            BiCenterRespVO infoCenterRespVO = new BiCenterRespVO();
            BeanUtils.copyProperties(ssuCenter, infoCenterRespVO);

            Center center = centerMap.get(ssuCenter.getId().intValue());
            if (center != null) {
                infoCenterRespVO.setAddress(center.getAddress());
                infoCenterRespVO.setProvince(center.getProvince());
                infoCenterRespVO.setCity(center.getCity());
            }

            TCenterStartUp tCenterStartUp = centerStartUpMap.get(ssuCenter.getId());
            if (tCenterStartUp != null && tCenterStartUp.getCenterRqmt() != null) {
                String centerRqmt = tCenterStartUp.getCenterRqmt();
                if (centerRqmt.contains("开通免费检查账号或者免费检查单")) {
                    infoCenterRespVO.setCenterRqmt("是");
                } else {
                    infoCenterRespVO.setCenterRqmt("否");
                }
            } else {
                infoCenterRespVO.setCenterRqmt("否");
            }

            TCenterEthics tCenterEthics1 = centerEthicsMap.get(ssuCenter.getId());
            if (tCenterEthics1 != null && tCenterEthics1.getEthicalPre() != null) {
                String ethicalPre = tCenterEthics1.getEthicalPre();
                if ("是".equals(ethicalPre)) {
                    infoCenterRespVO.setEthicalPre("是");
                } else {
                    infoCenterRespVO.setEthicalPre("否");
                }
            } else {
                infoCenterRespVO.setEthicalPre("否");
            }

            DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                    .appendOptional(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                    .appendOptional(DateTimeFormatter.ofPattern("yyyy/MM/dd"))
                    .toFormatter();

            List<Map<String, Object>> maps = resultMap.get(ssuCenter.getCenterName());
            boolean foundValidData = false;
            if (maps != null && !maps.isEmpty()) {
                // 计算启动用时，包括无效值
                List<Double> startTimes = maps.stream()
                        .map(record -> {
                            String startupTimeStr = String.valueOf(record.get("实际启动日期"));
                            String endUpTimeStr = String.valueOf(record.get("立项递交时间"));
                            if (startupTimeStr == null || endUpTimeStr == null ||
                                    "null".equals(startupTimeStr) || "null".equals(endUpTimeStr) ||
                                    startupTimeStr.trim().isEmpty() || endUpTimeStr.trim().isEmpty()) {
                                return null; // 有任何一个为空或无效，直接返回null
                            }
                            try {
                                LocalDate startupDate = LocalDate.parse(startupTimeStr, formatter);
                                LocalDate endDate = LocalDate.parse(endUpTimeStr, formatter);

                                long daysBetween = ChronoUnit.DAYS.between(endDate, startupDate);
                                return daysBetween >= 0 ? (double) daysBetween : null;
                            } catch (Exception e) {
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                // 计算平均启动用时，排除无效值
                if(!startTimes.isEmpty()){
                    double averageStartTime = startTimes.stream()
                            .mapToDouble(Double::doubleValue)
                            .average()
                            .orElse(0.0);

                    if (averageStartTime > 0) {
                        double ceilAverageStartTime = Math.ceil(averageStartTime);
                        infoCenterRespVO.setSpeed(ceilAverageStartTime);
                        foundValidData = true;
                    }
                }
            }
            if (!foundValidData) {
                for (AssociationTable table : associationTables) {
                    String thirdPartyCenterName = table.getThirdPartyCenterName();
                    String mdmCenterName = table.getMdmCenterName();
                    if (thirdPartyCenterName != null && mdmCenterName.equals(ssuCenter.getCenterName())) {
                        List<Map<String, Object>> tableMaps = resultMap.get(thirdPartyCenterName);
                        if (tableMaps == null || tableMaps.isEmpty()) {
                            continue;
                        }
                        for (Map<String, Object> tableMap : tableMaps) {
                            if (tableMap != null && !tableMap.isEmpty()) {
                                double averageStartTime = calculateAverageWithInvalidData(tableMaps);
                                if (averageStartTime > 0) {
                                    infoCenterRespVO.setSpeed(averageStartTime);
                                    foundValidData = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            if (!foundValidData) {
                infoCenterRespVO.setSpeed(0.0);
            }

            infoCenterRespVOS.add(infoCenterRespVO);
        }

        return infoCenterRespVOS;
    }

    private double calculateAverageWithInvalidData(List<Map<String, Object>> records) {
        DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                .appendOptional(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                .appendOptional(DateTimeFormatter.ofPattern("yyyy/MM/dd"))
                .toFormatter();

        // 计算有效的启动时长，排除无效项目
        List<Double> validStartTimes = records.stream()
                .map(record -> {
                    String startupTimeStr = String.valueOf(record.get("实际启动日期"));
                    String endUpTimeStr = String.valueOf(record.get("立项递交时间"));

                    // 检查是否为null或空字符串，或者为"null"，则认为是无效数据
                    if (startupTimeStr == null || endUpTimeStr == null ||
                            "null".equals(startupTimeStr) || "null".equals(endUpTimeStr) ||
                            startupTimeStr.trim().isEmpty() || endUpTimeStr.trim().isEmpty()) {
                        return null;
                    }

                    try {
                        LocalDate startupDate = LocalDate.parse(startupTimeStr, formatter);
                        LocalDate endDate = LocalDate.parse(endUpTimeStr, formatter);

                        long daysBetween = ChronoUnit.DAYS.between(endDate, startupDate);
                        return daysBetween >= 0 ? (double) daysBetween : null;
                    } catch (Exception e) {
                        // 如果无法解析为有效的日期，则返回null
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 只有当有有效数据时才计算平均值
        if (validStartTimes.isEmpty()) {
            return 0.0;
        }

        double average = validStartTimes.stream()
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0);

        // 向上取整
        return Math.ceil(average);
    }
}