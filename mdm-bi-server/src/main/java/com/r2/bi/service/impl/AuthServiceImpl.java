package com.r2.bi.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.r2.bi.config.ManagerRealm;
import com.r2.bi.config.shiro.NoPwdToken;
import com.r2.bi.dao.bi.TSysMenuDao;
import com.r2.bi.entity.bi.BiUser;
import com.r2.bi.enums.AuthLoginEnum;
import com.r2.bi.service.SysMdmService;
import com.r2.bi.service.SysUserService;
import com.r2.bi.service.auth.AuthService;
import com.r2.bi.util.Sm2Util;
import com.r2.bi.util.StringUtils;
import com.r2.bi.util.TokenGeneratorUtil;
import com.r2.bi.vo.login.AuthLoginReqVO;
import com.r2.bi.vo.login.AuthLoginRespVO;
import com.r2.bi.vo.login.AuthUpdatePasswordReqVO;
import com.r2.bi.vo.login.AuthUpdatePasswordRespVO;
import com.r2.bi.vo.sysmenu.SysMenuListRespVO;
import com.r2.framework.exception.CodeException;
import com.r2.framework.util.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.mgt.RealmSecurityManager;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.r2.bi.enums.SysUserSourceTypeEnum.EXTERNAL_USERS;

@Service
@Slf4j
public class AuthServiceImpl implements AuthService {


    @Resource
    private SysUserService sysUserService;

    @Resource
    private SysMdmService sysMdmService;

    @Resource
    private TSysMenuDao tsysMenuDao;
    /**
     * sm2密码解密秘钥
     */
    @Value("${password.privateKey}")
    private String privateKey;

    /**
     * token 秘钥
     */
    @Value("${encodeSalt.tokenSalt}")
    private String tokenSalt;


    @Override
    public AuthLoginRespVO thirdLoginBi(String key) {
        AuthLoginRespVO loginResult = new AuthLoginRespVO();
        try {
            //获取工号
            log.info("key:{}", key);
            // 去除首尾空格
            key = key.trim();
            // 检查是否是合法的Base64编码
            if (!Base64.isBase64(key)) {
                throw new CodeException(ResultCode.Codes.ERROR, "非法字符");
            }
            String decryptStr = TokenGeneratorUtil.decrypt(key);
            JSONObject userData = JSONObject.parseObject(decryptStr);

            if (ObjectUtil.isNotEmpty(userData) && userData.get("tokenSalt").equals(tokenSalt)) {
                //查看当前员工是否存在
                String jobNumber = (String) userData.get("jobNumber");
                BiUser userInfo = sysUserService.checkByJobNumber(jobNumber);
                if (ObjectUtil.isEmpty(userInfo)) {
                    throw new CodeException(ResultCode.Codes.ERROR, "用户不存在");
                }

                String menuIds="";
                //获取用户菜单权限
                List<SysMenuListRespVO> userMenuList = tsysMenuDao.getUserMenuList(userInfo.getId());
                if (CollectionUtil.isNotEmpty(userMenuList)) {
                    List<Integer> menuIdsArr = userMenuList.stream().map(item -> item.getId()).collect(Collectors.toList());
                    menuIds = StrUtil.join(",", menuIdsArr);
                }
                // 2：Dashboard,25：中心管理, 29：中心画像,
                List<String> requiredValues = Arrays.asList("25", "29", "2");
                // 过滤字符串并只保留需要的值，在这些值前加上 BI_
                String filteredString = Arrays.stream(menuIds.split(","))
                        .filter(requiredValues::contains)
                        .map(s -> "BI_" + s)
                        .collect(Collectors.joining(","));
                //登录成功
                Subject subject = SecurityUtils.getSubject();
                subject.login(new NoPwdToken(jobNumber));
                loginResult.setIsLogin(true);
                loginResult.setToken((String) subject.getSession().getId());

                Session session = SecurityUtils.getSubject().getSession();
                long lastAccessTime = session.getLastAccessTime().getTime();
                long timeout = session.getTimeout();
                long currentTime = System.currentTimeMillis();
                // 计算剩余时间（毫秒）
                long remainingTimeInMillis = (lastAccessTime + timeout) - currentTime;
                loginResult.setSessionTimeOut((int) remainingTimeInMillis / 1000);
                // 获取当前日期时间
                Date currentDate = new Date();
                // 使用 SimpleDateFormat 来格式化日期时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedDateTime = sdf.format(currentDate);
                loginResult.setCurrentTime(formattedDateTime);
                loginResult.setAuthorityCode(filteredString);
                log.info("token: " + (String) subject.getSession().getId());
            } else {
                throw new CodeException(ResultCode.Codes.ERROR, "参数异常");
            }
        } catch (Exception e) {
            log.info("参数异常:{}", e.getMessage());
            throw new CodeException(ResultCode.Codes.ERROR, e.getMessage());
        }
        clearAuthor();
        return loginResult;
    }

    @Override
    public AuthLoginRespVO login(AuthLoginReqVO vo) {
        AuthLoginRespVO loginResult = new AuthLoginRespVO();

        BiUser user = sysUserService.getByUserAccount(vo.getUserAccount());
        if (user == null) {
            loginResult.setIsLogin(false);
            //登录失败展示失败信息
            loginResult.setCode(AuthLoginEnum.USER_NOT_EXIST.getErrorCode());
            loginResult.setMessage(AuthLoginEnum.USER_NOT_EXIST.getMessage());
            return loginResult;
        }
        Subject subject = SecurityUtils.getSubject();
        //密码解密
        String decryptPwd = Sm2Util.decrypt(privateKey, vo.getPassword());
        //调用主数据平台接口
        JSONObject loginRes = sysMdmService.login(vo.getUserAccount(), decryptPwd);
        Integer code = loginRes.getInteger("errorCode");
        if (AuthLoginEnum.SUCCESS.getErrorCode().equals(code)) {
            subject.login(new NoPwdToken(vo.getUserAccount()));
            loginResult.setIsLogin(true);
            loginResult.setToken((String) subject.getSession().getId());
            loginResult.setSessionTimeOut((int) subject.getSession().getTimeout() / 1000);
            loginResult.setSourceType(user.getSourceType());
            //登录成功是否需要密码过期提示
            loginResult.setCode(AuthLoginEnum.SUCCESS.getErrorCode());
            loginResult.setMessage(StringUtils.defaultString(checkPasswordExpire(user)));
            clearAuthor();
        } else {
            String message = loginRes.getString("message");
            loginResult.setIsLogin(false);
            loginResult.setSourceType(user.getSourceType());
            loginResult.setCode(code);
            //登录失败展示失败信息
            if(AuthLoginEnum.USER_STATUS_ERROR.getErrorCode().equals(code)){
                message = AuthLoginEnum.USER_NOT_EXIST.getMessage();
            }
            loginResult.setMessage(message);
        }
        return loginResult;
}

    @Override
    public Object logout() {
        SecurityUtils.getSubject().logout();
        return null;
    }



    @Override
    public AuthUpdatePasswordRespVO updatePassword(AuthUpdatePasswordReqVO authUpdatePasswordReqVO) {
        AuthUpdatePasswordRespVO authUpdatePasswordRespVO = new AuthUpdatePasswordRespVO();
        //用户是否存在
        BiUser sysUser = sysUserService.getByUserAccount(authUpdatePasswordReqVO.getJobNumber());
        if (sysUser == null) {
            authUpdatePasswordRespVO.setResult(false);
            authUpdatePasswordRespVO.setCode(AuthLoginEnum.USER_NOT_EXIST.getErrorCode());
            authUpdatePasswordRespVO.setMessage(AuthLoginEnum.USER_NOT_EXIST.getMessage());
            return authUpdatePasswordRespVO;
        }
        String newPassword = authUpdatePasswordReqVO.getNewPassword();
        String confirmPassword = authUpdatePasswordReqVO.getConfirmPassword();
        if (!newPassword.equals(confirmPassword)) {
            authUpdatePasswordRespVO.setResult(false);
            authUpdatePasswordRespVO.setCode(AuthLoginEnum.CONFIRM_PASSWORD_NODE_MATCH.getErrorCode());
            authUpdatePasswordRespVO.setMessage(AuthLoginEnum.CONFIRM_PASSWORD_NODE_MATCH.getMessage());
            return authUpdatePasswordRespVO;
        }
        String decrypt = "";
        try{
            decrypt = Sm2Util.decrypt(privateKey, authUpdatePasswordReqVO.getNewPassword());
        }catch (Exception e){
            authUpdatePasswordRespVO.setResult(false);
            authUpdatePasswordRespVO.setCode(AuthLoginEnum.USER_PASSWORD_ERROR.getErrorCode());
            authUpdatePasswordRespVO.setMessage(AuthLoginEnum.USER_PASSWORD_ERROR.getMessage());
            return authUpdatePasswordRespVO;
        }
        //密码解密
        authUpdatePasswordReqVO.setNewPassword(decrypt);
        //调主数据接口
        JSONObject result = sysMdmService.updatePassword(authUpdatePasswordReqVO);
        Integer code = result.getInteger("errorCode");
        if (AuthLoginEnum.SUCCESS.getErrorCode().equals(code)) {
            authUpdatePasswordRespVO.setResult(true);
            authUpdatePasswordRespVO.setCode(AuthLoginEnum.SUCCESS.getErrorCode());
            authUpdatePasswordRespVO.setMessage(AuthLoginEnum.SUCCESS.getMessage());
            return authUpdatePasswordRespVO;
        } else {
            authUpdatePasswordRespVO.setResult(false);
            authUpdatePasswordRespVO.setCode(code);
            authUpdatePasswordRespVO.setMessage(result.getString("message"));
            return authUpdatePasswordRespVO;
        }
    }






    /**
     * @title 刷新用户权限
     */
    public void clearAuthor() {
        RealmSecurityManager rsm = (RealmSecurityManager) SecurityUtils.getSecurityManager();
        ManagerRealm myShiroRealm = (ManagerRealm) rsm.getRealms().iterator().next();
        myShiroRealm.clearCachedAuthorizationInfo();
    }

    /**
     * 密码即将过期提示
     *
     * @param user
     * @return
     */
    private String checkPasswordExpire(BiUser user) {
        Integer sourceType = user.getSourceType();
        String msg = "";
        //外部用户才能提醒密码过期
        if (EXTERNAL_USERS.getSourceType().equals(sourceType)) {
            // 创建一个SimpleDateFormat对象并指定格式
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//            // 使用format方法将Date对象转换为字符串
            String resetDay = user.getResetDay();
            if (StringUtils.isBlank(resetDay)) {
                return msg;
            }
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                //密码更新时间
                LocalDate date = LocalDate.parse(resetDay, formatter);
                //过期日期
                LocalDate localDate = date.plusDays(90);
                //相隔时间
                long daysBetween = ChronoUnit.DAYS.between(LocalDate.now(), localDate);
                if (daysBetween > 0 && daysBetween <= 10) {
                    msg = "你的密码还有" + daysBetween + "天过期，请及时重置密码";
                }
            } catch (Exception e) {
                //日期转换失败不做任何处理
            }
        }
        return msg;
    }
}
