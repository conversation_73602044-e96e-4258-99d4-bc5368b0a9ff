package com.r2.bi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.r2.bi.entity.bi.SysTaskResult;

/**
 * 定时任务执行结果表(SysTaskResult)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-03 16:46:33
 */
public interface SysTaskResultService extends IService<SysTaskResult> {

    /**
     * 新增数据
     *
     * @param sysTaskResult 实例对象
     * @return 实例对象
     */
    void insert(SysTaskResult sysTaskResult);

}
