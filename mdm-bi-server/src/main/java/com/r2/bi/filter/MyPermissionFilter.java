package com.r2.bi.filter;

import com.r2.framework.bean.BaseResponse;
import com.r2.framework.util.ResponseUtil;
import com.r2.framework.util.ResultCode;
import com.r2.framework.util.data.JsonUtil;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authz.AuthorizationFilter;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.util.StringUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class MyPermissionFilter extends AuthorizationFilter {

    @Override
    public boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) throws IOException {
        Subject subject = this.getSubject(request, response);
        String[] perms = (String[])((String[])mappedValue);
        boolean isPermitted = true;
        if (perms != null && perms.length > 0) {
            if (perms.length == 1) {
                if (!subject.isPermitted(perms[0])) {
                    isPermitted = false;
                }
            } else if (!subject.isPermittedAll(perms)) {
                isPermitted = false;
            }
        }

        return isPermitted;
    }

    @Override
    public boolean onAccessDenied(ServletRequest request, ServletResponse response) throws IOException {
        Subject subject = this.getSubject(request,response);
        if(subject.getPrincipal()==null){
            this.saveRequestAndRedirectToLogin(request,response);
        }else{
            HttpServletRequest req = (HttpServletRequest) request;
            HttpServletResponse resp = (HttpServletResponse)response;
            String header = req.getHeader("X-Requested-With");
            if(header!=null&&"XMLHttpRequest".equals(header)){
                HttpServletResponse httpServletResponse = (HttpServletResponse) response;
                httpServletResponse.setCharacterEncoding("UTF-8");
                httpServletResponse.setHeader("Content-type", "application/json;charset=UTF-8");
                BaseResponse commonResult = ResponseUtil.error(ResultCode.Codes.NOT_PERMISSIONS.getCode(), ResultCode.Codes.NOT_PERMISSIONS.getMessage());
                response.getWriter().write(JsonUtil.objectToJson(commonResult));
            }else {
                String unauthorizedUrl = this.getUnauthorizedUrl();
                if (StringUtils.hasText(unauthorizedUrl)) {
                    WebUtils.issueRedirect(request, response, unauthorizedUrl);
                } else {
                    HttpServletResponse httpServletResponse = (HttpServletResponse) response;
                    httpServletResponse.setCharacterEncoding("UTF-8");
                    httpServletResponse.setHeader("Content-type", "application/json;charset=UTF-8");
                    BaseResponse commonResult = ResponseUtil.error(ResultCode.Codes.NOT_PERMISSIONS.getCode(), ResultCode.Codes.NOT_PERMISSIONS.getMessage());
                    response.getWriter().write(JsonUtil.objectToJson(commonResult));
                }
            }
        }
        return false;
    }
}
