package com.r2.bi.filter;

import com.r2.framework.bean.BaseResponse;
import com.r2.framework.util.ResponseUtil;
import com.r2.framework.util.ResultCode;
import com.r2.framework.util.data.JsonUtil;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.AccessControlFilter;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;

public class TokenFilter extends AccessControlFilter {

    @Override
    protected boolean isAccessAllowed(ServletRequest servletRequest, ServletResponse servletResponse, Object o){
        Subject subject = getSubject(servletRequest, servletResponse);
        // 只有完成认证的用户才允许访问
        return subject.isAuthenticated();
    }

    @Override
    protected boolean onAccessDenied(ServletRequest servletRequest, ServletResponse servletResponse) throws Exception {
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-type", "application/json;charset=UTF-8");
        BaseResponse commonResult = ResponseUtil.error(ResultCode.Codes.INVALID_TOKEN.getCode(), ResultCode.Codes.INVALID_TOKEN.getMessage());
        response.getWriter().write(JsonUtil.objectToJson(commonResult));
        return false;
    }
}
