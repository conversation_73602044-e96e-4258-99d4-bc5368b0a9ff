package com.r2.bi.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class TreeUtils {
    /**
     *
     *
     * @param list             源数据
     * @param setChildListFn   设置递归的方法
     * @param idFn             获取id的方法
     * @param pidFn            获取父id的方法
     * @param getRootCondition 获取根节点的提哦啊见
     * @return {@link List< M >}
     */
    public static <M, T> List<M> listToTree(List<M> list, Function<M, T> idFn, Function<M, T> pidFn, BiConsumer<M, List<M>> setChildListFn, Predicate<M> getRootCondition) {
        if (CollectionUtils.isNotEmpty(list)) {
            Map<T, List<M>> listMap = list.stream().collect(Collectors.groupingBy(pidFn));
            list.forEach(model -> setChildListFn.accept(model, listMap.get(idFn.apply(model))));
            return list.stream().filter(getRootCondition).collect(Collectors.toList());
        }
        return null;
    }

    public static <M> List<M> treeToList(List<M> source, Function<M, List<M>> getChildListFn, BiConsumer<M, List<M>> setChildListFn, Predicate<M> getRootCondition) {
        List<M> target = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(source)) {
            treeToList(source, target, getChildListFn);
            target.forEach(model -> setChildListFn.accept(model, null));
            target.addAll(target.stream().filter(getRootCondition).collect(Collectors.toList()));
        }
        return target;
    }

    private static <F> void treeToList(List<F> source, List<F> target, Function<F, List<F>> getChildListFn) {
        if (CollectionUtils.isNotEmpty(source)) {
            target.addAll(source);
            source.forEach(model -> {
                List<F> childList = getChildListFn.apply(model);
                if (CollectionUtils.isNotEmpty(childList)) {
                    treeToList(childList, target, getChildListFn);
                }
            });
        }
    }

    @Data
    @AllArgsConstructor
    @ToString(callSuper = true)
    @NoArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class TestTreeObj {
        private int id;
        private int pid;
        private String name;
        private int sort;
        private List<TestTreeObj> testTreeObj;
    }

}
