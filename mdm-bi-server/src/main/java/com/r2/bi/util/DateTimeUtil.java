package com.r2.bi.util;

import org.apache.logging.log4j.util.Strings;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

public class DateTimeUtil {

    public static final String DATE_FORMAT_MIN = "yyyy-MM-dd HH:mm";

    public static final String DATE_FORMAT_DAY = "yyyy-MM-dd";

    public static final String DATE_FORMAT_SECOND = "yyyy-MM-dd HH:mm:ss";

    public static final String DATE_FORMAT_DAY2 = "yyyyMMdd";

    public static final String DATE_FORMAT_DAY3 = "yyyy/MM/d";

    public static final String START = "start";

    public static final String END = "end";

    public static final String DATE_FORMAT_MONTH = "yyyyMM";

    public static final String DATE_FORMAT_MONTH2 = "yyyy年MM月";

    public static final String DATE_FORMAT_MONTH3 = "yyyy-MM";

    public static final String DATE_TZ_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    // 时间转字符串
    public static String dateToString(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        String dateStr = sdf.format(date);
        return dateStr;
    }

    // 字符串转时间
    public static Date stringToDate(String dateStr, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Date dateFormat(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return sdf.parse(sdf.format(date));
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }


    public static Date today() {
        Date date = new Date();
        return stringToDate(dateToString(date, DATE_FORMAT_DAY), DATE_FORMAT_DAY);
    }


    //获取某个日期的开始时间
    public static Date getDayStartTime(Date d) {
        Calendar calendar = Calendar.getInstance();
        if (null != d) {
            calendar.setTime(d);
        }
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return new Date(calendar.getTimeInMillis());
    }

    //获取某个日期的结束时间
    public static Date getDayEndTime(Date d) {
        Calendar calendar = Calendar.getInstance();
        if (null != d) {
            calendar.setTime(d);
        }
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return new Date(calendar.getTimeInMillis());
    }

    /**
     * date2比date1多的天数
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int differentDays(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2) {//同一年
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0)    //闰年
                {
                    timeDistance += 366;
                } else    //不是闰年
                {
                    timeDistance += 365;
                }
            }

            return timeDistance + (day2 - day1);
        } else {// 不同年
            return day2 - day1;
        }
    }

    /**
     * yyyy-MM-dd
     *
     * @param day -1前 0现在 1后
     * @return
     */
    public static String getDay(Integer day) {
        DateFormat df = new SimpleDateFormat(DATE_FORMAT_DAY);
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, day);
        date = (Date) calendar.getTime();
        return df.format(date);
    }

    /**
     * tz转时间
     *
     * @param dateStr
     * @param format
     * @return
     */
    public static String tzToString(String dateStr, String format) {
        if (Strings.isBlank(dateStr)) {
            return null;
        }
        if (dateStr.contains("T")) {
            DateFormat formattz = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.CHINA);
            formattz.setTimeZone(TimeZone.getTimeZone("UTC"));
            try {
                Date date = formattz.parse(dateStr);
                return DateTimeUtil.dateToString(date, format);
            } catch (Exception e) {
            }
            return null;
        } else {
            return dateToString(stringToDate(dateStr, format), format);
        }
    }


    /**
     * 获取当前时间到凌晨12点的秒数
     *
     * @return
     */
    public static Long getSecondsNextEarlyMorning() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (cal.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }


    public static String removeHms(String date) {
        if (date == null) {
            return "";
        }
        return date.replaceAll(" 00:00:00","");
    }
}
