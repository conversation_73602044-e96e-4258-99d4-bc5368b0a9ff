package com.r2.bi.util;

import com.alibaba.fastjson.JSON;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;

import java.io.InputStreamReader;
import java.io.Reader;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@SuppressWarnings("all to suppress all warnings ")
public class ToolUtil {
    private static final Logger log = LoggerFactory.getLogger(ToolUtil.class);
    /**
     * 毫秒转秒
     * @param time
     * @return
     */
    public static long millisecondToSecond(long time){
        if (time == 0) {
            return 0;
        }
        return time / 1000;
    }

    /**
     * 获取数量
     *
     * @param map
     * @param typeIds
     * @return
     */
    public static Integer getInfoNum(Map<Integer, Integer> map, List<Integer> typeIds) {
        Integer total = 0;
        for (Integer typeId : typeIds) {
            Integer num = map.get(typeId);
            if (num != null) {
                total += num;
            }
        }
        return total;
    }

    /**
     * 获取数量
     *
     * @param map
     * @param typeId
     * @return
     */
    public static Integer getInfoNum(Map<Integer, Integer> map, Integer typeId) {
        Integer num = map.get(typeId);
        return  num == null ? 0 : num;
    }

    /**
     * 生成uuid
     * @return
     */
    public static String getUUId() {
        return UUID.randomUUID().toString().replace("-", "");
    }



    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    public static <T> List<T> page(List<T> dataList, int pageSize, int pageNum) {
        List<T> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dataList)) {
            int limit = (pageNum > 1?(pageNum -1)*pageSize : 0);
            for (int i =0; i<pageSize&&i<dataList.size()-limit;i++) {
                list.add(dataList.get(limit + i));
            }
        }
        return list;
    }


    /**
     * 字符串拼接
     *
     * @param delimiter
     * @param elements
     * @return
     */
    public static String join(CharSequence delimiter, CharSequence... elements) {
        StringJoiner joiner = new StringJoiner(delimiter);
        for (CharSequence cs : elements) {
            if (cs == null || cs.length() == 0) {
                continue;
            }
            joiner.add(cs);
        }
        return joiner.toString();
    }

    /**
     * 获取文件内容
     * @param filepath
     * @return
     */
    public static String getFileText(String filepath){
        StringBuffer sb = new StringBuffer();
        try{
            ClassPathResource classPathResource = new ClassPathResource(filepath);
            Reader reader = new InputStreamReader(classPathResource.getInputStream());
            int ch = 0;
            while ((ch = reader.read())!=-1){
                sb.append((char)ch);
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        return sb.toString().replaceAll("\r\n","");
    }

    /**
     * 分割字符串数组
     *
     * @param list
     * @return
     */
    public static List<String> getChildSingleList(List<String> list) {
        List<String> result = new ArrayList<>();
        for (String str : list) {
            if (Strings.isBlank(str)) {
                continue;
            }
            List<String> childList = Arrays.stream(str.split(","))
                    .filter(a -> Strings.isNotBlank(a)).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(childList)) {
                result.addAll(childList);
            }
        }
        return result.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 分割字符串数组
     *
     * @param list
     * @return
     */
    public static List<String> getChildSingleList(List<String> list, String splitor) {
        List<String> result = new ArrayList<>();
        for (String str : list) {
            if (Strings.isBlank(str)) {
                continue;
            }
            List<String> childList = Arrays.stream(str.split(splitor))
                    .filter(a -> Strings.isNotBlank(a)).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(childList)) {
                result.addAll(childList);
            }
        }
        return result.stream().distinct().collect(Collectors.toList());
    }


    /**
     * 数字补零
     * @param number
     * @param digits
     * @return
     */
    public static String getFormatNumber(int number, Integer digits){
        NumberFormat formatter = NumberFormat.getNumberInstance();
        formatter.setMinimumIntegerDigits(digits);
        formatter.setGroupingUsed(false);
        return formatter.format(number);
    }

    /**
     * 系统时间+随机四位
     * @return
     */
    public static Long getRandomId() {
        String s = System.currentTimeMillis() + "" + (int) (Math.random() * 9000 + 1000);
        log.info("随机id:{}", Long.valueOf(s));
        return Long.valueOf(s);
    }

    /**
     * 判断是否json
     * @param str
     * @return
     */
    public static boolean isJSON2(String str) {
        if (Strings.isBlank(str)) {
            return false;
        }
        boolean result = false;
        try {
            Object obj= JSON.parse(str);
            result = true;
        } catch (Exception e) {
            result=false;
        }
        return result;
    }

    /**
     * 判断是否全是中文
     * @param str
     * @return
     */
    public static boolean isNumeric(String str){
        Pattern pattern = Pattern.compile("[0-9]*");
        return pattern.matcher(str).matches();
    }


    public static boolean compareStringNull(String str1, String str2) {
        if (Strings.isBlank(str1) && Strings.isBlank(str2)) {
            return true;
        }
        return false;
    }
}
