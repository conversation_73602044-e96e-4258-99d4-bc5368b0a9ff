package com.r2.bi.util;


import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class CacheEntry<T> {
    private final T value;
    private final long expirationTime;

    /**
     * 构造一个新的缓存条目。
     *
     * @param value      缓存条目的值
     * @param duration   缓存的有效持续时间
     * @param timeUnit   持续时间的时间单位
     * @throws IllegalArgumentException 如果duration小于等于0
     */
    public CacheEntry(T value, long duration, TimeUnit timeUnit) {
        if (duration <= 0) {
            throw new IllegalArgumentException("持续时间必须大于零.");
        }
        this.value = value;
        this.expirationTime = System.currentTimeMillis() + timeUnit.toMillis(duration);
    }

    /**
     * 获取缓存条目的值。
     *
     * @return 缓存条目的值
     */
    public T getValue() {
        return value;
    }

    /**
     * 检查缓存条目是否已过期。
     *
     * @return 如果缓存条目已过期则返回true，否则返回false
     */
    public boolean isExpired() {
        return System.currentTimeMillis() > expirationTime;
    }

    /**
     * 获取缓存条目的剩余有效时间（以毫秒为单位）。
     *
     * @return 剩余有效时间，如果已过期则返回-1
     */
    public long getRemainingTime() {
        long remaining = expirationTime - System.currentTimeMillis();
        return remaining > 0 ? remaining : -1;
    }
}
