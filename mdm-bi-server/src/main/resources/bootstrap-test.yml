server:
  port: 35008
  servlet:
    context-path: /mdm-bi
spring:
  datasource:
    dynamic:
      primary: bi
      strict: false
      datasource:
        mdm:
          driver-class-name: com.mysql.jdbc.Driver
          jdbc-url: *****************************************************************************************************************************************
          username: root
          password: R2ai@mysql
        bi:
          driver-class-name: com.mysql.jdbc.Driver
          jdbc-url: ******************************************************************************************************************************************
          username: root
          password: Mysql#r2368
        cs:
          driver-class-name: com.mysql.jdbc.Driver
          jdbc-url: *******************************************************************************************************************************************
          username: root
          password: Mysql#r2368
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5 #连接池初始化大小
      min-idle: 10 # 最小空闲连接数
      max-active: 150 # 最大连接数
      remove-abandoned: true # 超过时间限制是否回收
      remove-abandoned-timeout: 90  # 超时时间
      web-stat-filter:
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"     # 不统计这些请求数据
    hikari:
      read-only:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  mail:
    # 配置 SMTP 服务器地址
    host: smtp.163.com
    # 发送者邮箱
    username: <EMAIL>
    # 配置密码，注意不是真正的密码，而是刚刚申请到的授权码
    password: KPEQBFNHGGYKTWFJ
    # 端口号465或587或25
    port: 25
    # 默认的邮件编码为UTF-8
    default-encoding: UTF-8
    # 配置SSL 加密工厂
    properties:
      mail:
        smtp:
          socketFactoryClass: javax.net.ssl.SSLSocketFactory
        #表示开启 DEBUG 模式，这样，邮件发送过程的日志会在控制台打印出来，方便排查错误
        debug: true
  resources:
    static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${upload.filePathPrefix}

redisson:
  enable: true
  mode: single
  address: redis://***********:6379
  connectTimeout: 1000
  idleConnectionTimeout: 1000
  timeout: 2000
  password: D1zBa9CsY43d0
  clientName: ${spring.application.name}
  database: 2
  connectionPoolSize: 100
  connectionMinimumIdleSize: 50
  masterConnectionMinimumIdleSize: 32
  masterConnectionPoolSize: 64

logging:
  level:
    com.r2: debug
  config: classpath:logback-spring.xml
  path: /data/logs/${spring.application.name}/

shiro:
  sessionTimeOut: 14400000

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml

upload:
  filePathPrefix: /data/uploadFile/
  fileSuffix: .pdf,.xls,.xlsx,.csv,.doc,.docx,.ppt,.pptx,.png,.jpg,.jpeg,.heic

aliyun:
  accessKeyId: LTAIN6saMetY9yoM
  secret: BwsoTmKP9CMF4lylja2fQokMKKZuBU
  signName: 新研招募
  templateCode: SMS_205467742

ewsEmail:
  hostname: owa.pharmaron-bj.com
  username: DIT.Dev1-Clin
  password: 2Vo$w9dI

ldap:
  url: ldap://*********:389
  basedn: "DC=phc-ts,DC=local"
  baseUserDn: "@PHC-TS.local"

middle:
  url: http://*********:8000
#  url: http://b12048d.r16.cpolar.top

db:
  name: dc
  crm: crm

encodeSalt:
  tokenSalt: i-tf9u5d9m3srHKG

mdm:
  url: http://***********:18081/mdm-admin
  tokenSalt: UPewI6UlaY5wSK4n
  ssoUrl: http://***********:35002/sso-server

bi:
  url: http://bitest.pharmaron-bj.com
  ticketUrl: https://cas.pharmaron-bj.com
  username: c03110
  password: Axiao@123

password:
  privateKey: 00E3AC51470248CA73136068FC7BBB80F03320B33DCDD10DED6CC6BC2912F59F9E
