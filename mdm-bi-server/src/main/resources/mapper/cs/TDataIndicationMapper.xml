<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.cs.TDataIndicationMapper">

    <resultMap id="BaseResultMap" type="com.r2.bi.entity.cs.TDataIndication">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="fieldId" column="field_id" jdbcType="INTEGER"/>
            <result property="nameCn" column="name_cn" jdbcType="VARCHAR"/>
            <result property="nameEn" column="name_en" jdbcType="VARCHAR"/>
            <result property="abbreviation" column="abbreviation" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,field_id,name_cn,
        name_en,abbreviation,status,
        is_delete,create_time,update_time,
        create_by,update_by
    </sql>
</mapper>
