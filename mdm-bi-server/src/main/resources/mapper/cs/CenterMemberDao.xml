<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.cs.BiCenterMemberDao">

    <resultMap type="com.r2.bi.vo.bi.centermember.BiCenterMemberRespVO" id="CenterMemberMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="centerId" column="center_id" jdbcType="INTEGER"/>
        <result property="tel" column="tel" jdbcType="VARCHAR"/>
        <result property="corporation" column="corporation" jdbcType="VARCHAR"/>
        <result property="contactType" column="contact_type" jdbcType="VARCHAR"/>
        <result property="contactName" column="contactName" jdbcType="VARCHAR"/>
        <result property="role" column="role" jdbcType="VARCHAR"/>
        <result property="roleRemark" column="role_remark" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listByCond" resultType="com.r2.bi.vo.bi.centermember.BiCenterMemberRespVO">
        select
        cm.id, cm.center_id, cm.name, cm.dept_id, cm.gender, cm.title, cm.position,
        <choose>
            <when test="(query.contactTypeNotNull != null and query.contactTypeNotNull == 1)">
                r.contact_type,
            </when>
            <otherwise>
                cm.contact_type,
            </otherwise>
        </choose>
        cm.corporation, cm.tel, cm.email, cm.remark, cm.modifiedByName, cm.role,
        cm.role_remark
        from t_center_member cm
        <if test="(query.contactTypeNotNull != null and query.contactTypeNotNull == 1)">
            join t_center_member_contact_type_role r on r.member_id = cm.id and r.center_id = cm.center_id
        </if>
        <where>
            cm.is_delete = 0
            <if test="query.centerId != null and query.centerId != ''">
                and cm.center_id = #{query.centerId}
            </if>
            <if test="query.contactTypeNotNull != null and query.contactTypeNotNull == 1">
                and r.contact_type is not null
                and r.contact_type != ''
            </if>
        </where>
        order by cm.dateCreated desc
        LIMIT #{start}, #{size}
    </select>

    <select id="totalCount" resultType="java.lang.Integer">
        select
        count(*)
        from t_center_member cm
        <if test="(query.contactTypeNotNull != null and query.contactTypeNotNull == 1)">
            join t_center_member_contact_type_role r on r.member_id = cm.id and r.center_id = cm.center_id
        </if>
        <where>
            and cm.is_delete = 0
            <if test="query.centerId != null and query.centerId != ''">
                and cm.center_id = #{query.centerId}
            </if>
            <if test="query.contactTypeNotNull != null and query.contactTypeNotNull == 1">
                and r.contact_type != ''
                and r.contact_type IS NOT NULL
            </if>
</where>
    </select>
</mapper>

