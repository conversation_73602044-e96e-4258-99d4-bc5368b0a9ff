<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.cs.TCenterEthicsMapper">

    <resultMap id="BaseResultMap" type="com.r2.bi.entity.cs.TCenterEthics">
            <id property="ethicsId" column="ethics_id" jdbcType="BIGINT"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
            <result property="deleteFlag" column="delete_flag" jdbcType="VARCHAR"/>
            <result property="centerId" column="center_id" jdbcType="BIGINT"/>
            <result property="processTimeline" column="process_timeline" jdbcType="VARCHAR"/>
            <result property="workflow" column="workflow" jdbcType="VARCHAR"/>
            <result property="downloadEthicsMaterials" column="download_ethics_materials" jdbcType="VARCHAR"/>
            <result property="ethicalApprovalRequirements" column="ethical_approval_requirements" jdbcType="VARCHAR"/>
            <result property="leaderApproval" column="leader_approval" jdbcType="VARCHAR"/>
            <result property="withPpt" column="with_ppt" jdbcType="VARCHAR"/>
            <result property="pptOutlines" column="ppt_outlines" jdbcType="VARCHAR"/>
            <result property="allowQuickVerify" column="allow_quick_verify" jdbcType="VARCHAR"/>
            <result property="meetingWait" column="meeting_wait" jdbcType="VARCHAR"/>
            <result property="meetingWaitInfo" column="meeting_wait_info" jdbcType="VARCHAR"/>
            <result property="waitTime" column="wait_time" jdbcType="VARCHAR"/>
            <result property="cutLineWill" column="cut_line_will" jdbcType="VARCHAR"/>
            <result property="allowAddlEc" column="allow_addl_ec" jdbcType="VARCHAR"/>
            <result property="prstRqmt" column="prst_rqmt" jdbcType="VARCHAR"/>
            <result property="reviewPaymentRemark" column="review_payment_remark" jdbcType="VARCHAR"/>
            <result property="subscribe" column="subscribe" jdbcType="VARCHAR"/>
            <result property="subscribeType" column="subscribe_type" jdbcType="VARCHAR"/>
            <result property="subscribeTypeInfo" column="subscribe_type_info" jdbcType="VARCHAR"/>
            <result property="staffAsk" column="staff_ask" jdbcType="VARCHAR"/>
            <result property="subscribeAsk" column="subscribe_ask" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="plusStaffRemark" column="plus_staff_remark" jdbcType="VARCHAR"/>
            <result property="report" column="report" jdbcType="VARCHAR"/>
            <result property="reportRemark" column="report_remark" jdbcType="VARCHAR"/>
            <result property="reportAsk" column="report_ask" jdbcType="VARCHAR"/>
            <result property="accompany" column="accompany" jdbcType="VARCHAR"/>
            <result property="accompanyType" column="accompany_type" jdbcType="VARCHAR"/>
            <result property="accompanyRemark" column="accompany_remark" jdbcType="VARCHAR"/>
            <result property="eAuditType" column="e_audit_type" jdbcType="VARCHAR"/>
            <result property="systemAuditType" column="system_audit_type" jdbcType="VARCHAR"/>
            <result property="systemAuditLink" column="system_audit_link" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="emailAuditAddress" column="email_audit_address" jdbcType="VARCHAR"/>
            <result property="emailReply" column="email_reply" jdbcType="VARCHAR"/>
            <result property="dataSendRequire" column="data_send_require" jdbcType="VARCHAR"/>
            <result property="auditRemark" column="audit_remark" jdbcType="VARCHAR"/>
            <result property="ethicalApprovalRequirementsRemark" column="ethical_approval_requirements_remark" jdbcType="VARCHAR"/>
            <result property="leaderApprovalRemark" column="leader_approval_remark" jdbcType="VARCHAR"/>
            <result property="rateRemark" column="rate_remark" jdbcType="VARCHAR"/>
            <result property="projectNum" column="project_num" jdbcType="INTEGER"/>
            <result property="abutmentStaff" column="abutment_staff" jdbcType="VARCHAR"/>
            <result property="abutmentStaffRemark" column="abutment_staff_remark" jdbcType="VARCHAR"/>
            <result property="point" column="point" jdbcType="VARCHAR"/>
            <result property="plus" column="plus" jdbcType="VARCHAR"/>
            <result property="plusCondition" column="plus_condition" jdbcType="VARCHAR"/>
            <result property="ethicalPre" column="ethical_pre" jdbcType="VARCHAR"/>
            <result property="ethicalPrecondition" column="ethical_precondition" jdbcType="VARCHAR"/>
            <result property="submit" column="submit" jdbcType="VARCHAR"/>
            <result property="duration" column="duration" jdbcType="VARCHAR"/>
            <result property="ecTime" column="ec_time" jdbcType="VARCHAR"/>
            <result property="accessoryId" column="accessory_id" jdbcType="VARCHAR"/>
            <result property="dataPreAudit" column="data_pre_audit" jdbcType="VARCHAR"/>
            <result property="paperDataStaffRequire" column="paper_data_staff_require" jdbcType="VARCHAR"/>
            <result property="preAudit" column="pre_audit" jdbcType="VARCHAR"/>
            <result property="dataType" column="data_type" jdbcType="VARCHAR"/>
            <result property="dataNum" column="data_num" jdbcType="VARCHAR"/>
            <result property="submitStaff" column="submit_staff" jdbcType="VARCHAR"/>
            <result property="submitTime" column="submit_time" jdbcType="VARCHAR"/>
            <result property="bindAsk" column="bind_ask" jdbcType="VARCHAR"/>
            <result property="ppt" column="ppt" jdbcType="VARCHAR"/>
            <result property="pptUrls" column="ppt_urls" jdbcType="VARCHAR"/>
            <result property="submitAddress" column="submit_address" jdbcType="VARCHAR"/>
            <result property="askRemark" column="ask_remark" jdbcType="VARCHAR"/>
            <result property="reviewPaymentDeadline" column="review_payment_deadline" jdbcType="VARCHAR"/>
            <result property="reviewInvoice" column="review_invoice" jdbcType="VARCHAR"/>
            <result property="attachment" column="attachment" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="allowQuickVerifyRemark" column="allow_quick_verify_remark" jdbcType="VARCHAR"/>
            <result property="ethicsFreqDes" column="ethics_freq_des" jdbcType="VARCHAR"/>
            <result property="charge" column="charge" jdbcType="VARCHAR"/>
            <result property="firstInfluence" column="first_influence" jdbcType="VARCHAR"/>
            <result property="firstPaymentRemark" column="first_payment_remark" jdbcType="VARCHAR"/>
            <result property="firstPaymentDeadline" column="first_payment_deadline" jdbcType="VARCHAR"/>
            <result property="firstInvoice" column="first_invoice" jdbcType="VARCHAR"/>
            <result property="reviewFee" column="review_fee" jdbcType="VARCHAR"/>
            <result property="auditType" column="audit_type" jdbcType="VARCHAR"/>
            <result property="reviewInfluence" column="review_influence" jdbcType="VARCHAR"/>
            <result property="meetingPerCond" column="meeting_per_cond" jdbcType="VARCHAR"/>
            <result property="meetingPerCondRemark" column="meeting_per_cond_remark" jdbcType="VARCHAR"/>
            <result property="ethicalMeetingRequire" column="ethical_meeting_require" jdbcType="VARCHAR"/>
            <result property="ecResultTime" column="ec_result_time" jdbcType="VARCHAR"/>
            <result property="openDay" column="open_day" jdbcType="VARCHAR"/>
            <result property="openDayRequire" column="open_day_require" jdbcType="VARCHAR"/>
            <result property="ethicalDataAccountRequire" column="ethical_data_account_require" jdbcType="VARCHAR"/>
            <result property="ethicalDataNetworkRequire" column="ethical_data_network_require" jdbcType="VARCHAR"/>
            <result property="onlineAuditProcess" column="online_audit_process" jdbcType="VARCHAR"/>
            <result property="paperData" column="paper_data" jdbcType="VARCHAR"/>
            <result property="noCrcRemark" column="no_crc_remark" jdbcType="VARCHAR"/>
            <result property="ethicsDataDeadline" column="ethics_data_deadline" jdbcType="VARCHAR"/>
            <result property="dataSendRemark" column="data_send_remark" jdbcType="VARCHAR"/>
            <result property="stampAsk" column="stamp_ask" jdbcType="VARCHAR"/>
            <result property="ethicsAuditType" column="ethics_audit_type" jdbcType="VARCHAR"/>
            <result property="urgentFee" column="urgent_fee" jdbcType="VARCHAR"/>
            <result property="urgentFeeAhead" column="urgent_fee_ahead" jdbcType="VARCHAR"/>
            <result property="quickFee" column="quick_fee" jdbcType="VARCHAR"/>
            <result property="quickFeeAhead" column="quick_fee_ahead" jdbcType="VARCHAR"/>
            <result property="paymentCardNo" column="payment_card_no" jdbcType="VARCHAR"/>
            <result property="attachmentRemark" column="attachment_remark" jdbcType="VARCHAR"/>
            <result property="pptUrlsRemark" column="ppt_urls_remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ethics_id,is_delete,delete_flag,
        center_id,process_timeline,workflow,
        download_ethics_materials,ethical_approval_requirements,leader_approval,
        with_ppt,ppt_outlines,allow_quick_verify,
        meeting_wait,meeting_wait_info,wait_time,
        cut_line_will,allow_addl_ec,prst_rqmt,
        review_payment_remark,subscribe,subscribe_type,
        subscribe_type_info,staff_ask,subscribe_ask,
        create_time,update_time,create_by,
        update_by,plus_staff_remark,report,
        report_remark,report_ask,accompany,
        accompany_type,accompany_remark,e_audit_type,
        system_audit_type,system_audit_link,email,
        email_audit_address,email_reply,data_send_require,
        audit_remark,ethical_approval_requirements_remark,leader_approval_remark,
        rate_remark,project_num,abutment_staff,
        abutment_staff_remark,point,plus,
        plus_condition,ethical_pre,ethical_precondition,
        submit,duration,ec_time,
        accessory_id,data_pre_audit,paper_data_staff_require,
        pre_audit,data_type,data_num,
        submit_staff,submit_time,bind_ask,
        ppt,ppt_urls,submit_address,
        ask_remark,review_payment_deadline,review_invoice,
        attachment,remark,allow_quick_verify_remark,
        ethics_freq_des,charge,first_influence,
        first_payment_remark,first_payment_deadline,first_invoice,
        review_fee,audit_type,review_influence,
        meeting_per_cond,meeting_per_cond_remark,ethical_meeting_require,
        ec_result_time,open_day,open_day_require,
        ethical_data_account_require,ethical_data_network_require,online_audit_process,
        paper_data,no_crc_remark,ethics_data_deadline,
        data_send_remark,stamp_ask,ethics_audit_type,
        urgent_fee,urgent_fee_ahead,quick_fee,
        quick_fee_ahead,payment_card_no,attachment_remark,
        ppt_urls_remark
    </sql>
</mapper>
