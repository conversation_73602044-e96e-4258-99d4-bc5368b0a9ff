<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.cs.InitSmoDataAreaRelateMapper">

    <resultMap id="BaseResultMap" type="com.r2.bi.entity.cs.InitSmoDataAreaRelate">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="areaName" column="area_name" jdbcType="VARCHAR"/>
            <result property="cta" column="cta" jdbcType="VARCHAR"/>
            <result property="dept" column="dept" jdbcType="VARCHAR"/>
            <result property="director" column="director" jdbcType="VARCHAR"/>
            <result property="cohort" column="cohort" jdbcType="VARCHAR"/>
            <result property="province" column="province" jdbcType="VARCHAR"/>
            <result property="provinceDirector" column="province_director" jdbcType="VARCHAR"/>
            <result property="tl" column="tl" jdbcType="VARCHAR"/>
            <result property="city" column="city" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,area_name,cta,
        dept,director,cohort,
        province,province_director,tl,
        city
    </sql>
</mapper>
