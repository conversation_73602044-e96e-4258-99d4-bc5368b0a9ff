<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.cs.CenterMeetingDao">

    <resultMap type="com.r2.bi.entity.cs.CenterMeeting" id="CenterMeetingResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="VARCHAR"/>
        <result property="datecreated" column="dateCreated" jdbcType="TIMESTAMP"/>
        <result property="datemodified" column="dateModified" jdbcType="TIMESTAMP"/>
        <result property="createdby" column="createdBy" jdbcType="VARCHAR"/>
        <result property="createdbyname" column="createdByName" jdbcType="VARCHAR"/>
        <result property="modifiedby" column="modifiedBy" jdbcType="VARCHAR"/>
        <result property="modifiedbyname" column="modifiedByName" jdbcType="VARCHAR"/>
        <result property="centerId" column="center_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="period" column="period" jdbcType="VARCHAR"/>
        <result property="repeatType" column="repeat_type" jdbcType="VARCHAR"/>
        <result property="repeatNum" column="repeat_num" jdbcType="VARCHAR"/>
        <result property="repeatDetails" column="repeat_details" jdbcType="VARCHAR"/>
        <result property="startDate" column="start_date" jdbcType="VARCHAR"/>
        <result property="endDate" column="end_date" jdbcType="VARCHAR"/>
        <result property="repeatWeek" column="repeat_week" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap type="com.r2.bi.vo.centermeeting.CenterMeetingListRespVO" id="CenterMeetingListRespVOResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="VARCHAR"/>
        <result property="datecreated" column="dateCreated" jdbcType="TIMESTAMP"/>
        <result property="datemodified" column="dateModified" jdbcType="TIMESTAMP"/>
        <result property="createdby" column="createdBy" jdbcType="VARCHAR"/>
        <result property="createdbyname" column="createdByName" jdbcType="VARCHAR"/>
        <result property="modifiedby" column="modifiedBy" jdbcType="VARCHAR"/>
        <result property="modifiedbyname" column="modifiedByName" jdbcType="VARCHAR"/>
        <result property="centerId" column="center_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="repeatType" column="repeat_type" jdbcType="VARCHAR"/>
        <result property="repeatNum" column="repeat_num" jdbcType="VARCHAR"/>
        <result property="startDate" column="start_date" jdbcType="VARCHAR"/>
        <result property="endDate" column="end_date" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap type="com.r2.bi.vo.centermeeting.CenterMeetingQueryStrRespVO" id="CenterMeetingQueryStrRespVOResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="centerId" column="center_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="repeatType" column="repeat_type" jdbcType="VARCHAR"/>
        <result property="repeatNum" column="repeat_num" jdbcType="VARCHAR"/>
        <result property="startDate" column="start_date" jdbcType="VARCHAR"/>
        <result property="endDate" column="end_date" jdbcType="VARCHAR"/>
        <result property="repeatWeek" column="repeat_week" jdbcType="VARCHAR"/>
        <result property="repeatDetails" column="repeat_details" jdbcType="VARCHAR"/>
        <result property="period" column="period" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="selectCenterMeetingVo">
        select *
        from t_center_meeting t
    </sql>

    <!-- 查询中心机构会议列表 -->
    <select id="queryByInitialCenterId" parameterType="Long" resultMap="CenterMeetingListRespVOResult">
        <include refid="selectCenterMeetingVo"/>
        where t.is_delete = 0
        and t.center_id = #{centerId}
        and t.type = '立项会'
        order by t.id
    </select>

    <!-- 查询中心伦理会议列表 -->
    <select id="queryByEthicCenterId" parameterType="Long" resultMap="CenterMeetingListRespVOResult">
        <include refid="selectCenterMeetingVo"/>
        where t.is_delete = 0
        and t.center_id = #{centerId}
        and t.type = '伦理会'
        order by t.id
    </select>

    <!-- 查询中心合同会议列表 -->
    <select id="queryByContractCenterId" parameterType="Long" resultMap="CenterMeetingListRespVOResult">
        <include refid="selectCenterMeetingVo"/>
        where t.is_delete = 0
        and t.center_id = #{centerId}
        and t.type = '合同会'
        order by t.id
    </select>

    <!-- 根据中心和类型查询立项会议表 -->
    <select id="queryInitialStrByCenterId" parameterType="Long" resultMap="CenterMeetingQueryStrRespVOResult">
        select m.id, m.center_id, m.start_date, m.end_date, m.repeat_type, m.repeat_num, m.type, tcmc.period, tcmc.repeat_details, tcmc.repeat_week
        from t_center_meeting m
        left join t_center_meeting_cycle tcmc on m.id = tcmc.meeting_id
        where m.center_id = #{centerId}
        and m.type='立项会'
        and m.is_delete = 0
        order by m.id
    </select>

    <!-- 根据中心和类型查询伦理会议表 -->
    <select id="queryEthicsStrByCenterId" parameterType="Long" resultMap="CenterMeetingQueryStrRespVOResult">
        select m.id, m.center_id, m.start_date, m.end_date, m.repeat_type, m.repeat_num, m.type, tcmc.period, tcmc.repeat_details, tcmc.repeat_week
        from t_center_meeting m
        left join t_center_meeting_cycle tcmc on m.id = tcmc.meeting_id
        where m.center_id = #{centerId}
        and m.type='伦理会'
        and m.is_delete = 0
        order by m.id
    </select>

    <!-- 根据中心和类型查询合同会议表 -->
    <select id="queryContractStrByCenterId" parameterType="Long" resultMap="CenterMeetingQueryStrRespVOResult">
        select m.id, m.center_id, m.start_date, m.end_date, m.repeat_type, m.repeat_num, m.type, tcmc.period, tcmc.repeat_details, tcmc.repeat_week
        from t_center_meeting m
        left join t_center_meeting_cycle tcmc on m.id = tcmc.meeting_id
        where m.center_id = #{centerId}
        and m.type='合同会'
        and m.is_delete = 0
        order by m.id
    </select>

    <!-- 根据条件获取计数 -->
    <select id="getCount" resultType="Integer">
		select count() from t_center_meeting ct
		where ct.is_delete = 0
	</select>

    <!-- 根据日期范围查询会议列表 -->
    <select id="queryByDateScope" parameterType="String" resultMap="CenterMeetingQueryStrRespVOResult">
        select m.id, m.center_id, tc.center_name,tc.standard_code,tc.alias,tc.province,tc.city, m.start_date, m.end_date, m.repeat_type, m.repeat_num, m.type, tcmc.period, tcmc.repeat_details, tcmc.repeat_week
        from t_center_meeting m
        left join t_center_meeting_cycle tcmc on m.id = tcmc.meeting_id
        left join t_center tc on m.center_id = tc.id
        where m.is_delete = 0
        and (m.type = '立项会' OR m.type = '伦理会')
        and (m.end_date = '' OR m.end_date IS NULL OR m.end_date <![CDATA[ >= ]]> DATE_FORMAT(#{startDate}, '%Y-%m-%d'))
    </select>

    <update id="disableCycles" parameterType="com.r2.bi.entity.cs.CenterMeeting">
        update t_center_meeting_cycle mc
        LEFT JOIN t_center_meeting m ON mc.meeting_id = m.id
        <set>
            mc.modifiedBy = #{modifiedby},
            mc.modifiedByName = #{modifiedbyname},
            mc.is_delete = 1,
            mc.dateModified = #{datemodified}
        </set>
        where m.center_id= #{centerId}
    </update>

    <!-- 批量插入meeting -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into t_center_meeting(is_delete, delete_flag, modifiedByName, modifiedBy, createdByName, createdBy, dateModified, dateCreated, center_id, type, repeat_type, repeat_num, start_date, end_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.isDelete}, #{entity.deleteFlag}, #{entity.modifiedbyname}, #{entity.modifiedby}, #{entity.createdbyname}, #{entity.createdby}, #{entity.datemodified}, #{entity.datecreated}, #{entity.centerId}, #{entity.type}, #{entity.repeatType}, #{entity.repeatNum}, #{entity.startDate}, #{entity.endDate})
        </foreach>
    </insert>

    <!-- 批量插入meeting 周期 -->
    <insert id="insertCycleBatch" keyProperty="id" useGeneratedKeys="true">
        insert into t_center_meeting_cycle(is_delete, delete_flag, modifiedByName, modifiedBy, createdByName, createdBy, dateModified, dateCreated, meeting_id, period, repeat_details, repeat_week)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.isDelete}, #{entity.deleteFlag}, #{entity.modifiedbyname}, #{entity.modifiedby}, #{entity.createdbyname}, #{entity.createdby}, #{entity.datemodified}, #{entity.datecreated}, #{entity.meetingId}, #{entity.period}, #{entity.repeatDetails}, #{entity.repeatWeek})
        </foreach>
    </insert>
</mapper>
