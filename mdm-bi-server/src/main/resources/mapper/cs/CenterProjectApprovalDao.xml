<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.cs.CenterProjectApprovalDao">

    <resultMap type="com.r2.bi.entity.cs.CenterProjectApproval" id="CenterProjectApprovalResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="VARCHAR"/>
        <result property="modifiedbyname" column="modifiedByName" jdbcType="VARCHAR"/>
        <result property="modifiedby" column="modifiedBy" jdbcType="VARCHAR"/>
        <result property="createdbyname" column="createdByName" jdbcType="VARCHAR"/>
        <result property="createdby" column="createdBy" jdbcType="VARCHAR"/>
        <result property="datemodified" column="dateModified" jdbcType="TIMESTAMP"/>
        <result property="datecreated" column="dateCreated" jdbcType="TIMESTAMP"/>
        <result property="centerId" column="center_id" jdbcType="INTEGER"/>
        <result property="contact" column="contact" jdbcType="VARCHAR"/>
        <result property="newResearchOrder" column="new_research_order" jdbcType="VARCHAR"/>
        <result property="newResearchWay" column="new_research_way" jdbcType="VARCHAR"/>
        <result property="newResearcherRqmt" column="new_researcher_rqmt" jdbcType="VARCHAR"/>
        <result property="newResearcherRqmtInfo" column="new_researcher_rqmt_info" jdbcType="VARCHAR"/>
        <result property="simultaneously" column="simultaneously" jdbcType="VARCHAR"/>
        <result property="fisrtUndertakeQual" column="fisrt_undertake_qual" jdbcType="VARCHAR"/>
        <result property="piProjNumLimit" column="pi_proj_num_limit" jdbcType="VARCHAR"/>
        <result property="piProjNumLimitInfo" column="pi_proj_num_limit_info" jdbcType="VARCHAR"/>
        <result property="projRqmt" column="proj_rqmt" jdbcType="VARCHAR"/>
        <result property="projRqmtInfo" column="proj_rqmt_info" jdbcType="VARCHAR"/>
        <result property="smoRqmt" column="smo_rqmt" jdbcType="VARCHAR"/>
        <result property="smoRqmtAppendix" column="smo_rqmt_appendix" jdbcType="VARCHAR"/>
        <result property="smoChooseRqmt" column="smo_choose_rqmt" jdbcType="VARCHAR"/>
        <result property="craRqmt" column="cra_rqmt" jdbcType="VARCHAR"/>
        <result property="craRqmtInfo" column="cra_rqmt_info" jdbcType="VARCHAR"/>
        <result property="preRequisitesInfo" column="pre_requisites_info" jdbcType="VARCHAR"/>
        <result property="projectApprovalCycle" column="project_approval_cycle" jdbcType="VARCHAR"/>
        <result property="workflow" column="workflow" jdbcType="VARCHAR"/>
        <result property="ethiauditOrder" column="ethiaudit_order" jdbcType="VARCHAR"/>
        <result property="isGroupLeaderFile" column="is_group_leader_file" jdbcType="VARCHAR"/>
        <result property="auditRequirements" column="audit_requirements" jdbcType="VARCHAR"/>
        <result property="downFileUrl" column="down_file_url" jdbcType="VARCHAR"/>
        <result property="preTrialWay" column="pre_trial_way" jdbcType="VARCHAR"/>
        <result property="verifyOnlineWay" column="verify_online_way" jdbcType="VARCHAR"/>
        <result property="systemLink" column="system_link" jdbcType="VARCHAR"/>
        <result property="emailAddr" column="email_addr" jdbcType="VARCHAR"/>
        <result property="verifyContactWay" column="verify_contact_way" jdbcType="VARCHAR"/>
        <result property="emailReplyFreq" column="email_reply_freq" jdbcType="VARCHAR"/>
        <result property="fileSendRqmt" column="file_send_rqmt" jdbcType="VARCHAR"/>
        <result property="paperSubmitRqmt" column="paper_submit_rqmt" jdbcType="VARCHAR"/>
        <result property="auditWay" column="audit_way" jdbcType="VARCHAR"/>
        <result property="verifyFreq" column="verify_freq" jdbcType="VARCHAR"/>
        <result property="fileVerifyRqmt" column="file_verify_rqmt" jdbcType="VARCHAR"/>
        <result property="personSubmitRqmt" column="person_submit_rqmt" jdbcType="VARCHAR"/>
        <result property="noCrsubmitInfo" column="no_crsubmit_info" jdbcType="VARCHAR"/>
        <result property="projectApprovalTime" column="project_approval_time" jdbcType="VARCHAR"/>
        <result property="approvalFreqInfo" column="approval_freq_info" jdbcType="VARCHAR"/>
        <result property="projApprCharge" column="proj_appr_charge" jdbcType="VARCHAR"/>
        <result property="chargeStandard" column="charge_standard" jdbcType="VARCHAR"/>
        <result property="paymentImpact" column="payment_impact" jdbcType="VARCHAR"/>
        <result property="paymentMilestone" column="payment_milestone" jdbcType="VARCHAR"/>
        <result property="paymentMilestoneInfo" column="payment_milestone_info" jdbcType="VARCHAR"/>
        <result property="paymentNotes" column="payment_notes" jdbcType="VARCHAR"/>
        <result property="supplimentary" column="supplimentary" jdbcType="VARCHAR"/>
        <result property="appendix" column="appendix" jdbcType="VARCHAR"/>
        <result property="qualityControl" column="quality_control" jdbcType="VARCHAR"/>
        <result property="qualityControlAppendix" column="quality_control_appendix" jdbcType="VARCHAR"/>
        <result property="smo" column="smo" jdbcType="VARCHAR"/>
        <result property="perFeedbackLimitInfo" column="per_feedback_limit_info" jdbcType="VARCHAR"/>
        <result property="ethiauditWay" column="ethiaudit_way" jdbcType="VARCHAR"/>
        <result property="perFeedbackLimit" column="per_feedback_limit" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="selectCenterProjectApprovalVo">
        select  t.id  t.is_delete  t.delete_flag  t.modifiedByName  t.modifiedBy  t.createdByName  t.createdBy  t.dateModified  t.dateCreated  t.center_id  t.contact  t.new_research_order  t.new_research_way  t.new_researcher_rqmt  t.new_researcher_rqmt_info  t.simultaneously  t.fisrt_undertake_qual  t.pi_proj_num_limit  t.pi_proj_num_limit_info  t.proj_rqmt  t.proj_rqmt_info  t.smo_rqmt  t.smo_rqmt_appendix  t.smo_choose_rqmt  t.cra_rqmt  t.cra_rqmt_info  t.pre_requisites_info  t.project_approval_cycle  t.workflow  t.ethiaudit_order  t.is_group_leader_file  t.audit_requirements  t.down_file_url  t.pre_trial_way  t.verify_online_way  t.system_link  t.email_addr  t.verify_contact_way  t.email_reply_freq  t.file_send_rqmt  t.paper_submit_rqmt  t.audit_way  t.verify_freq  t.file_verify_rqmt  t.person_submit_rqmt  t.no_crsubmit_info  t.project_approval_time  t.approval_freq_info  t.proj_appr_charge  t.charge_standard  t.payment_impact  t.payment_milestone  t.payment_milestone_info  t.payment_notes  t.supplimentary  t.appendix  t.quality_control t.quality_control_appendix  t.smo  t.per_feedback_limit_info  t.ethiaudit_way  t.per_feedback_limit
        from t_center_project_approval t
    </sql>

</mapper>
