<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.cs.BiCenterDao">

    <resultMap type="com.r2.bi.entity.cs.Center" id="CenterMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="VARCHAR"/>
        <result property="datecreated" column="dateCreated" jdbcType="TIMESTAMP"/>
        <result property="datemodified" column="dateModified" jdbcType="TIMESTAMP"/>
        <result property="createdby" column="createdBy" jdbcType="VARCHAR"/>
        <result property="createdbyname" column="createdByName" jdbcType="VARCHAR"/>
        <result property="modifiedby" column="modifiedBy" jdbcType="VARCHAR"/>
        <result property="modifiedbyname" column="modifiedByName" jdbcType="VARCHAR"/>
        <result property="centerName" column="center_name" jdbcType="VARCHAR"/>
        <result property="standardCode" column="standard_code" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="class1" column="class1" jdbcType="VARCHAR"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="area" column="area" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="area" column="area" jdbcType="VARCHAR"/>
        <result property="crc" column="crc" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="tel" column="tel" jdbcType="VARCHAR"/>
        <result property="admittedRqmt" column="admitted_rqmt" jdbcType="VARCHAR"/>
        <result property="admittedRemark" column="admitted_remark" jdbcType="VARCHAR"/>
        <result property="alias" column="alias" jdbcType="VARCHAR"/>
        <result property="orgReceptionDate" column="org_reception_date" jdbcType="VARCHAR"/>
        <result property="ethicsReceptionDate" column="ethics_reception_date" jdbcType="VARCHAR"/>
        <result property="startupTime" column="startup_time" jdbcType="VARCHAR"/>
        <result property="zoneId" column="zone_id" jdbcType="VARCHAR"/>
        <result property="updator" column="updator" jdbcType="VARCHAR"/>
        <result property="aqNumber" column="aq_number" jdbcType="VARCHAR"/>
        <result property="aqStatus" column="aq_status" jdbcType="VARCHAR"/>
        <result property="fristAqTime" column="frist_aq_time" jdbcType="VARCHAR"/>
        <result property="aqTime" column="aq_time" jdbcType="VARCHAR"/>
        <result property="drugAqFirstNumber" column="drug_aq_first_number" jdbcType="VARCHAR"/>
        <result property="drugAqFirstTime" column="drug_aq_first_time" jdbcType="VARCHAR"/>
        <result property="drugAqNumber" column="drug_aq_number" jdbcType="VARCHAR"/>
        <result property="drugAqStatus" column="drug_aq_status" jdbcType="VARCHAR"/>
        <result property="drugAqTime" column="drug_aq_time" jdbcType="VARCHAR"/>
        <result property="drugAqContacts" column="drug_aq_contacts" jdbcType="VARCHAR"/>
        <result property="drugAqContactsPhone" column="drug_aq_contacts_phone" jdbcType="VARCHAR"/>
        <result property="drugAqMajor" column="drug_aq_major" jdbcType="VARCHAR"/>
        <result property="deviceAqNumber" column="device_aq_number" jdbcType="VARCHAR"/>
        <result property="deviceAqStatus" column="device_aq_status" jdbcType="VARCHAR"/>
        <result property="deviceAqTime" column="device_aq_time" jdbcType="VARCHAR"/>
        <result property="deviceAqContacts" column="device_aq_contacts" jdbcType="VARCHAR"/>
        <result property="deviceAqContactsPhone" column="device_aq_contacts_phone" jdbcType="VARCHAR"/>
        <result property="deviceAqMajor" column="device_aq_major" jdbcType="VARCHAR"/>
        <result property="haveSmo" column="have_smo" jdbcType="VARCHAR"/>
        <result property="linkstartSmo" column="linkstart_smo" jdbcType="VARCHAR"/>
        <result property="croCorpor" column="cro_corpor" jdbcType="VARCHAR"/>
        <result property="deptGrade" column="dept_grade" jdbcType="VARCHAR"/>
        <result property="orgGrade" column="org_grade" jdbcType="VARCHAR"/>
        <result property="ethicsGrade" column="ethics_grade" jdbcType="VARCHAR"/>
        <result property="processGrade" column="process_grade" jdbcType="VARCHAR"/>
        <result property="projectGrade" column="project_grade" jdbcType="VARCHAR"/>
        <result property="linkstartCorp" column="linkstart_corp" jdbcType="VARCHAR"/>
        <result property="linkstartCorpRemark" column="linkstart_corp_remark" jdbcType="VARCHAR"/>
        <result property="linkstartCover" column="linkstart_cover" jdbcType="VARCHAR"/>
        <result property="performInfo" column="perform_info" jdbcType="VARCHAR"/>
        <result property="performRemark" column="perform_remark" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="score" column="score" jdbcType="VARCHAR"/>
        <result property="workFlow" column="work_flow" jdbcType="VARCHAR"/>
        <result property="projectProgress" column="project_progress" jdbcType="VARCHAR"/>
        <result property="deptDegree" column="dept_degree" jdbcType="VARCHAR"/>
        <result property="ethicsDegree" column="ethics_degree" jdbcType="VARCHAR"/>
        <result property="orgDegree" column="org_degree" jdbcType="VARCHAR"/>
        <result property="admittedRqmtInfo" column="admitted_rqmt_info" jdbcType="VARCHAR"/>
        <result property="croZoneId" column="cro_zone_id" jdbcType="VARCHAR"/>
        <result property="croUpdator" column="cro_updator" jdbcType="VARCHAR"/>
        <result property="siteRecommend" column="site_recommend" jdbcType="VARCHAR"/>
        <result property="recommendReason" column="recommend_reason" jdbcType="VARCHAR"/>
        <result property="community" column="community" jdbcType="VARCHAR"/>
        <result property="clinicalProjects" column="clinical_projects" jdbcType="VARCHAR"/>
        <result property="smoAsk" column="smo_ask" jdbcType="VARCHAR"/>
        <result property="strategyCorpor" column="strategy_corpor" jdbcType="VARCHAR"/>
        <result property="personnelCoverage" column="personnel_coverage" jdbcType="VARCHAR"/>
        <result property="preferredTime" column="preferred_time" jdbcType="VARCHAR"/>
        <result property="selectionFile" column="selection_file" jdbcType="VARCHAR"/>
        <result property="smoSelectionAsk" column="smo_selection_ask" jdbcType="VARCHAR"/>
        <result property="sameGroup" column="same_group" jdbcType="VARCHAR"/>
        <result property="speedProcess" column="speed_process" jdbcType="VARCHAR"/>
        <result property="researchProcess" column="research_process" jdbcType="VARCHAR"/>
        <result property="requestResearchers" column="request_researchers" jdbcType="VARCHAR"/>
        <result property="researchMethods" column="research_methods" jdbcType="VARCHAR"/>
        <result property="researchMethodsSupplement" column="research_methods_supplement" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

