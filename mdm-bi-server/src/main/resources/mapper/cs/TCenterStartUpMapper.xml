<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.cs.TCenterStartUpMapper">

    <resultMap id="BaseResultMap" type="com.r2.bi.entity.cs.TCenterStartUp">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
            <result property="deleteFlag" column="delete_flag" jdbcType="VARCHAR"/>
            <result property="datecreated" column="dateCreated" jdbcType="TIMESTAMP"/>
            <result property="datemodified" column="dateModified" jdbcType="TIMESTAMP"/>
            <result property="createdby" column="createdBy" jdbcType="VARCHAR"/>
            <result property="createdbyname" column="createdByName" jdbcType="VARCHAR"/>
            <result property="modifiedby" column="modifiedBy" jdbcType="VARCHAR"/>
            <result property="modifiedbyname" column="modifiedByName" jdbcType="VARCHAR"/>
            <result property="centerId" column="center_id" jdbcType="BIGINT"/>
            <result property="kickoffRqmt" column="kickoff_rqmt" jdbcType="VARCHAR"/>
            <result property="kickoffAddlInfo" column="kickoff_addl_info" jdbcType="VARCHAR"/>
            <result property="centerRqmt" column="center_rqmt" jdbcType="VARCHAR"/>
            <result property="subjectPrecondition" column="subject_precondition" jdbcType="VARCHAR"/>
            <result property="safetyEthicsTemplate" column="safety_ethics_template" jdbcType="VARCHAR"/>
            <result property="safetyFreqRqmt" column="safety_freq_rqmt" jdbcType="VARCHAR"/>
            <result property="safetyReviewMethod" column="safety_review_method" jdbcType="VARCHAR"/>
            <result property="safetyReviewAddlInfo" column="safety_review_addl_info" jdbcType="VARCHAR"/>
            <result property="safetyJoint" column="safety_joint" jdbcType="VARCHAR"/>
            <result property="safetyFee" column="safety_fee" jdbcType="VARCHAR"/>
            <result property="safetyPaymentRemarks" column="safety_payment_remarks" jdbcType="VARCHAR"/>
            <result property="safetyPayLimit" column="safety_pay_limit" jdbcType="VARCHAR"/>
            <result property="safetyInvoice" column="safety_invoice" jdbcType="VARCHAR"/>
            <result property="safetySubmitTime" column="safety_submit_time" jdbcType="VARCHAR"/>
            <result property="safetySubmitor" column="safety_submitor" jdbcType="VARCHAR"/>
            <result property="safetyAddlInfo" column="safety_addl_info" jdbcType="VARCHAR"/>
            <result property="periodiethicsTemplate" column="periodiethics_template" jdbcType="VARCHAR"/>
            <result property="periodifreqRqmt" column="periodifreq_rqmt" jdbcType="VARCHAR"/>
            <result property="periodiconclusionRqmt" column="periodiconclusion_rqmt" jdbcType="VARCHAR"/>
            <result property="periodimaterialRqmt" column="periodimaterial_rqmt" jdbcType="VARCHAR"/>
            <result property="periodireviewMethod" column="periodireview_method" jdbcType="VARCHAR"/>
            <result property="periodireviewAddlInfo" column="periodireview_addl_info" jdbcType="VARCHAR"/>
            <result property="periodijoint" column="periodijoint" jdbcType="VARCHAR"/>
            <result property="periodifee" column="periodifee" jdbcType="VARCHAR"/>
            <result property="periodipaymentRemarks" column="periodipayment_remarks" jdbcType="VARCHAR"/>
            <result property="periodidefense" column="periodidefense" jdbcType="VARCHAR"/>
            <result property="periodidefenseRqmt" column="periodidefense_rqmt" jdbcType="VARCHAR"/>
            <result property="periodifeedback" column="periodifeedback" jdbcType="VARCHAR"/>
            <result property="periodipayLimit" column="periodipay_limit" jdbcType="VARCHAR"/>
            <result property="periodiinvoice" column="periodiinvoice" jdbcType="VARCHAR"/>
            <result property="periodisubmitTime" column="periodisubmit_time" jdbcType="VARCHAR"/>
            <result property="periodiaddlInfo" column="periodiaddl_info" jdbcType="VARCHAR"/>
            <result property="conclusionEthicsTemplate" column="conclusion_ethics_template" jdbcType="VARCHAR"/>
            <result property="conclusionFreqRqmt" column="conclusion_freq_rqmt" jdbcType="VARCHAR"/>
            <result property="conclusionConclusionRqmt" column="conclusion_conclusion_rqmt" jdbcType="VARCHAR"/>
            <result property="conclusionMaterialRqmt" column="conclusion_material_rqmt" jdbcType="VARCHAR"/>
            <result property="conclusionReviewMethod" column="conclusion_review_method" jdbcType="VARCHAR"/>
            <result property="conclusionReviewAddlInfo" column="conclusion_review_addl_info" jdbcType="VARCHAR"/>
            <result property="conclusionDefense" column="conclusion_defense" jdbcType="VARCHAR"/>
            <result property="conclusionDefenseRqmt" column="conclusion_defense_rqmt" jdbcType="VARCHAR"/>
            <result property="conclusionFeedback" column="conclusion_feedback" jdbcType="VARCHAR"/>
            <result property="conclusionSubmitTime" column="conclusion_submit_time" jdbcType="VARCHAR"/>
            <result property="conclusionAddlInfo" column="conclusion_addl_info" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,is_delete,delete_flag,
        dateCreated,dateModified,createdBy,
        createdByName,modifiedBy,modifiedByName,
        center_id,kickoff_rqmt,kickoff_addl_info,
        center_rqmt,subject_precondition,safety_ethics_template,
        safety_freq_rqmt,safety_review_method,safety_review_addl_info,
        safety_joint,safety_fee,safety_payment_remarks,
        safety_pay_limit,safety_invoice,safety_submit_time,
        safety_submitor,safety_addl_info,periodiethics_template,
        periodifreq_rqmt,periodiconclusion_rqmt,periodimaterial_rqmt,
        periodireview_method,periodireview_addl_info,periodijoint,
        periodifee,periodipayment_remarks,periodidefense,
        periodidefense_rqmt,periodifeedback,periodipay_limit,
        periodiinvoice,periodisubmit_time,periodiaddl_info,
        conclusion_ethics_template,conclusion_freq_rqmt,conclusion_conclusion_rqmt,
        conclusion_material_rqmt,conclusion_review_method,conclusion_review_addl_info,
        conclusion_defense,conclusion_defense_rqmt,conclusion_feedback,
        conclusion_submit_time,conclusion_addl_info
    </sql>
</mapper>
