<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.dc.CustomTableDataMapper">

    <resultMap id="BaseResultMap" type="com.r2.bi.entity.dc.CustomTableData">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="tableId" column="table_id" jdbcType="INTEGER"/>
            <result property="content" column="content" jdbcType="OTHER"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,table_id,content,status,remark,
        create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
