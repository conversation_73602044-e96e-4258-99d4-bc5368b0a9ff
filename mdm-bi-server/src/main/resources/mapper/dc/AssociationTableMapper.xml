<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.dc.AssociationTableMapper">

    <resultMap id="BaseResultMap" type="com.r2.bi.entity.dc.AssociationTable">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="systemSource" column="system_source" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="thirdPartyId" column="third_party_id" jdbcType="VARCHAR"/>
        <result property="thirdPartyCenterName" column="third_party_center_name" jdbcType="VARCHAR"/>
        <result property="thirdPartyAddress" column="third_party_address" jdbcType="VARCHAR"/>
        <result property="provinceAndCity" column="province_and_city" jdbcType="VARCHAR"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="VARCHAR"/>
        <result property="mdmId" column="mdm_id" jdbcType="VARCHAR"/>
        <result property="mdmCenterCode" column="mdm_center_code" jdbcType="VARCHAR"/>
        <result property="mdmCenterName" column="mdm_center_name" jdbcType="VARCHAR"/>
        <result property="mdmCenterAlias" column="mdm_center_alias" jdbcType="VARCHAR"/>
        <result property="mdmAddress" column="mdm_address" jdbcType="VARCHAR"/>
        <result property="thirdPartyIndicationId" column="third_party_indication_id" jdbcType="VARCHAR"/>
        <result property="thirdPartyIndication" column="third_party_indication" jdbcType="VARCHAR"/>
        <result property="mdmIndicationId" column="mdm_indication_id" jdbcType="VARCHAR"/>
        <result property="mdmIndicationZh" column="mdm_indication_zh" jdbcType="VARCHAR"/>
        <result property="mdmIndicationEn" column="mdm_indication_en" jdbcType="VARCHAR"/>
        <result property="mdmIndicationAbbr" column="mdm_indication_abbr" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,system_source,type,
        third_party_id,third_party_center_name,third_party_address,province_and_city,delete_flag,mdm_id,
        mdm_center_code,mdm_center_name,mdm_center_alias,
        mdm_address,third_party_indication_id,third_party_indication,
        mdm_indication_id,mdm_indication_zh,mdm_indication_en,
        mdm_indication_abbr,is_delete,create_by,
        create_time,update_by,update_time
    </sql>
</mapper>
