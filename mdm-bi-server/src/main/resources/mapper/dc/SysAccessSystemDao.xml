<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.dc.SysAccessSystemDao">


    <select id="totalCount" resultType="java.lang.Integer">
        select
        count(*)
        from sys_access_system su
        <where>
            su.is_delete = 0
            <if test="query.search != null and query.search != ''">
                and (su.name like CONCAT('%',#{query.search},'%') or su.name_en like CONCAT('%',#{query.search},'%') or su.code like CONCAT('%',#{query.search},'%') or su.alias like CONCAT('%',#{query.search},'%'))
            </if>
            <if test="query.status != null and query.status != ''">
                and su.status = #{query.status}
            </if>
            <if test="query.ids != null and query.ids.size > 0">
                and su.id  in
                <foreach collection="query.ids" open="(" close=")" separator="," item="id">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listByCond" resultType="com.r2.bi.vo.sysaccesssystem.AccessListRespVO">
        select distinct
        su.id,su.name,su.code,su.name_en,su.alias,su.dept,su.dept as deptStr,su.description,su.status,su.is_delete,su.create_time,su.update_time,su.create_by,su.update_by,su.type,su.tabulatio_name
        from sys_access_system su
        <where>
         su.is_delete = 0
            <if test="query.search != null and query.search != ''">
                and (su.name like CONCAT('%',#{query.search},'%') or su.name_en like CONCAT('%',#{query.search},'%') or su.code like CONCAT('%',#{query.search},'%') or su.alias like CONCAT('%',#{query.search},'%'))
            </if>
            <if test="query.status != null and query.status != ''">
                and su.status = #{query.status}
            </if>
            <if test="query.ids != null and query.ids.size > 0">
            and su.id  in
            <foreach collection="query.ids" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
            </if>
        </where>
        limit #{query.offset}, #{query.size}
    </select>


</mapper>

