<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.bi.SysOrganizeDao">
    <resultMap type="com.r2.bi.entity.bi.SysOrganize" id="SysOrgResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="organizeCode" column="organize_code" jdbcType="VARCHAR"/>
        <result property="organizeName" column="organize_name" jdbcType="VARCHAR"/>
        <result property="parentCode" column="parent_code" jdbcType="VARCHAR"/>
        <result property="secondLevelOrgCode" column="second_level_org_code" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="personInCharge" column="person_in_charge" jdbcType="BIGINT"/>
        <result property="updateDay" column="update_day" jdbcType="DATE"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <resultMap type="com.r2.bi.vo.sysorg.SysOrgTreeNodeRespVO" id="TreeSysOrgResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="organizeCode" column="organize_code" jdbcType="VARCHAR"/>
        <result property="organizeName" column="organize_name" jdbcType="VARCHAR"/>
        <result property="parentCode" column="parent_code" jdbcType="VARCHAR"/>

        <result property="secondLevelOrgCode" column="second_level_org_code" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="personInCharge" column="person_in_charge" jdbcType="INTEGER"/>
        <result property="updateDay" column="update_day" jdbcType="DATE"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>


    <sql id="selectOrgVo">
        select d.id, d.organize_code, d.organize_name, d.parent_code, d.second_level_org_code, d.status, d.person_in_charge, d.update_day, d.is_delete, d.create_time, d.update_time
        from t_sys_organize d
    </sql>


    <select id="selectTreeOrgList" parameterType="com.r2.bi.vo.sysorg.SysOrgListReqVO" resultMap="TreeSysOrgResult">
        <include refid="selectOrgVo"/>
        where d.is_delete = 0
        AND d.status = 1
        <if test="organize_name != null and organize_name != ''">
            AND d.organize_name like concat('%', #{organizeName}, '%')
        </if>
        order by d.id, d.create_time
    </select>

    <select id="selectOrgList" parameterType="com.r2.bi.vo.sysorg.SysOrgListReqVO" resultMap="SysOrgResult">
        <include refid="selectOrgVo"/>
        where d.is_delete = 0
        AND d.status = 1
        <if test="parent_code != null and parent_code != ''">
            AND d.parent_code = #{parent_code}
        </if>
        <if test="organize_name != null and organize_name != ''">
            AND d.organize_name like concat('%', #{organize_name}, '%')
        </if>
        <if test="person_in_charge != null and person_in_charge != 0">
            AND d.person_in_charge = #{person_in_charge}
        </if>
        order by d.id, d.create_time
    </select>
</mapper>

