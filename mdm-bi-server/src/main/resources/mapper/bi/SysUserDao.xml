<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.bi.BiSysUserDao">
    <resultMap type="com.r2.bi.entity.bi.BiUser" id="SysUserMap">
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="jobNumber" column="job_number" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="organizeCode" column="organize_code" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="dutyName" column="duty_name" jdbcType="VARCHAR"/>
        <result property="poiDempAdminName" column="poi_demp_admin_name" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="salt" column="salt" jdbcType="VARCHAR"/>
        <result property="userAccount" column="user_account" jdbcType="VARCHAR"/>
        <result property="searchKey" column="search_key" jdbcType="VARCHAR"/>
        <result property="resetDay" column="reset_day" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="base_col_list">
        user_id, job_number, mobile,username, organize_code,  poi_demp_admin_name,  email, duty_name, is_delete, create_time, update_time, password, salt, user_account, reset_day, searchKey
    </sql>

    <select id="findPage" resultType="com.r2.bi.bo.SysUserListBO">
        select
            u.id                     as id,
            u.user_id                as userId,
            u.job_number             as jobNumber,
            u.mobile                 as mobile,
            u.user_account           as userAccount,
            u.username               as username,
            group_concat(ur.role_id) as roleIds,
            u.email                  as email,
            u.duty_name              as dutyName,
            u.poi_demp_admin_name    as poiDempAdminName,
            u.organize_code          as organizeCode,
            u.create_time            as createTime,
            u.data_permission        as dataPermission,
            u.source_type            as sourceType
        from t_user u
                 left join t_sys_user_roles ur on ur.user_id = u.id
            ${ew.customSqlSegment}
    </select>



</mapper>

