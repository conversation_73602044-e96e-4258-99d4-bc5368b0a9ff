<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.r2.bi.dao.bi.TSysMenuDao">

    <resultMap type="com.r2.bi.entity.bi.SysMenu" id="SysMenuMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="pid" column="pid" jdbcType="INTEGER"/>
        <result property="subCount" column="sub_count" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="component" column="component" jdbcType="VARCHAR"/>
        <result property="menuSort" column="menu_sort" jdbcType="INTEGER"/>
        <result property="icon" column="icon" jdbcType="VARCHAR"/>
        <result property="permission" column="permission" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
</mapper>

