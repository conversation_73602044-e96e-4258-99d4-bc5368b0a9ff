<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.r2.framework</groupId>
        <artifactId>framework-starter</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <modules>
        <module>mdm-bi-server</module>
    </modules>
    <groupId>com.r2.bi</groupId>
    <artifactId>mdm-bi</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>mdm-bi</name>
    <packaging>pom</packaging>



    <dependencies>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
    </dependencies>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-cloud.version>Finchley.SR2</spring-cloud.version>
        <java.version>1.8</java.version>
        <start-class>com.r2.bi.Application</start-class>
        <docker.image.prefix>mdm-bi</docker.image.prefix>
        <spring-cloud-starter-bootstrap-version>3.0.2</spring-cloud-starter-bootstrap-version>
        <mybatis-plus-version>3.4.1</mybatis-plus-version>
        <velocity-engine-core-version>2.0</velocity-engine-core-version>
        <shiro-version>1.4.0</shiro-version>
        <orika-version>1.9.0</orika-version>
        <easypoi-version>4.0.0</easypoi-version>
        <org.apache.poi-version>4.0.0</org.apache.poi-version>
        <fr.opensagres.xdocreport-version>1.0.6</fr.opensagres.xdocreport-version>
        <commons-net-version>3.6</commons-net-version>
        <commons-pool2-version>2.7.0</commons-pool2-version>
        <easyexcel-version>3.0.5</easyexcel-version>
        <aliyun-version>4.3.5</aliyun-version>
        <!--        <alibaba-cloud.version>2.0.4.RELEASE</alibaba-cloud.version>-->
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <classifier>execute</classifier>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>nexus</id>
            <name>Nexus Release Repository</name>
            <url>http://192.168.0.30:8082/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://192.168.0.30:8082/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>


</project>